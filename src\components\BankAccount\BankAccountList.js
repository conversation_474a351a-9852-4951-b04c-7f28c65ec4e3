import React, { useState, useEffect } from 'react';
import { Table, Button, Space, Popconfirm, Typography, Input, Card, Tag } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, SearchOutlined } from '@ant-design/icons';
import { Toaster, toast } from 'react-hot-toast';
import { Link } from 'react-router-dom';
import BankAccountService from '../../services/bankAccount.service';

const { Title } = Typography;

const BankAccountList = () => {
  const [bankAccounts, setBankAccounts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');

  useEffect(() => {
    fetchBankAccounts();
  }, []);

  const fetchBankAccounts = async () => {
    setLoading(true);
    try {
      const response = await BankAccountService.getAllBankAccounts();
      setBankAccounts(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách tài khoản ngân hàng: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    try {
      await BankAccountService.deleteBankAccount(id);
      toast.success('Xóa tài khoản ngân hàng thành công');
      fetchBankAccounts();
    } catch (error) {
      toast.error('Không thể xóa tài khoản ngân hàng: ' + error.message);
    }
  };

  const filteredBankAccounts = bankAccounts.filter(
    (account) =>
      account.accountNumber?.includes(searchText) ||
      account.bankName?.toLowerCase().includes(searchText.toLowerCase()) ||
      account.accountHolder?.toLowerCase().includes(searchText.toLowerCase())
  );

  const columns = [
    {
      title: <span style={{ fontSize: 16 }}>Số tài khoản</span>,
      dataIndex: 'accountNumber',
      key: 'accountNumber',
    },
    {
      title: <span style={{ fontSize: 16 }}>Ngân hàng</span>,
      dataIndex: 'bankName',
      key: 'bankName',
      sorter: (a, b) => a.bankName.localeCompare(b.bankName),
    },
    {
      title: <span style={{ fontSize: 16 }}>Chủ tài khoản</span>,
      dataIndex: 'accountHolder',
      key: 'accountHolder',
    },
    {
      title: <span style={{ fontSize: 16 }}>Chi nhánh</span>,
      dataIndex: 'branch',
      key: 'branch',
    },
    {
      title: <span style={{ fontSize: 16 }}>Loại</span>,
      dataIndex: 'type',
      key: 'type',
      render: (type) => {
        let text = 'Công ty';
        let color = 'blue';
        
        if (type === 'personal') {
          text = 'Cá nhân';
          color = 'green';
        }
        
        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: <span style={{ fontSize: 16 }}>Trạng thái</span>,
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? 'Hoạt động' : 'Không hoạt động'}
        </Tag>
      ),
    },
    {
      title: <span style={{ fontSize: 16 }}>Thao tác</span>,
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Link to={`/bank-accounts/edit/${record.id}`}>
            <Button type="primary" icon={<EditOutlined />} size="small" style={{ height: 40, fontSize: 14, display: 'flex', alignItems: 'center', padding: '0 15px' }}>
              Sửa
            </Button>
          </Link>
          <Popconfirm
            title="Bạn có chắc chắn muốn xóa tài khoản ngân hàng này?"
            onConfirm={() => handleDelete(record.id)}
            okText="Có"
            cancelText="Không"
          >
            <Button type="primary" danger icon={<DeleteOutlined />} size="small" style={{ height: 40, fontSize: 14, display: 'flex', alignItems: 'center', padding: '0 15px' }}>
              Xóa
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Toaster position="top-right" />
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 20 }}>
        <Title level={2} style={{ fontSize: 28, background: 'linear-gradient(90deg, #1890ff, #096dd9)', WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent' }}>Quản lý tài khoản ngân hàng</Title>
        <Link to="/bank-accounts/add">
          <Button type="primary" icon={<PlusOutlined />} style={{ height: 40, fontSize: 16, display: 'flex', alignItems: 'center' }}>
            Thêm tài khoản
          </Button>
        </Link>
      </div>

      <Card style={{ marginBottom: 16 }}>
        <Input
          placeholder="Tìm kiếm tài khoản ngân hàng..."
          prefix={<SearchOutlined />}
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          style={{ width: 300, height: 42, fontSize: 16 }}
        />
      </Card>

      <Table
        columns={columns}
        dataSource={filteredBankAccounts}
        rowKey="id"
        loading={loading}
        pagination={{ pageSize: 10, style: { fontSize: 16 } }}
        style={{ fontSize: 16 }}
      />
    </div>
  );
};

export default BankAccountList;
