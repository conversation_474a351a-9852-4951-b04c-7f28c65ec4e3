/* Login page styles with red and dark green theme */
:root {
  --primary-red: #d32f2f;
  --primary-green: #1b5e20;
  --light-red: #ff6659;
  --light-green: #4c8c4a;
  --dark-red: #9a0007;
  --dark-green: #003300;
  --white: #ffffff;
  --light-gray: #f5f5f5;
  --dark-gray: #333333;
}

.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, var(--light-gray) 0%, var(--white) 100%);
  position: relative;
  overflow: hidden;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.login-shape {
  position: absolute;
  border-radius: 50%;
  opacity: 0.6;
}

.shape-1 {
  width: 300px;
  height: 300px;
  background: var(--primary-red);
  top: -100px;
  right: -50px;
  animation: float 8s ease-in-out infinite;
}

.shape-2 {
  width: 200px;
  height: 200px;
  background: var(--primary-green);
  bottom: -50px;
  left: -50px;
  animation: float 6s ease-in-out infinite reverse;
}

.shape-3 {
  width: 150px;
  height: 150px;
  background: var(--light-red);
  top: 50%;
  left: 15%;
  animation: float 10s ease-in-out infinite;
}

@keyframes float {
  0% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(5deg);
  }
  100% {
    transform: translateY(0) rotate(0deg);
  }
}

.login-card-container {
  position: relative;
  z-index: 1;
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.5s ease, transform 0.5s ease;
}

.login-card-container.animate {
  opacity: 1;
  transform: translateY(0);
}

.login-card {
  width: 420px;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-logo {
  text-align: center;
  margin-bottom: 20px;
}

.login-icon {
  font-size: 48px;
  color: var(--primary-red);
  background: linear-gradient(135deg, var(--primary-red), var(--primary-green));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-title {
  margin-bottom: 8px !important;
  color: var(--primary-red) !important;
  font-weight: 700 !important;
  letter-spacing: 1px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.login-subtitle {
  color: var(--dark-gray);
  font-size: 16px;
  display: block;
}

.login-alert {
  margin-bottom: 20px;
}

.login-form {
  margin-bottom: 20px;
}

.interface-type-selector {
  margin-bottom: 24px;
}

.hidden-radio-group {
  display: none;
}

.interface-options {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin: 0 auto;
  max-width: 300px;
}

.interface-option {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px 10px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid #e8e8e8;
  background-color: #f9f9f9;
}

.interface-option:hover {
  border-color: var(--primary-red);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.interface-option.active {
  border-color: var(--primary-green);
  background-color: rgba(27, 94, 32, 0.05);
  box-shadow: 0 4px 12px rgba(27, 94, 32, 0.2);
}

.interface-icon {
  font-size: 28px;
  margin-bottom: 8px;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.management-icon {
  color: var(--primary-green);
  background-color: rgba(27, 94, 32, 0.1);
}

.sales-icon {
  color: var(--primary-red);
  background-color: rgba(211, 47, 47, 0.1);
}

.interface-option.active .management-icon {
  background-color: var(--primary-green);
  color: white;
}

.interface-option.active .sales-icon {
  background-color: var(--primary-red);
  color: white;
}

.interface-label {
  font-weight: 600;
  font-size: 16px;
  margin-top: 5px;
}

.login-input {
  border-radius: 6px;
  border: 1px solid #e8e8e8;
  transition: all 0.3s;
}

.login-input:hover {
  border-color: var(--primary-red);
}

.login-input:focus,
.login-input-focused {
  border-color: var(--primary-green) !important;
  box-shadow: 0 0 0 2px rgba(27, 94, 32, 0.2) !important;
}

.login-input-icon {
  color: #bfbfbf;
}

.login-button {
  height: 45px;
  border-radius: 6px;
  font-weight: 600;
  font-size: 16px;
  letter-spacing: 0.5px;
  background: linear-gradient(135deg, var(--primary-red), var(--primary-green)) !important;
  border: none !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s !important;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
  background: linear-gradient(135deg, var(--light-red), var(--light-green)) !important;
}

.login-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, var(--dark-red), var(--dark-green)) !important;
}

.login-footer {
  text-align: center;
  margin-top: 20px;
}

.login-copyright {
  color: var(--dark-gray);
  font-size: 14px;
  opacity: 0.7;
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .login-card {
    width: 90%;
    padding: 20px;
  }

  .login-icon {
    font-size: 40px;
  }

  .login-title {
    font-size: 24px !important;
  }

  .login-subtitle {
    font-size: 14px;
  }
}
