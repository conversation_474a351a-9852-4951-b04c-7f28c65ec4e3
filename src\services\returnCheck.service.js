import api from './api';

/**
 * Service để kiểm tra và quản lý số lượng sản phẩm đã trả trong hóa đơn
 */
const ReturnCheckService = {
  /**
   * Lấy tổng số lượng sản phẩm đã trả theo hóa đơn và sản phẩm
   * @param {number} invoiceId - ID của hóa đơn
   * @returns {Promise} - Promise chứa thông tin số lượng đã trả của từng sản phẩm
   */
  getReturnedQuantities: async (invoiceId) => {
    try {
      
      // Lấy tất cả các phiếu trả hàng của hóa đơn này (không lọc theo trạng thái)
      // Sử dụng include để lấy luôn các items trong một lần gọi API
      // Thêm tham số filter để chỉ lấy các phiếu trả hàng của hóa đơn hiện tại
      const response = await api.get(`/returns?invoiceId=${invoiceId}&include=ReturnItems,items`);
      const allReturns = response.data;
      
      // Lọc các phiếu trả hàng chỉ thuộc về hóa đơn hiện tại
      const returnsForThisInvoice = allReturns.filter(returnDoc => {
        // Kiểm tra xem phiếu trả hàng có thuộc về hóa đơn hiện tại không
        return returnDoc.invoiceId === invoiceId || returnDoc.invoiceId === parseInt(invoiceId);
      });
     
      // Nếu không có thông tin chi tiết về các sản phẩm trong phiếu trả hàng, lấy thêm thông tin chi tiết
      const detailedReturns = [];
      
      // Lấy thông tin chi tiết của từng phiếu trả hàng
      for (const returnDoc of returnsForThisInvoice) {
        if (!returnDoc.ReturnItems || returnDoc.ReturnItems.length === 0) {
          try {
            const detailResponse = await api.get(`/returns/${returnDoc.id}?include=ReturnItems,items`);
            const detailedReturn = detailResponse.data;
            
            // Kiểm tra lại xem phiếu trả hàng chi tiết có thuộc về hóa đơn hiện tại không
            if (detailedReturn && (detailedReturn.invoiceId === invoiceId || detailedReturn.invoiceId === parseInt(invoiceId))) {
              detailedReturns.push(detailedReturn);
            } else {
              console.log(`Bỏ qua phiếu trả hàng ID: ${returnDoc.id} vì không thuộc hóa đơn ${invoiceId}`);
            }
          } catch (detailError) {
            console.error(`Lỗi khi lấy thông tin chi tiết của phiếu trả hàng ID: ${returnDoc.id}`, detailError);
            // Vẫn giữ lại phiếu trả hàng gốc nếu không lấy được thông tin chi tiết
            detailedReturns.push(returnDoc);
          }
        } else {
          // Nếu đã có thông tin chi tiết, sử dụng trực tiếp
          detailedReturns.push(returnDoc);
        }
      }
      
      // Sử dụng danh sách phiếu trả hàng đã có thông tin chi tiết
      const allReturnsWithDetails = detailedReturns;
      
      // Nếu không có phiếu trả hàng nào, trả về đối tượng rỗng
      if (!allReturnsWithDetails || allReturnsWithDetails.length === 0) {
        return { returnedItems: {}, totalReturnedAmount: 0 };
      }
      
      // Khởi tạo đối tượng để lưu số lượng đã trả của từng sản phẩm
      let returnedItems = {};
      let totalReturnedAmount = 0;
      
      // Duyệt qua từng phiếu trả hàng
      for (const returnDoc of allReturnsWithDetails) {
        // Kiểm tra lại một lần nữa xem phiếu trả hàng có thuộc về hóa đơn hiện tại không
        if (returnDoc.invoiceId !== invoiceId && returnDoc.invoiceId !== parseInt(invoiceId)) {
          continue;
        }
        
        // Chỉ tính các phiếu trả hàng đã hoàn thành
        if (returnDoc.status === 'completed') {
          // Lấy danh sách sản phẩm từ phiếu trả hàng
          let returnItems = returnDoc.ReturnItems || returnDoc.items || [];
           
          if (returnItems.length === 0) {
            
            // Kiểm tra xem có thuộc tính items không
            if (returnDoc.items && returnDoc.items.length > 0) {
              returnItems = returnDoc.items;
            } else if (returnDoc.data && returnDoc.data.items && returnDoc.data.items.length > 0) {
              returnItems = returnDoc.data.items;
            }
          }
          
          // Cộng dồn số lượng sản phẩm đã trả
          returnItems.forEach(item => {
            const productId = item.productId;
            const quantity = parseInt(item.quantity) || 0;
            
            if (!returnedItems[productId]) {
              returnedItems[productId] = 0;
            }
            
            returnedItems[productId] += quantity;
          });
          
          // Cộng dồn tổng tiền trả hàng
          totalReturnedAmount += parseFloat(returnDoc.totalAmount) || 0;
        } else {
          console.log(`Bỏ qua phiếu trả hàng ID: ${returnDoc.id} vì trạng thái: ${returnDoc.status}`);
        }
      }
      
      // Format tổng tiền trả hàng thành chuỗi có định dạng
      const formattedTotalReturnedAmount = totalReturnedAmount.toFixed(2).padStart(12, '0');
      
      return { returnedItems, totalReturnedAmount: formattedTotalReturnedAmount };
    } catch (error) {
      console.error('Lỗi khi lấy thông tin số lượng đã trả:', error);
      throw error;
    }
  },
  
  /**
   * Kiểm tra xem số lượng trả hàng có hợp lệ không
   * @param {number} invoiceId - ID của hóa đơn
   * @param {Array} invoiceItems - Danh sách sản phẩm trong hóa đơn
   * @param {Array} returnItems - Danh sách sản phẩm muốn trả
   * @returns {Promise} - Promise chứa kết quả kiểm tra
   */
  validateReturnQuantities: async (invoiceId, invoiceItems, returnItems) => {
    try {
      // Lấy số lượng đã trả của từng sản phẩm
      const { returnedItems } = await ReturnCheckService.getReturnedQuantities(invoiceId);
      
      // Tạo map để tra cứu nhanh số lượng sản phẩm trong hóa đơn
      const invoiceItemMap = {};
      invoiceItems.forEach(item => {
        invoiceItemMap[item.productId] = item.quantity;
      });
      
      // Kiểm tra từng sản phẩm trong danh sách trả hàng
      const invalidItems = [];
      
      returnItems.forEach(item => {
        const productId = item.productId;
        const returnQuantity = item.quantity;
        const invoiceQuantity = invoiceItemMap[productId] || 0;
        const alreadyReturnedQuantity = returnedItems[productId] || 0;
        const remainingQuantity = invoiceQuantity - alreadyReturnedQuantity;
        
        // Nếu số lượng trả vượt quá số lượng còn lại, thêm vào danh sách không hợp lệ
        if (returnQuantity > remainingQuantity) {
          invalidItems.push({
            productId,
            productName: item.productName,
            requestedQuantity: returnQuantity,
            remainingQuantity,
            invoiceQuantity,
            alreadyReturnedQuantity
          });
        }
      });
      
      return {
        isValid: invalidItems.length === 0,
        invalidItems,
        returnedItems
      };
    } catch (error) {
      console.error('Lỗi khi kiểm tra số lượng trả hàng:', error);
      throw error;
    }
  }
};

export default ReturnCheckService;
