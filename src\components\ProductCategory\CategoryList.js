import React, { useState, useEffect } from 'react';
import { Table, Button, Space, Popconfirm, Typography, Input, Card, Tag } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, SearchOutlined, MinusCircleOutlined } from '@ant-design/icons';
import { Link } from 'react-router-dom';
import { Toaster, toast } from 'react-hot-toast';
import ProductCategoryService from '../../services/productCategory.service';

const { Title } = Typography;

const CategoryList = () => {
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [parentCategoryMap, setParentCategoryMap] = useState({});

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    setLoading(true);
    try {
      const response = await ProductCategoryService.getAllCategories();
      const categoriesData = response.data;

      // Tạo map để tra cứu nhanh các nhóm cha
      const parentMap = {};
      categoriesData.forEach(cat => {
        if (cat.parentId) {
          const parent = categoriesData.find(p => p.id === cat.parentId);
          if (parent) {
            parentMap[cat.id] = parent;
          }
        }
      });

      setParentCategoryMap(parentMap);
      setCategories(categoriesData);
    } catch (error) {
      toast.error('Không thể tải danh sách nhóm hàng: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    try {
      await ProductCategoryService.deleteCategory(id);
      toast.success('Xóa nhóm hàng thành công');
      fetchCategories();
    } catch (error) {
      toast.error('Không thể xóa nhóm hàng: ' + error.message);
    }
  };

  // Lọc ra chỉ các nhóm cha (không có parentId)
  const rootCategories = categories.filter(cat => !cat.parentId)
    .sort((a, b) => a.name.localeCompare(b.name));

  // Lọc theo từ khóa tìm kiếm
  const filteredCategories = rootCategories.filter(
    (category) =>
      category.name?.toLowerCase().includes(searchText.toLowerCase()) ||
      category.description?.toLowerCase().includes(searchText.toLowerCase())
  );

  const columns = [
    {
      title: <span style={{ fontSize: '16px', fontWeight: 'bold' }}>Tên nhóm hàng</span>,
      dataIndex: 'name',
      key: 'name',
      sorter: (a, b) => a.name.localeCompare(b.name),
      render: (text, record) => {
        // Tất cả đều là nhóm cha trong danh sách chính
        return <strong>{text}</strong>;
      }
    },
    {
      title: <span style={{ fontSize: '16px', fontWeight: 'bold' }}>Nhóm cha</span>,
      dataIndex: 'parentId',
      key: 'parentId',
      render: (parentId, record) => {
        if (!parentId) return <span>-</span>;
        const parentCategory = categories.find(cat => cat.id === parentId);
        return parentCategory ? parentCategory.name : '-';
      }
    },
    {
      title: <span style={{ fontSize: '16px', fontWeight: 'bold' }}>Mô tả</span>,
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: <span style={{ fontSize: '16px', fontWeight: 'bold' }}>Trạng thái</span>,
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? 'Hoạt động' : 'Không hoạt động'}
        </Tag>
      ),
    },
    {
      title: <span style={{ fontSize: '16px', fontWeight: 'bold' }}>Thao tác</span>,
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Link to={`/product-categories/edit/${record.id}`}>
            <Button type="primary" icon={<EditOutlined />} size="small" style={{ height: '40px', fontSize: '16px', display: 'flex', alignItems: 'center', padding: '0 15px' }}>
              Sửa
            </Button>
          </Link>
          <Popconfirm
            title="Bạn có chắc chắn muốn xóa nhóm hàng này?"
            onConfirm={() => handleDelete(record.id)}
            okText="Có"
            cancelText="Không"
          >
            <Button type="primary" danger icon={<DeleteOutlined />} size="small" style={{ height: '40px', fontSize: '16px', display: 'flex', alignItems: 'center', padding: '0 15px' }}>
              Xóa
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Toaster position="top-right" />
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
        <Title level={2} style={{ fontSize: 28, background: 'linear-gradient(90deg, #1890ff, #096dd9)', WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent' }}>Danh sách nhóm hàng</Title>
        <Link to="/product-categories/add">
          <Button
            type="primary"
            icon={<PlusOutlined />}
            style={{ height: '40px', fontSize: '16px', display: 'flex', alignItems: 'center' }}
          >
            Thêm nhóm hàng mới
          </Button>
        </Link>
      </div>

      <Card style={{ marginBottom: 16 }}>
        <Input
          placeholder="Tìm kiếm nhóm hàng..."
          style={{ width: 300, height: '40px', fontSize: '16px' }}
          prefix={<SearchOutlined />}
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
        />
      </Card>

      <Table
        columns={columns}
        dataSource={filteredCategories}
        style={{ fontSize: '16px' }}
        rowKey="id"
        loading={loading}
        pagination={{ pageSize: 20, style: { fontSize: '16px' } }}
        expandable={{
          childrenColumnName: 'none', // Không sử dụng cấu trúc children mặc định
          expandedRowRender: record => {
            // Tìm các nhóm con của nhóm hiện tại
            const children = categories.filter(cat => cat.parentId === record.id);
            if (children.length === 0) return null;

            // Tạo columns mới cho nhóm con
            const childColumns = [
              {
                title: <span style={{ fontSize: '16px', fontWeight: 'bold' }}>Tên nhóm hàng</span>,
                dataIndex: 'name',
                key: 'name',
                render: (text) => (
                  <span style={{ paddingLeft: '20px' }}>
                    ↳ {text}
                  </span>
                )
              },
              {
                title: <span style={{ fontSize: '16px', fontWeight: 'bold' }}>Mô tả</span>,
                dataIndex: 'description',
                key: 'description',
              },
              {
                title: <span style={{ fontSize: '16px', fontWeight: 'bold' }}>Trạng thái</span>,
                dataIndex: 'isActive',
                key: 'isActive',
                render: (isActive) => (
                  <Tag color={isActive ? 'green' : 'red'}>
                    {isActive ? 'Hoạt động' : 'Không hoạt động'}
                  </Tag>
                ),
              },
              {
                title: <span style={{ fontSize: '16px', fontWeight: 'bold' }}>Thao tác</span>,
                key: 'action',
                render: (_, record) => (
                  <Space size="middle">
                    <Link to={`/product-categories/edit/${record.id}`}>
                      <Button type="primary" icon={<EditOutlined />} size="small" style={{ height: '40px', fontSize: '16px', display: 'flex', alignItems: 'center', padding: '0 15px' }}>
                        Sửa
                      </Button>
                    </Link>
                    <Popconfirm
                      title="Bạn có chắc chắn muốn xóa nhóm hàng này?"
                      onConfirm={() => handleDelete(record.id)}
                      okText="Có"
                      cancelText="Không"
                    >
                      <Button type="primary" danger icon={<DeleteOutlined />} size="small" style={{ height: '40px', fontSize: '16px', display: 'flex', alignItems: 'center', padding: '0 15px' }}>
                        Xóa
                      </Button>
                    </Popconfirm>
                  </Space>
                ),
              },
            ];

            return (
              <Table
                columns={childColumns}
                dataSource={children}
                style={{ fontSize: '16px' }}
                rowKey="id"
                pagination={false}
                showHeader={false}
              />
            );
          },
          expandRowByClick: true,
          expandIcon: ({ expanded, onExpand, record }) => {
            // Chỉ hiển thị icon expand nếu có nhóm con
            const hasChildren = categories.some(cat => cat.parentId === record.id);
            if (!hasChildren) return null;

            return expanded ? (
              <Button type="text" icon={<MinusCircleOutlined />} onClick={e => onExpand(record, e)} />
            ) : (
              <Button type="text" icon={<PlusOutlined />} onClick={e => onExpand(record, e)} />
            );
          }
        }}
      />
    </div>
  );
};

export default CategoryList;
