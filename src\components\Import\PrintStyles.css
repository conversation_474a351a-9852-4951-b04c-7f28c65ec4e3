/* Styles for printing */
@media print {
  /* <PERSON><PERSON><PERSON><PERSON> lập khổ giấy ngang (landscape) với !important để ưu tiên cao nhất */
  @page {
    size: A4 landscape !important;
    margin: 0.5cm !important;
  }

  /* Force landscape mode cho mọi trình duyệt */
  html, body {
    width: 297mm !important; /* Chiều rộng A4 ngang */
    height: 210mm !important; /* Chiều cao A4 ngang */
    margin: 0 !important;
    padding: 0 !important;
    overflow: visible !important;
    font-size: 14px !important;
    font-family: 'Segoe UI', Arial, sans-serif !important;
  }

  /* Ẩn các phần tử không cần in */
  .no-print,
  .ant-btn,
  .ant-card-head {
    display: none !important;
  }

  /* Đảm bảo container phù hợp với khổ giấy ngang */
  .print-container {
    width: 100% !important;
    margin: 0 !important;
    padding: 10px !important;
    box-shadow: none !important;
    border: none !important;
    transform: rotate(0deg) !important; /* <PERSON><PERSON><PERSON> tr<PERSON><PERSON> duyệt tự xoay */
  }

  /* <PERSON><PERSON><PERSON><PERSON> padding của card để tận dụng không gian */
  .ant-card-body {
    padding: 10px !important;
  }

  /* Điều chỉnh kích thước tiêu đề và header */
  h3.ant-typography {
    margin-top: 10px !important;
    margin-bottom: 10px !important;
    font-size: 22px !important;
    text-transform: uppercase !important;
    font-weight: bold !important;
  }

  .print-header p {
    margin: 4px 0 !important;
    font-size: 13px !important;
  }

  /* Điều chỉnh Descriptions để hiển thị đẹp hơn trên khổ ngang */
  .ant-descriptions {
    margin-bottom: 10px !important;
    width: 100% !important;
    table-layout: fixed !important;
  }

  .ant-descriptions-bordered .ant-descriptions-item-label,
  .ant-descriptions-bordered .ant-descriptions-item-content {
    padding: 8px 10px !important;
    border: 1px solid #d9d9d9 !important;
    font-size: 13px !important;
  }

  /* Điều chỉnh bảng để hiển thị tốt trên khổ ngang */
  .ant-table {
    border-collapse: collapse !important;
    width: 100% !important;
    table-layout: fixed !important;
  }

  .ant-table-thead > tr > th,
  .ant-table-tbody > tr > td {
    padding: 8px 10px !important;
    border: 1px solid #d9d9d9 !important;
    word-break: normal !important;
    font-size: 13px !important;
  }

  /* Đảm bảo độ rộng cột đầu tiên */
  .ant-table-thead > tr > th:first-child,
  .ant-table-tbody > tr > td:first-child {
    width: 40px !important;
  }

  /* Sửa dòng tổng kết bảng */
  .ant-table-summary {
    background-color: #fafafa !important;
  }

  .ant-table-summary-row td {
    padding: 8px 10px !important;
    border: 1px solid #d9d9d9 !important;
    font-size: 13px !important;
    font-weight: bold !important;
  }

  /* Điều chỉnh divider */
  .ant-divider {
    margin: 15px 0 !important;
    border-top: 1px solid #d9d9d9 !important;
  }

  /* Phần chữ ký */
  .print-signatures {
    display: flex !important;
    justify-content: space-between !important;
    margin-top: 25px !important;
    margin-bottom: 15px !important;
    page-break-inside: avoid !important;
  }

  .signature-block {
    text-align: center !important;
    width: 30% !important;
    height: 100px !important;
  }

  .signature-block p {
    margin: 4px 0 !important;
    font-size: 13px !important;
    font-weight: bold !important;
  }

  /* Ngăn ngắt trang trong các phần tử quan trọng */
  .ant-table,
  .ant-descriptions,
  .print-signatures {
    page-break-inside: avoid !important;
  }

  /* Hiển thị tag đúng */
  .ant-tag {
    display: inline-block !important;
    padding: 3px 8px !important;
    border: 1px solid #d9d9d9 !important;
    border-radius: 3px !important;
    font-size: 13px !important;
    font-weight: bold !important;
  }

  /* Màu sắc cho tag */
  .ant-tag-green {
    color: #52c41a !important;
    background: #f6ffed !important;
    border-color: #b7eb8f !important;
  }

  .ant-tag-orange {
    color: #fa8c16 !important;
    background: #fff7e6 !important;
    border-color: #ffd591 !important;
  }

  .ant-tag-red {
    color: #f5222d !important;
    background: #fff1f0 !important;
    border-color: #ffa39e !important;
  }

  /* Đảm bảo hiển thị tất cả nội dung */
  * {
    overflow: visible !important;
  }
}

/* Styles cho giao diện trên màn hình */
.print-container {
  padding: 20px;
  background: white;
}

/* Điều chỉnh xem trước in - quan trọng để khớp với in thực tế */
@media screen and (prefers-reduced-motion: no-preference) {
  .print-container {
    max-width: 100%;
  }

  .print-table {
    width: 100%;
  }
}