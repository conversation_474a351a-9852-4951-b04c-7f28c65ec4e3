import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Card, Switch, Select, Typography, Divider } from 'antd';
import { useNavigate, useParams } from 'react-router-dom';
import { Toaster, toast } from 'react-hot-toast';
import SupplierService from '../../services/supplier.service';
import BranchService from '../../services/branch.service';

const { Title } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const SupplierForm = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [branches, setBranches] = useState([]);
  const navigate = useNavigate();
  const { id } = useParams();

  useEffect(() => {
    fetchBranches();
    
    if (id) {
      setIsEdit(true);
      fetchSupplierData();
    }
  }, [id]);

  const fetchBranches = async () => {
    try {
      const response = await BranchService.getAllBranches();
      setBranches(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách chi nhánh: ' + error.message);
    }
  };

  const fetchSupplierData = async () => {
    setLoading(true);
    try {
      const response = await SupplierService.getSupplierById(id);
      const supplierData = response.data;
      form.setFieldsValue({
        supplierCode: supplierData.supplierCode,
        name: supplierData.name,
        phone: supplierData.phone,
        address: supplierData.address,
        area: supplierData.area,
        ward: supplierData.ward,
        branchId: supplierData.branchId,
        taxCode: supplierData.taxCode,
        email: supplierData.email,
        contactPerson: supplierData.contactPerson,
        note: supplierData.note,
        isActive: supplierData.isActive,
      });
    } catch (error) {
      toast.error('Không thể tải thông tin nhà cung cấp: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const onFinish = async (values) => {
    setLoading(true);
    try {
      if (isEdit) {
        await SupplierService.updateSupplier(id, values);
        toast.success('Cập nhật nhà cung cấp thành công');
      } else {
        await SupplierService.createSupplier(values);
        toast.success('Thêm nhà cung cấp thành công');
      }
      navigate('/suppliers');
    } catch (error) {
      toast.error('Lỗi: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <Toaster position="top-right" />
      <Title level={2}>{isEdit ? 'Cập nhật nhà cung cấp' : 'Thêm nhà cung cấp mới'}</Title>
      <Divider />

      <Card>
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
        >
          <Form.Item
            name="supplierCode"
            label="Mã nhà cung cấp"
            rules={[{ required: true, message: 'Vui lòng nhập mã nhà cung cấp' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="name"
            label="Tên nhà cung cấp"
            rules={[{ required: true, message: 'Vui lòng nhập tên nhà cung cấp' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="contactPerson"
            label="Người liên hệ"
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="phone"
            label="Số điện thoại"
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="email"
            label="Email"
            rules={[
              { type: 'email', message: 'Email không hợp lệ' },
            ]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="address"
            label="Địa chỉ"
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="area"
            label="Khu vực"
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="ward"
            label="Phường xã"
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="taxCode"
            label="Mã số thuế"
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="branchId"
            label="Chi nhánh"
          >
            <Select placeholder="Chọn chi nhánh" allowClear>
              {branches.map(branch => (
                <Option key={branch.id} value={branch.id}>{branch.name}</Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="note"
            label="Ghi chú"
          >
            <TextArea rows={4} />
          </Form.Item>

          <Form.Item
            name="isActive"
            label="Trạng thái"
            valuePropName="checked"
          >
            <Switch checkedChildren="Hoạt động" unCheckedChildren="Không hoạt động" defaultChecked />
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading}>
              {isEdit ? 'Cập nhật' : 'Thêm mới'}
            </Button>
            <Button style={{ marginLeft: 8 }} onClick={() => navigate('/suppliers')}>
              Hủy
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default SupplierForm;
