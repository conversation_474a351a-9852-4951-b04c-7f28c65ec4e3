import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Card, Switch, Typography, Divider } from 'antd';
import { useNavigate, useParams } from 'react-router-dom';
import { Toaster, toast } from 'react-hot-toast';
import PayerService from '../../services/payer.service';

const { Title } = Typography;
const { TextArea } = Input;

const PayerForm = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const navigate = useNavigate();
  const { id } = useParams();

  useEffect(() => {
    if (id) {
      setIsEdit(true);
      fetchPayerData();
    }
  }, [id]);

  const fetchPayerData = async () => {
    setLoading(true);
    try {
      const response = await PayerService.getPayerById(id);
      const payerData = response.data;
      form.setFieldsValue({
        name: payerData.name,
        phone: payerData.phone,
        address: payerData.address,
        area: payerData.area,
        ward: payerData.ward,
        email: payerData.email,
        note: payerData.note,
        isActive: payerData.isActive,
      });
    } catch (error) {
      toast.error('Không thể tải thông tin người nộp: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const onFinish = async (values) => {
    setLoading(true);
    try {
      if (isEdit) {
        await PayerService.updatePayer(id, values);
        toast.success('Cập nhật người nộp thành công');
      } else {
        await PayerService.createPayer(values);
        toast.success('Thêm người nộp thành công');
      }
      navigate('/payers');
    } catch (error) {
      toast.error('Lỗi: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <Toaster position="top-right" />
      <Title level={2}>{isEdit ? 'Cập nhật người nộp' : 'Thêm người nộp mới'}</Title>
      <Divider />

      <Card>
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          initialValues={{ isActive: true }}
        >
          <Form.Item
            name="name"
            label="Tên người nộp"
            rules={[{ required: true, message: 'Vui lòng nhập tên người nộp' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="phone"
            label="Số điện thoại"
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="email"
            label="Email"
            rules={[
              { type: 'email', message: 'Email không hợp lệ' },
            ]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="address"
            label="Địa chỉ"
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="area"
            label="Khu vực"
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="ward"
            label="Phường xã"
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="note"
            label="Ghi chú"
          >
            <TextArea rows={4} />
          </Form.Item>

          <Form.Item
            name="isActive"
            label="Trạng thái"
            valuePropName="checked"
          >
            <Switch checkedChildren="Hoạt động" unCheckedChildren="Không hoạt động" defaultChecked />
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading}>
              {isEdit ? 'Cập nhật' : 'Thêm mới'}
            </Button>
            <Button style={{ marginLeft: 8 }} onClick={() => navigate('/payers')}>
              Hủy
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default PayerForm;
