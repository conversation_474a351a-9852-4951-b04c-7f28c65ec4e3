import React, { useState, useEffect } from 'react';
import { Table, Card, DatePicker, Button, Select, Form, Space, Typography, Statistic, Row, Col, Divider } from 'antd';
import { Toaster, toast } from 'react-hot-toast';
import { SearchOutlined, ReloadOutlined, FileTextOutlined, DollarOutlined, CreditCardOutlined, RollbackOutlined, LineChartOutlined } from '@ant-design/icons';
import moment from 'moment';
import ReportService from '../../services/report.service';
import BranchService from '../../services/branch.service';
import { formatCurrencyWithSymbol } from '../../utils/format';
import { Link } from 'react-router-dom';

const { Title } = Typography;
const { Option } = Select;

const DailyRevenueReport = () => {
  const [loading, setLoading] = useState(false);
  const [reportData, setReportData] = useState({
    date: new Date(),
    totalRevenue: 0,
    totalPaid: 0,
    totalReturns: 0,
    netRevenue: 0,
    invoiceCount: 0,
    invoices: []
  });
  const [branches, setBranches] = useState([]);
  const [form] = Form.useForm();

  useEffect(() => {
    fetchBranches();
    fetchReport();
  }, []);

  const fetchBranches = async () => {
    try {
      const response = await BranchService.getAllBranches();
      setBranches(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách chi nhánh: ' + error.message);
    }
  };

  const fetchReport = async (values = {}) => {
    setLoading(true);
    try {
      const options = {
        branchId: values.branchId,
      };

      if (values.date) {
        options.date = values.date.format('YYYY-MM-DD');
      }

      const response = await ReportService.getDailyRevenue(options);
      setReportData(response.data.data);
    } catch (error) {
      toast.error('Không thể tải báo cáo: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (values) => {
    fetchReport(values);
  };

  const handleReset = () => {
    form.resetFields();
    fetchReport();
  };

  const columns = [
    {
      title: <span style={{ fontSize: 16 }}>Mã hoá đơn</span>,
      dataIndex: 'invoiceCode',
      key: 'invoiceCode',
      render: (text, record) => <Link to={`/invoices/view/${record.id}`}>{text}</Link>,
    },
    {
      title: <span style={{ fontSize: 16 }}>Khách hàng</span>,
      dataIndex: ['Customer', 'name'],
      key: 'customerName',
      render: (text, record) => record.Customer ? record.Customer.name : 'N/A',
    },
    {
      title: <span style={{ fontSize: 16 }}>Thời gian</span>,
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => moment(date).format('DD/MM/YYYY HH:mm:ss'),
      sorter: (a, b) => moment(a.createdAt).valueOf() - moment(b.createdAt).valueOf(),
    },
    {
      title: <span style={{ fontSize: 16 }}>Tổng tiền</span>,
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      render: (amount) => formatCurrencyWithSymbol(amount),
      sorter: (a, b) => a.totalAmount - b.totalAmount,
    },
    {
      title: <span style={{ fontSize: 16 }}>Đã thanh toán</span>,
      dataIndex: 'paidAmount',
      key: 'paidAmount',
      render: (amount) => formatCurrencyWithSymbol(amount),
    },
    {
      title: <span style={{ fontSize: 16 }}>Còn nợ</span>,
      key: 'remainingAmount',
      render: (_, record) => formatCurrencyWithSymbol(record.totalAmount - record.paidAmount),
    },
  ];

  return (
    <div>
      <Toaster position="top-right" />
      <Card style={{ marginBottom: 20 }}>
        <Form
          form={form}
          layout="inline"
          onFinish={handleSearch}
          initialValues={{
            date: moment(),
          }}
        >
          <Form.Item name="date" label={<span style={{ fontSize: 16 }}>Ngày</span>}>
            <DatePicker format="DD/MM/YYYY" style={{ height: 42, fontSize: 16 }} />
          </Form.Item>
          <Form.Item name="branchId" label={<span style={{ fontSize: 16 }}>Chi nhánh</span>}>
            <Select
              placeholder="Tất cả chi nhánh"
              style={{ width: 200, height: 42, fontSize: 16 }}
              allowClear
            >
              {branches.map((branch) => (
                <Option key={branch.id} value={branch.id}>
                  {branch.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" icon={<SearchOutlined />} style={{ height: 40, fontSize: 16, display: 'flex', alignItems: 'center' }}>
                Tìm kiếm
              </Button>
              <Button onClick={handleReset} icon={<ReloadOutlined />} style={{ height: 40, fontSize: 16, display: 'flex', alignItems: 'center' }}>
                Đặt lại
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title={<span style={{ fontSize: 16 }}>Tổng doanh thu</span>}
              value={formatCurrencyWithSymbol(reportData.totalRevenue)}
              prefix={<DollarOutlined />}
              loading={loading}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title={<span style={{ fontSize: 16 }}>Giá trị hàng trả lại</span>}
              value={formatCurrencyWithSymbol(reportData.totalReturns || 0)}
              prefix={<RollbackOutlined />}
              loading={loading}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title={<span style={{ fontSize: 16 }}>Doanh thu thuần</span>}
              value={formatCurrencyWithSymbol(reportData.netRevenue || (reportData.totalRevenue - (reportData.totalReturns || 0)))}
              prefix={<LineChartOutlined />}
              loading={loading}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title={<span style={{ fontSize: 16 }}>Số lượng hoá đơn</span>}
              value={reportData.invoiceCount}
              prefix={<FileTextOutlined />}
              loading={loading}
            />
          </Card>
        </Col>
      </Row>

      <Divider><span style={{ fontSize: 18, fontWeight: 'bold' }}>Danh sách hoá đơn trong ngày</span></Divider>

      <Table
        columns={columns}
        dataSource={reportData.invoices}
        rowKey="id"
        loading={loading}
        pagination={{ pageSize: 10, style: { fontSize: 16 } }}
        style={{ fontSize: 16 }}
      />
    </div>
  );
};

export default DailyRevenueReport;
