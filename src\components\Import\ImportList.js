import React, { useState, useEffect } from 'react';
import { Table, Button, Space, Popconfirm, Typography, Input, Card, Tag } from 'antd';
import { Toaster, toast } from 'react-hot-toast';
import { PlusOutlined, EditOutlined, DeleteOutlined, SearchOutlined, EyeOutlined } from '@ant-design/icons';
import { Link } from 'react-router-dom';
import ImportService from '../../services/import.service';
import { formatCurrencyWithSymbol } from '../../utils/format';
import ProductService from '../../services/product.service';

const { Title } = Typography;

const ImportList = () => {
  const [imports, setImports] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');

  useEffect(() => {
    fetchImports();
  }, []);

  const fetchImports = async () => {
    setLoading(true);
    try {
      const response = await ImportService.getAllImports();
      setImports(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách phiếu nhập: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Hàm kiểm tra tồn kho sau khi cập nhật
  const verifyStockUpdate = async (productId, expectedStock) => {
    try {
      // Lấy thông tin sản phẩm từ backend
      const productResponse = await ProductService.getProductById(productId);
      const product = productResponse.data;

      // So sánh tồn kho thực tế với tồn kho kỳ vọng
      const actualStock = product.stock || 0;

      if (actualStock !== expectedStock) {


        return {
          isCorrect: false,
          difference: actualStock - expectedStock,
          expected: expectedStock,
          actual: actualStock
        };
      }


      return {
        isCorrect: true,
        expected: expectedStock,
        actual: actualStock
      };
    } catch (error) {

      return { isCorrect: false, error };
    }
  };

  // Kiểm tra xem phiếu nhập đã thực sự cập nhật tồn kho hay chưa
  const checkImportHasUpdatedStock = async (importItems, importDate) => {
    if (!importItems || importItems.length === 0) return { hasUpdated: false };

    try {
      // Lấy tất cả sản phẩm trong phiếu nhập để kiểm tra
      const productsToCheck = [];

      for (const item of importItems) {
        const productId = item.productId || (item.Product && item.Product.id);
        if (!productId) continue;

        productsToCheck.push({
          productId,
          quantity: item.quantity
        });
      }

      // Lấy lịch sử tồn kho của sản phẩm (giả định) - có thể thay thế bằng API thực tế nếu có
      // Trong thực tế, cần có API để lấy lịch sử tồn kho
      const productStockHistory = []; // Giả định dữ liệu

      // Dựa vào thời gian phiếu nhập và lịch sử tồn kho, xác định xem đã cập nhật chưa
      // Đây là phương pháp ước lượng, chỉ có thể chính xác nếu có dữ liệu lịch sử tồn kho thực tế
      const importDateTime = new Date(importDate);

      return {
        hasUpdated: true, // Giả định đã cập nhật
        productsUpdated: productsToCheck
      };
    } catch (error) {

      return { hasUpdated: true }; // Mặc định là đã cập nhật để tránh cập nhật trùng lặp
    }
  };

  const handleDelete = async (id) => {
    try {
      // Biến cờ để đảm bảo chỉ cập nhật tồn kho một lần
      let stockUpdateCompleted = false;

      // Trước khi xóa, lấy thông tin phiếu nhập để kiểm tra trạng thái và cập nhật tồn kho
      const importResponse = await ImportService.getImportById(id);
      const importData = importResponse.data;

      // Nếu phiếu nhập đã hoàn thành, cần cập nhật tồn kho
      if (importData.status === 'completed') {
        // Kiểm tra xem backend có tự động cập nhật tồn kho khi xóa phiếu nhập không

        // Lấy danh sách sản phẩm từ phiếu nhập
        const items = importData.ImportItems || importData.items || [];

        if (items.length > 0) {
          // Mảng lưu trữ thông tin các sản phẩm cần kiểm tra
          const productsToCheck = [];

          // Biến để lưu trữ thông tin sản phẩm

          // Kiểm tra tồn kho hiện tại của các sản phẩm trước
          for (const item of items) {
            try {
              // Lấy thông tin sản phẩm
              const productId = item.productId || (item.Product && item.Product.id);
              if (!productId) continue;

              // Lấy thông tin đầy đủ về sản phẩm
              const productResponse = await ProductService.getProductById(productId);
              const product = productResponse.data;

              // Đảm bảo không có yêu cầu trùng lặp đang xử lý
              await new Promise(resolve => setTimeout(resolve, 300));

              // Lấy số lượng tồn kho hiện tại
              const currentStock = product.stock || 0;
              const quantity = item.quantity || 0;

              // Tính toán tồn kho mới dự kiến nếu phiếu nhập được xóa
              const expectedStockAfterDelete = Math.max(0, currentStock - quantity);

              productsToCheck.push({
                productId,
                name: product.name,
                currentStock,
                quantity,
                expectedStockAfterDelete
              });

              // Nếu tồn kho hiện tại < số lượng trong phiếu nhập, có thể đã có vấn đề
              if (currentStock < quantity) {
                toast.error(`Cảnh báo: Tồn kho hiện tại (${currentStock}) của sản phẩm "${product.name}" thấp hơn số lượng đã nhập (${quantity}).`);
              }
            } catch (error) {
              console.error(`Lỗi kiểm tra sản phẩm:`, error);
            }
          }

          // Bỏ hộp thoại xác nhận thứ hai, vì đã có Popconfirm của Ant Design

          // Mảng lưu trữ thông tin các sản phẩm cần kiểm tra sau khi cập nhật
          const productsToVerify = [];




          // Cập nhật tồn kho cho từng sản phẩm
          for (const product of productsToCheck) {
            try {
              // Lấy lại thông tin sản phẩm mới nhất (đề phòng có sự thay đổi)
              const productResponse = await ProductService.getProductById(product.productId);
              const productData = productResponse.data;
              const latestStock = productData.stock || 0;

              // Kiểm tra nếu tồn kho đã thay đổi trong quá trình xác nhận
              if (latestStock !== product.currentStock) {
                // Cập nhật giá trị mới mà không thông báo
                product.currentStock = latestStock;
              }

              // Không cập nhật tồn kho, chỉ lưu thông tin để kiểm tra sau
              productsToVerify.push({
                productId: product.productId,
                name: product.name,
                previousStock: product.currentStock,
                expectedStock: product.currentStock - product.quantity,
                quantity: product.quantity,
                backendHandled: true
              });

              // Thêm thời gian chờ giữa các yêu cầu API
              await new Promise(resolve => setTimeout(resolve, 500));

            } catch (error) {
              console.error(`Lỗi cập nhật tồn kho sản phẩm "${product.name || 'Không xác định'}":  ${error.message}`);
            }
          }

          // Không cần chờ đợi quá lâu, backend sẽ tự xử lý

          // Kiểm tra lại tồn kho sau khi cập nhật
          let allStocksCorrect = true;

          for (const product of productsToVerify) {
            try {
              // Gọi trực tiếp API để lấy dữ liệu mới nhất, bỏ qua cache
              const freshProductResponse = await ProductService.getProductById(product.productId, { timestamp: new Date().getTime() });
              const freshProduct = freshProductResponse.data;
              const actualStock = freshProduct.stock || 0;

              // Kiểm tra xem tồn kho có chính xác không
              if (actualStock === product.expectedStock) {
                // Tồn kho chính xác
              } else {
                // Phân tích tình huống
                if (actualStock === product.previousStock - (2 * product.quantity)) {
                  // Sửa lỗi tồn kho bị giảm gấp đôi
                  const correctedStock = actualStock + product.quantity;
                  await ProductService.updateProduct(product.id, {
                    stock: correctedStock
                  });
                  // Không cần kiểm tra lại ngay lập tức, backend sẽ tự xử lý
                } else if (actualStock !== product.previousStock) {
                  // Chỉ ghi nhận lỗi, không hiển thị thông báo
                  console.warn(`Tồn kho sản phẩm "${product.name}" không khớp với giá trị kỳ vọng.`);
                  allStocksCorrect = false;
                }
              }
            } catch (error) {
              console.error(`Lỗi kiểm tra tồn kho sản phẩm "${product.name}": ${error.message}`);
            }
          }

          // Không hiển thị thông báo về tồn kho, chỉ ghi log

          // Đánh dấu đã hoàn thành cập nhật tồn kho
          stockUpdateCompleted = true;
        }
      }

      // Xóa phiếu nhập
      await ImportService.deleteImport(id);
      toast.success('Xóa phiếu nhập thành công');

      // Làm mới dữ liệu sau khi xóa
      await fetchImports();
    } catch (error) {

      toast.error('Không thể xóa phiếu nhập: ' + (error.response?.data?.message || error.message));
    }
  };

  const filteredImports = imports.filter(
    (importItem) =>
      importItem.importCode?.toLowerCase().includes(searchText.toLowerCase()) ||
      importItem.supplier?.name?.toLowerCase().includes(searchText.toLowerCase())
  );

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'green';
      case 'pending':
        return 'orange';
      case 'cancelled':
        return 'red';
      default:
        return 'default';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'completed':
        return 'Đã hoàn thành';
      case 'pending':
        return 'Chờ xử lý';
      case 'cancelled':
        return 'Đã hủy';
      default:
        return status;
    }
  };

  const columns = [
    {
      title: <span style={{ fontSize: 16 }}>Mã phiếu nhập</span>,
      dataIndex: 'importCode',
      key: 'importCode',
    },
    {
      title: <span style={{ fontSize: 16 }}>Nhà cung cấp</span>,
      key: 'supplier',
      render: (_, record) => {
        if (record.supplier && record.supplier.name) {
          return record.supplier.name;
        }
        if (record.Supplier && record.Supplier.name) {
          return record.Supplier.name;
        }
        return 'N/A';
      },
    },
    {
      title: <span style={{ fontSize: 16 }}>Ngày nhập</span>,
      dataIndex: 'importDate',
      key: 'importDate',
      render: (date) => new Date(date).toLocaleDateString('vi-VN'),
      sorter: (a, b) => new Date(a.importDate) - new Date(b.importDate),
    },
    {
      title: <span style={{ fontSize: 16 }}>Tổng tiền</span>,
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      render: (amount) => formatCurrencyWithSymbol(amount),
      sorter: (a, b) => a.totalAmount - b.totalAmount,
    },
    {
      title: <span style={{ fontSize: 16 }}>Đã thanh toán</span>,
      dataIndex: 'paidAmount',
      key: 'paidAmount',
      render: (amount) => formatCurrencyWithSymbol(amount),
    },
    {
      title: <span style={{ fontSize: 16 }}>Còn nợ</span>,
      dataIndex: 'remainingAmount',
      key: 'remainingAmount',
      render: (amount) => {
        const style = amount > 0 ? { color: 'red', fontWeight: 'bold' } : {};
        return <span style={style}>{formatCurrencyWithSymbol(amount)}</span>;
      },
      sorter: (a, b) => a.remainingAmount - b.remainingAmount,
    },
    {
      title: <span style={{ fontSize: 16 }}>Trạng thái</span>,
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: <span style={{ fontSize: 16 }}>Thao tác</span>,
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Link to={`/imports/view/${record.id}`}>
            <Button type="default" icon={<EyeOutlined />} style={{ fontSize: 16, height: 40, padding: '0 15px', display: 'flex', alignItems: 'center' }}>
              Xem
            </Button>
          </Link>
          <Link to={`/imports/edit/${record.id}`}>
            <Button type="primary" icon={<EditOutlined />} style={{ fontSize: 16, height: 40, padding: '0 15px', display: 'flex', alignItems: 'center' }}>
              Sửa
            </Button>
          </Link>
          <Popconfirm
            title="Bạn có chắc chắn muốn xóa phiếu nhập này?"
            onConfirm={() => handleDelete(record.id)}
            okText="Có"
            cancelText="Không"
          >
            <Button type="primary" danger icon={<DeleteOutlined />} style={{ fontSize: 16, height: 40, padding: '0 15px', display: 'flex', alignItems: 'center' }}>
              Xóa
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Toaster position="top-right" />
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
        <Title level={2} style={{ fontSize: 28, background: 'linear-gradient(90deg, #1890ff, #096dd9)', WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent' }}>Quản lý nhập hàng</Title>
        <Link to="/imports/add">
          <Button type="primary" icon={<PlusOutlined />} style={{ height: 40, fontSize: 16, display: 'flex', alignItems: 'center' }}>
            Tạo phiếu nhập
          </Button>
        </Link>
      </div>

      <Card style={{ marginBottom: 16 }}>
        <Input
          placeholder="Tìm kiếm phiếu nhập..."
          prefix={<SearchOutlined />}
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          style={{ width: 300, height: 42, fontSize: 16 }}
        />
      </Card>

      <Table
        columns={columns}
        dataSource={filteredImports}
        rowKey="id"
        loading={loading}
        pagination={{ pageSize: 10, style: { fontSize: 16 } }}
        style={{ fontSize: 16 }}
      />
    </div>
  );
};

export default ImportList;
