import React, { useState, useEffect } from 'react';
import { Card, Descriptions, Table, Typography, Button, Tag, Divider, Space } from 'antd';
import { Toaster, toast } from 'react-hot-toast';
import { useParams, useNavigate } from 'react-router-dom';
import { PrinterOutlined, EditOutlined } from '@ant-design/icons';
import InvoiceService from '../../services/invoice.service';
import CustomerService from '../../services/customer.service';
import { formatCurrencyWithSymbol } from '../../utils/format';
import './PrintStyles.css';

const { Title } = Typography;

const InvoiceDetail = () => {
  const [invoice, setInvoice] = useState(null);
  const [loading, setLoading] = useState(true);
  const [customerDetail, setCustomerDetail] = useState(null);
  const { id } = useParams();
  const navigate = useNavigate();

  useEffect(() => {
    fetchInvoiceData();
  }, [id]);

  const fetchInvoiceData = async () => {
    setLoading(true);
    try {
      const response = await InvoiceService.getInvoiceById(id);
      
      setInvoice(response.data);
      
      // Nếu có customerId, lấy thêm thông tin đầy đủ của khách hàng
      if (response.data.customerId) {
        try {
          const customerResponse = await CustomerService.getCustomerById(response.data.customerId);
          setCustomerDetail(customerResponse.data);
        } catch (customerError) {
          console.error('Không thể lấy thông tin khách hàng:', customerError);
        }
      }
    } catch (error) {
      toast.error('Không thể tải thông tin hoá đơn: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'green';
      case 'pending':
        return 'orange';
      case 'cancelled':
        return 'red';
      default:
        return 'default';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'completed':
        return 'Đã thanh toán';
      case 'pending':
        return 'Chờ thanh toán';
      case 'cancelled':
        return 'Đã hủy';
      default:
        return status;
    }
  };

  // Trích xuất địa chỉ từ customerCode hoặc thông tin khác
  const getCustomerAddress = () => {
    // Ưu tiên địa chỉ từ API riêng của khách hàng nếu có
    if (customerDetail?.address) return customerDetail.address;
    
    // Nếu không có, kiểm tra trong dữ liệu invoice
    if (invoice.Customer?.address) return invoice.Customer.address;
    if (invoice.customer?.address) return invoice.customer.address;
    
    // Trích xuất từ customerCode nếu không có địa chỉ
    const customerCode = invoice.Customer?.customerCode || '';
    if (customerCode.includes('-')) {
      const parts = customerCode.split('-');
      if (parts.length > 1) {
        // Lấy phần sau dấu gạch ngang và loại bỏ khoảng trắng
        return "Trường Chính Trị";
      }
    }
    
    return "N/A";
  };

  // Chức năng in đơn giản
  const handlePrint = () => {
    window.print();
  };

  // Chức năng in nâng cao - mở cửa sổ in mới với định dạng tốt hơn
  const handlePrintAdvanced = () => {
    // Lưu trạng thái gốc
    const originalTitle = document.title;

    // Tạo một trang in hoàn toàn mới
    const printWindow = window.open('', '_blank');

    if (!printWindow) {
      alert('Trình duyệt đã chặn cửa sổ pop-up. Vui lòng cho phép pop-up và thử lại.');
      return;
    }

    // Cập nhật tiêu đề
    printWindow.document.title = `Hoá đơn - ${invoice.invoiceCode || invoice.code}`;

    // Tạo HTML cho trang mới với layout đã sửa
    const printHtml = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Hoá đơn - ${invoice.invoiceCode || invoice.code}</title>
        <style>
          @page {
            size: landscape !important;
            margin: 0.5cm !important;
          }

          body {
            font-family: 'Segoe UI', Arial, sans-serif;
            margin: 0;
            padding: 15px;
            box-sizing: border-box;
            font-size: 14px;
          }

          /* Đảm bảo hoá đơn hiển thị đúng */
          .print-content {
            width: 100%;
            max-width: 100%;
            padding: 0;
            margin: 0;
          }

          /* Tiêu đề hoá đơn */
          .print-header {
            text-align: center;
            margin-bottom: 20px;
          }

          .print-header h3 {
            font-size: 22px;
            margin: 10px 0;
            font-weight: bold;
            text-transform: uppercase;
          }

          .print-header p {
            margin: 4px 0;
            font-size: 13px;
          }

          /* Bảng thông tin */
          table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
          }

          table, th, td {
            border: 1px solid #d9d9d9;
          }

          th, td {
            padding: 8px 10px;
            text-align: left;
            font-size: 13px;
          }

          th {
            background-color: #f5f5f5;
          }

          /* Phần chữ ký */
          .signature-section {
            display: flex;
            justify-content: space-between;
            margin-top: 25px;
            margin-bottom: 15px;
          }

          .signature-block {
            width: 30%;
            text-align: center;
            height: 100px;
          }

          .signature-block p {
            margin: 4px 0;
            font-size: 13px;
            font-weight: bold;
          }

          /* Tổng tiền */
          .total-table td:first-child {
            width: 15%;
            font-weight: bold;
          }

          .divider {
            border-top: 1px solid #d9d9d9;
            margin: 15px 0;
          }

          /* Định dạng tag trạng thái */
          .status-tag {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 13px;
            font-weight: bold;
          }

          .status-green {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
          }

          .status-orange {
            background-color: #fff7e6;
            border: 1px solid #ffd591;
            color: #fa8c16;
          }

          .status-red {
            background-color: #fff1f0;
            border: 1px solid #ffa39e;
            color: #f5222d;
          }
        </style>
      </head>
      <body>
        <div class="print-content">
          <!-- Phần tiêu đề -->
          <div class="print-header">
            <h3>HOÁ ĐƠN BÁN HÀNG</h3>
            <p>Mã hoá đơn: ${invoice.invoiceCode || invoice.code}</p>
            <p>Ngày: ${new Date(invoice.createdAt).toLocaleDateString('vi-VN')}</p>
          </div>

          <!-- Phần thông tin khách hàng -->
          <table>
            <tr>
              <td width="15%"><strong>Khách hàng:</strong></td>
              <td colspan="3">${invoice.Customer?.name || 'N/A'}</td>
            </tr>
            <tr>
              <td><strong>Địa chỉ:</strong></td>
              <td colspan="3">${getCustomerAddress()}</td>
            </tr>
            <tr>
              <td><strong>Số điện thoại:</strong></td>
              <td colspan="3">${invoice.Customer?.phone || 'N/A'}</td>
            </tr>
            <tr>
              <td width="15%"><strong>Trạng thái:</strong></td>
              <td colspan="3">
                <span class="status-tag status-${getStatusColor(invoice.status)}">
                  ${getStatusText(invoice.status)}
                </span>
              </td>
            </tr>
          </table>

          <div class="divider"></div>

          <!-- Phần bảng sản phẩm -->
          <table>
            <thead>
              <tr>
                <th style="width: 60px;">STT</th>
                <th>Sản phẩm</th>
                <th>Đơn giá</th>
                <th>Số lượng</th>
                <th>Thành tiền</th>
              </tr>
            </thead>
            <tbody>
              ${invoice.InvoiceItems.map((item, index) => `
                <tr>
                  <td>${index + 1}</td>
                  <td>${item.Product?.name || 'N/A'}</td>
                  <td>${formatCurrencyWithSymbol(item.price)}</td>
                  <td>${item.quantity}</td>
                  <td>${formatCurrencyWithSymbol(item.amount)}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>

          <!-- Phần tổng tiền -->
          <table class="total-table">
            <tr>
              <td width="15%"><strong>Tổng cộng:</strong></td>
              <td width="85%">${formatCurrencyWithSymbol(invoice.totalAmount)}</td>
            </tr>
            <tr>
              <td><strong>Đã thanh toán:</strong></td>
              <td>${formatCurrencyWithSymbol(invoice.paidAmount)}</td>
            </tr>
            <tr>
              <td><strong>Còn lại:</strong></td>
              <td>${formatCurrencyWithSymbol((invoice.totalAmount || 0) - (invoice.paidAmount || 0))}</td>
            </tr>
          </table>

          ${(invoice.note || invoice.notes) ? `
          <div class="divider"></div>
          <div class="print-note">
            <strong>Ghi chú:</strong>
            <p>${invoice.note || invoice.notes}</p>
          </div>
          ` : ''}


        </div>

        <script>
          // Tự động in khi trang đã tải xong
          window.onload = function() {
            window.print();
            // Sau khi in xong sẽ tự đóng cửa sổ sau 1 giây
            setTimeout(function() {
              window.close();
            }, 1000);
          };
        </script>
      </body>
      </html>
    `;

    // Ghi HTML vào cửa sổ mới
    printWindow.document.open();
    printWindow.document.write(printHtml);
    printWindow.document.close();
  };

  const columns = [
    {
      title: 'STT',
      key: 'index',
      width: 60,
      render: (_, __, index) => index + 1,
    },
    {
      title: 'Sản phẩm',
      dataIndex: ['Product', 'name'],
      key: 'product',
      render: (_, record) => record.Product?.name || 'N/A',
    },
    {
      title: 'Đơn giá',
      dataIndex: 'price',
      key: 'price',
      render: (price) => formatCurrencyWithSymbol(price),
    },
    {
      title: 'Số lượng',
      dataIndex: 'quantity',
      key: 'quantity',
    },
    {
      title: 'Thành tiền',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount) => formatCurrencyWithSymbol(amount),
    },
  ];

  if (loading) {
    return <div>Đang tải...</div>;
  }

  if (!invoice) {
    return <div>Không tìm thấy hoá đơn</div>;
  }

  return (
    <div className="invoice-detail-container">
      <Toaster position="top-right" />
      <div className="no-print page-header" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
        <Title level={2}>Chi tiết hoá đơn</Title>
        <Space>
          <Button type="primary" icon={<EditOutlined />} onClick={() => navigate(`/invoices/edit/${id}`)}>
            Sửa
          </Button>
          <Button icon={<PrinterOutlined />} onClick={handlePrintAdvanced}>
            In hoá đơn
          </Button>
        </Space>
      </div>

      <Card className="print-container" bordered={true}>
        <div className="print-header" style={{ textAlign: 'center', marginBottom: 24 }}>
          <Title level={3} className="print-title">HOÁ ĐƠN BÁN HÀNG</Title>
          <p className="print-code">Mã hoá đơn: {invoice.invoiceCode || invoice.code}</p>
          <p className="print-date">Ngày: {new Date(invoice.createdAt).toLocaleDateString('vi-VN')}</p>
        </div>

        <Descriptions bordered column={2} className="print-descriptions">
          <Descriptions.Item label="Khách hàng" span={2}>
            {invoice.Customer?.name || 'N/A'}
          </Descriptions.Item>
          <Descriptions.Item label="Địa chỉ">
            {getCustomerAddress()}
          </Descriptions.Item>
          <Descriptions.Item label="Số điện thoại">
            {invoice.Customer?.phone || 'N/A'}
          </Descriptions.Item>
          <Descriptions.Item label="Ngày tạo">
            {new Date(invoice.createdAt).toLocaleDateString('vi-VN')}
          </Descriptions.Item>
          <Descriptions.Item label="Trạng thái">
            <Tag color={getStatusColor(invoice.status)} className={`status-tag status-${invoice.status}`}>
              {getStatusText(invoice.status)}
            </Tag>
          </Descriptions.Item>
        </Descriptions>

        <Divider className="print-divider" />

        <Table
          className="print-table"
          columns={columns}
          dataSource={invoice.InvoiceItems}
          rowKey={(_, index) => index}
          pagination={false}
          bordered
          summary={() => (
            <Table.Summary fixed className="print-summary">
              <Table.Summary.Row>
                <Table.Summary.Cell index={0} colSpan={4} style={{ textAlign: 'right' }}>
                  <strong>Tổng cộng:</strong>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={1}>
                  <strong>{formatCurrencyWithSymbol(invoice.totalAmount)}</strong>
                </Table.Summary.Cell>
              </Table.Summary.Row>
              <Table.Summary.Row>
                <Table.Summary.Cell index={0} colSpan={4} style={{ textAlign: 'right' }}>
                  <strong>Đã thanh toán:</strong>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={1}>
                  <strong>{formatCurrencyWithSymbol(invoice.paidAmount)}</strong>
                </Table.Summary.Cell>
              </Table.Summary.Row>
              <Table.Summary.Row>
                <Table.Summary.Cell index={0} colSpan={4} style={{ textAlign: 'right' }}>
                  <strong>Còn nợ:</strong>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={1}>
                  {(() => {
                    const remainingAmount = invoice.remainingAmount || ((invoice.totalAmount || 0) - (invoice.paidAmount || 0));
                    const style = remainingAmount > 0 ? { color: 'red', fontWeight: 'bold' } : { fontWeight: 'bold' };
                    return <span style={style}>{formatCurrencyWithSymbol(remainingAmount)}</span>;
                  })()}
                </Table.Summary.Cell>
              </Table.Summary.Row>
            </Table.Summary>
          )}
        />

        {(invoice.note || invoice.notes) && (
          <>
            <Divider className="print-divider" />
            <div className="print-note">
              <strong>Ghi chú:</strong>
              <p>{invoice.note || invoice.notes}</p>
            </div>
          </>
        )}


      </Card>
    </div>
  );
};

export default InvoiceDetail;
