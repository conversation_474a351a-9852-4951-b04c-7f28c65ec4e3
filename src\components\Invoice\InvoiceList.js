import React, { useState, useEffect } from 'react';
import { Table, Button, Space, Popconfirm, Typography, Input, Card, Tag } from 'antd';
import { Toaster, toast } from 'react-hot-toast';
import { PlusOutlined, EditOutlined, DeleteOutlined, SearchOutlined, EyeOutlined } from '@ant-design/icons';
import { Link } from 'react-router-dom';
import InvoiceService from '../../services/invoice.service';
import ProductService from '../../services/product.service';
import { formatCurrencyWithSymbol } from '../../utils/format';

const { Title } = Typography;

const InvoiceList = () => {
  const [invoices, setInvoices] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');

  useEffect(() => {
    fetchInvoices();
  }, []);

  const fetchInvoices = async () => {
    setLoading(true);
    try {
      const response = await InvoiceService.getAllInvoices();
      setInvoices(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách hoá đơn: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    try {
      // Kiểm tra xem hóa đơn có phiếu trả hàng liên quan không
      const returnCheckResult = await InvoiceService.checkInvoiceHasReturns(id);
      
      if (returnCheckResult.hasReturns) {
        // Nếu có phiếu trả hàng liên quan, hiển thị thông báo lỗi và không cho phép xóa
        const returnCount = returnCheckResult.returns.length;
        toast.error(`Không thể xóa hóa đơn này vì đang có ${returnCount} phiếu trả hàng liên quan.`);
        return;
      }
      
      // Trước khi xóa, lấy thông tin hóa đơn để kiểm tra trạng thái và cập nhật tồn kho
      const invoiceResponse = await InvoiceService.getInvoiceById(id);
      const invoiceData = invoiceResponse.data;
      
      // Nếu hóa đơn đã hoàn thành, cần cập nhật tồn kho
      if (invoiceData.status === 'completed') {
        console.log('Hóa đơn đã hoàn thành, cập nhật tồn kho trước khi xóa');
        
        // Lấy danh sách sản phẩm từ hóa đơn
        const items = invoiceData.InvoiceItems || invoiceData.items || [];
        
        if (items.length > 0) {
          // Cập nhật tồn kho cho từng sản phẩm
          for (const item of items) {
            try {
              // Lấy thông tin sản phẩm hiện tại từ backend
              const productId = item.productId || (item.Product && item.Product.id);
              if (!productId) continue;
              
              const productResponse = await ProductService.getProductById(productId);
              const product = productResponse.data;
              
              // Lấy số lượng tồn kho hiện tại
              const currentStock = product.stock || 0;
              const quantity = item.quantity || 0;
              
              // Khi xóa hóa đơn đã hoàn thành, cộng lại tồn kho vì sản phẩm không bị bán nữa
              const newStock = currentStock + quantity;
              
              console.log(`Xóa hóa đơn đã hoàn thành, tăng tồn kho sản phẩm ${productId}: ${currentStock} → ${newStock}`);
              
              // Cập nhật tồn kho sản phẩm
              await ProductService.updateProduct(productId, {
                ...product,
                stock: newStock
              });
            } catch (error) {
              console.error(`Lỗi cập nhật tồn kho sản phẩm:`, error);
            }
          }
          
          toast.success('Đã cập nhật tồn kho cho các sản phẩm');
        }
      }
      
      // Sau khi cập nhật tồn kho, tiến hành xóa hóa đơn
      await InvoiceService.deleteInvoice(id);
      toast.success('Xóa hoá đơn thành công');
      fetchInvoices();
    } catch (error) {
      console.error('Lỗi chi tiết:', error);
      toast.error('Không thể xóa hoá đơn: ' + (error.response?.data?.message || error.message));
    }
  };

  const filteredInvoices = invoices.filter(
    (invoice) =>
      invoice.invoiceCode?.toLowerCase().includes(searchText.toLowerCase()) ||
      invoice.code?.toLowerCase().includes(searchText.toLowerCase()) ||
      invoice.Customer?.name?.toLowerCase().includes(searchText.toLowerCase())
  );

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'green';
      case 'pending':
        return 'orange';
      case 'cancelled':
        return 'red';
      default:
        return 'default';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'completed':
        return 'Đã thanh toán';
      case 'pending':
        return 'Chờ thanh toán';
      case 'cancelled':
        return 'Đã hủy';
      default:
        return status;
    }
  };

  const columns = [
    {
      title: <span style={{ fontSize: 16 }}>Mã hóa đơn</span>,
      dataIndex: 'invoiceCode',
      key: 'invoiceCode',
    },
    {
      title: <span style={{ fontSize: 16 }}>Khách hàng</span>,
      dataIndex: ['Customer', 'name'],
      key: 'customer',
      render: (_, record) => {
        // Kiểm tra nhiều trường hợp có thể có
        if (record.Customer) {
          // Ưu tiên name (theo dữ liệu API)
          if (record.Customer.name) {
            return record.Customer.name;
          }
          // Nếu có Customer nhưng không có tên
          return 'Khách hàng không tên';
        }
        return 'N/A';
      },
    },
    {
      title: <span style={{ fontSize: 16 }}>Ngày tạo</span>,
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => new Date(date).toLocaleDateString('vi-VN'),
      sorter: (a, b) => new Date(a.createdAt) - new Date(b.createdAt),
    },
    {
      title: <span style={{ fontSize: 16 }}>Tổng tiền</span>,
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      render: (amount) => formatCurrencyWithSymbol(amount),
      sorter: (a, b) => a.totalAmount - b.totalAmount,
    },
    {
      title: <span style={{ fontSize: 16 }}>Đã thanh toán</span>,
      dataIndex: 'paidAmount',
      key: 'paidAmount',
      render: (amount) => formatCurrencyWithSymbol(amount),
    },
    {
      title: <span style={{ fontSize: 16 }}>Còn nợ</span>,
      dataIndex: 'remainingAmount',
      key: 'remainingAmount',
      render: (amount) => {
        const style = amount > 0 ? { color: 'red', fontWeight: 'bold' } : {};
        return <span style={style}>{formatCurrencyWithSymbol(amount)}</span>;
      },
      sorter: (a, b) => a.remainingAmount - b.remainingAmount,
    },
    {
      title: <span style={{ fontSize: 16 }}>Trạng thái</span>,
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: <span style={{ fontSize: 16 }}>Thao tác</span>,
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Link to={`/invoices/view/${record.id}`}>
            <Button type="default" icon={<EyeOutlined />} size="small" style={{ height: 40, fontSize: 14, display: 'flex', alignItems: 'center', padding: '0 15px' }}>
              Xem
            </Button>
          </Link>
          <Link to={`/invoices/edit/${record.id}`}>
            <Button type="primary" icon={<EditOutlined />} size="small" style={{ height: 40, fontSize: 14, display: 'flex', alignItems: 'center', padding: '0 15px' }}>
              Sửa
            </Button>
          </Link>
          <Popconfirm
            title="Bạn có chắc chắn muốn xóa hoá đơn này?"
            onConfirm={() => handleDelete(record.id)}
            okText="Có"
            cancelText="Không"
          >
            <Button type="primary" danger icon={<DeleteOutlined />} size="small" style={{ height: 40, fontSize: 14, display: 'flex', alignItems: 'center', padding: '0 15px' }}>
              Xóa
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Toaster position="top-right" />
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 20 }}>
        <Title level={2} style={{ fontSize: 28, background: 'linear-gradient(90deg, #1890ff, #096dd9)', WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent' }}>Quản lý hoá đơn</Title>
        <Link to="/invoices/add">
          <Button type="primary" icon={<PlusOutlined />} style={{ height: 40, fontSize: 16, display: 'flex', alignItems: 'center' }}>
            Tạo hoá đơn
          </Button>
        </Link>
      </div>

      <Card style={{ marginBottom: 16 }}>
        <Input
          placeholder="Tìm kiếm hoá đơn..."
          prefix={<SearchOutlined />}
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          style={{ width: 300, height: 42, fontSize: 16 }}
        />
      </Card>

      <Table
        columns={columns}
        dataSource={filteredInvoices}
        rowKey="id"
        loading={loading}
        pagination={{ pageSize: 10, style: { fontSize: 16 } }}
        style={{ fontSize: 16 }}
      />
    </div>
  );
};

export default InvoiceList;
