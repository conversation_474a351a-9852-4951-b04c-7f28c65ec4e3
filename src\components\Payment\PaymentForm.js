import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Card, Select, DatePicker, InputNumber, Typography, Divider, Modal, Switch, Space } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';
import { Toaster, toast } from 'react-hot-toast';
import moment from 'moment';
import PaymentService from '../../services/payment.service';
import ReceiptTypeService from '../../services/receiptType.service';
import UserService from '../../services/user.service';
import SupplierService from '../../services/supplier.service';
import BankAccountService from '../../services/bankAccount.service';
import BranchService from '../../services/branch.service';
import ImportService from '../../services/import.service';

const { Title } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const PaymentForm = () => {
  const [form] = Form.useForm();
  const [supplierForm] = Form.useForm();
  const [bankAccountForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [receiptTypes, setReceiptTypes] = useState([]);
  const [users, setUsers] = useState([]);
  const [suppliers, setSuppliers] = useState([]);
  const [bankAccounts, setBankAccounts] = useState([]);
  const [branches, setBranches] = useState([]);
  const [imports, setImports] = useState([]);
  const [selectedReceiptType, setSelectedReceiptType] = useState(1); // Mặc định là tiền mặt
  const [importDebtInfo, setImportDebtInfo] = useState(null); // Thông tin công nợ của phiếu nhập
  const [supplierModalVisible, setSupplierModalVisible] = useState(false);
  const [bankAccountModalVisible, setBankAccountModalVisible] = useState(false);
  // Thêm state riêng để quản lý giá trị của dropdown nhà cung cấp
  const [selectedSupplierId, setSelectedSupplierId] = useState(null);
  // Thêm state riêng để quản lý giá trị của dropdown tài khoản ngân hàng
  const [selectedBankAccountId, setSelectedBankAccountId] = useState(null);
  const navigate = useNavigate();
  const { id } = useParams();

  useEffect(() => {
    fetchReceiptTypes();
    fetchUsers();
    fetchSuppliers();
    fetchBankAccounts();
    fetchBranches();
    fetchImports();

    if (id) {
      setIsEdit(true);
      fetchPaymentData();
    }
  }, [id]);
  
  // Theo dõi sự thay đổi của form để cập nhật state
  useEffect(() => {
    // Lắng nghe sự thay đổi của trường receiverId
    const unsubscribe = form.getFieldValue;
    const receiverId = form.getFieldValue('receiverId');
    
    if (receiverId !== undefined && receiverId !== selectedSupplierId) {
      setSelectedSupplierId(receiverId);
    }
    
    return () => {
      // Cleanup nếu cần
    };
  }, [form]);
  
  // Theo dõi sự thay đổi của trường bankAccountId
  useEffect(() => {
    const bankAccountId = form.getFieldValue('bankAccountId');
    
    if (bankAccountId !== undefined && bankAccountId !== selectedBankAccountId) {
      setSelectedBankAccountId(bankAccountId);
    }
  }, [form]);

  const fetchReceiptTypes = async () => {
    try {
      const response = await ReceiptTypeService.getAllReceiptTypes();
      setReceiptTypes(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách loại chi: ' + error.message);
    }
  };

  const fetchUsers = async () => {
    try {
      const response = await UserService.getAllUsers();
      setUsers(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách người dùng: ' + error.message);
    }
  };

  const fetchSuppliers = async () => {
    try {
      const response = await SupplierService.getAllSuppliers();
      setSuppliers(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách nhà cung cấp: ' + error.message);
    }
  };

  const showAddSupplierModal = () => {
    supplierForm.resetFields();
    setSupplierModalVisible(true);
  };

  const handleAddSupplierModalCancel = () => {
    setSupplierModalVisible(false);
  };

  const showAddBankAccountModal = () => {
    bankAccountForm.resetFields();
    setBankAccountModalVisible(true);
  };

  const handleAddBankAccountModalCancel = () => {
    setBankAccountModalVisible(false);
  };

  const handleAddBankAccountModalOk = async () => {
    try {
      const values = await bankAccountForm.validateFields();
      setLoading(true);

      // Gọi API để tạo tài khoản ngân hàng mới
      const response = await BankAccountService.createBankAccount(values);
      const newBankAccount = response.data;

      toast.success('Thêm tài khoản ngân hàng mới thành công');

      // Cập nhật danh sách tài khoản ngân hàng
      setBankAccounts([...bankAccounts, newBankAccount]);

      // Chọn tài khoản ngân hàng vừa tạo trong form
      form.setFieldsValue({ 
        bankAccountId: newBankAccount.id
      });
      
      // Cập nhật state riêng để quản lý giá trị của dropdown
      setSelectedBankAccountId(newBankAccount.id);

      setBankAccountModalVisible(false);
    } catch (error) {
      if (error.errorFields) {
        toast.error('Vui lòng điền đầy đủ thông tin tài khoản ngân hàng');
      } else {
        toast.error('Lỗi khi thêm tài khoản ngân hàng: ' + error.message);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleAddSupplierModalOk = async () => {
    try {
      const values = await supplierForm.validateFields();
      setLoading(true);

      // Gọi API để tạo nhà cung cấp mới
      const response = await SupplierService.createSupplier(values);
      const newSupplier = response.data;

      toast.success('Thêm nhà cung cấp mới thành công');

      // Cập nhật danh sách nhà cung cấp
      setSuppliers([...suppliers, newSupplier]);

      // Chọn nhà cung cấp vừa tạo trong form
      form.setFieldsValue({ 
        receiverId: newSupplier.id,
        receiverName: newSupplier.name
      });
      
      // Cập nhật state để hiển thị giá trị trong dropdown
      setSelectedSupplierId(newSupplier.id);

      setSupplierModalVisible(false);
    } catch (error) {
      if (error.errorFields) {
        toast.error('Vui lòng điền đầy đủ thông tin nhà cung cấp');
      } else {
        toast.error('Lỗi khi thêm nhà cung cấp: ' + error.message);
      }
    } finally {
      setLoading(false);
    }
  };

  const fetchBankAccounts = async () => {
    try {
      const response = await BankAccountService.getAllBankAccounts();
      setBankAccounts(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách tài khoản ngân hàng: ' + error.message);
    }
  };

  const fetchBranches = async () => {
    try {
      const response = await BranchService.getAllBranches();
      setBranches(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách chi nhánh: ' + error.message);
    }
  };

  const fetchImports = async () => {
    try {
      const response = await ImportService.getAllImports();
      setImports(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách phiếu nhập: ' + error.message);
    }
  };

  // Hàm xử lý khi chọn phiếu nhập liên quan
  const handleImportChange = async (importId) => {
    // Reset thông tin công nợ
    setImportDebtInfo(null);

    if (!importId) {
      // Nếu không chọn phiếu nhập, cho phép chọn nhà cung cấp
      form.setFieldsValue({ receiverId: undefined });
      setSelectedSupplierId(null); // Reset state nhà cung cấp
      return;
    }

    try {
      // Lấy thông tin chi tiết của phiếu nhập
      const importResponse = await ImportService.getImportById(importId);
      const selectedImport = importResponse.data;

      if (selectedImport) {
        // Tính toán số tiền còn nợ
        const totalAmount = parseFloat(selectedImport.totalAmount || 0);
        const paidAmount = parseFloat(selectedImport.paidAmount || 0);
        const debtAmount = totalAmount - paidAmount;

        // Lưu thông tin công nợ vào state
        setImportDebtInfo({
          importCode: selectedImport.importCode || `NK${selectedImport.id}`,
          totalAmount,
          paidAmount,
          debtAmount,
          date: selectedImport.createdAt || selectedImport.importDate
        });

        // Tự động điền số tiền còn nợ vào ô số tiền
        if (debtAmount > 0) {
          form.setFieldsValue({ amount: debtAmount });
        }

        // Thử các trường hợp khác nhau của cấu trúc dữ liệu
        const supplierId = selectedImport.supplierId || selectedImport.SupplierId;

        if (supplierId) {
          // Tìm nhà cung cấp trong danh sách nhà cung cấp
          const supplier = suppliers.find(s => s.id === supplierId);

          if (supplier) {
            // Chỉ cập nhật dropdown nhà cung cấp, không cập nhật trường tên người nhận
            form.setFieldsValue({ 
              receiverId: supplierId
            });

            // Cập nhật state riêng để quản lý giá trị của dropdown
            setSelectedSupplierId(supplierId);

            console.log('Đã tự động chọn nhà cung cấp:', supplier.name);
          }
        } else {
          console.log('Không tìm thấy thông tin nhà cung cấp cho phiếu nhập này');
        }
      }
    } catch (error) {
      console.error('Lỗi khi lấy thông tin phiếu nhập:', error);
      toast.error('Không thể lấy thông tin phiếu nhập: ' + error.message);
    }
  };

  const fetchPaymentData = async () => {
    setLoading(true);
    try {
      // Lấy dữ liệu từ tất cả các API cần thiết
      const [paymentResponse, receiptTypesResponse] = await Promise.all([
        PaymentService.getPaymentById(id),
        ReceiptTypeService.getAllReceiptTypes()
      ]);

      const paymentData = paymentResponse.data;

      // Kiểm tra nếu có bankAccountId thì đây là loại Tài khoản (ID: 2)
      if (paymentData.bankAccountId) {
        paymentData.receiptTypeId = 2; // Tài khoản
      } else {
        // Nếu không có bankAccountId, kiểm tra các trường khác
        const typeId = paymentData.paymentTypeId || paymentData.receiptTypeId;
        if (typeId) {
          // Chuyển đổi ID sang số để so sánh chính xác
          const numericTypeId = Number(typeId);

          // Tìm trong danh sách loại thu/chi
          const matchingType = receiptTypesResponse.data.find(type => Number(type.id) === numericTypeId);
          if (matchingType) {
            // Gán loại chi đã tìm thấy
            paymentData.receiptType = matchingType;
            paymentData.receiptTypeId = matchingType.id;
          } else {
            // Gán giá trị mặc định cho loại chi
            if (numericTypeId === 1) {
              paymentData.receiptTypeId = 1; // Tiền mặt
            } else if (numericTypeId === 2) {
              paymentData.receiptTypeId = 2; // Tài khoản
            }
          }
        } else {
          paymentData.receiptTypeId = 1; // Tiền mặt
        }
      }

      // Cập nhật các state để điều khiển các dropdown
      setSelectedReceiptType(paymentData.receiptTypeId);
      setSelectedSupplierId(paymentData.receiverId);
      setSelectedBankAccountId(paymentData.bankAccountId);
      
      // Log để debug
      console.log('Đã cập nhật selectedSupplierId:', paymentData.receiverId);
      console.log('Đã cập nhật selectedBankAccountId:', paymentData.bankAccountId);

      form.setFieldsValue({
        paymentCode: paymentData.paymentCode,
        paymentDate: paymentData.paymentDate ? moment(paymentData.paymentDate) : moment(),
        receiptTypeId: paymentData.receiptTypeId,
        amount: paymentData.amount,
        userId: paymentData.userId,
        receiverId: paymentData.receiverId,
        receiverName: paymentData.receiverName,
        bankAccountId: paymentData.bankAccountId,
        note: paymentData.note,
        branchId: paymentData.branchId,
        status: paymentData.status,
        importId: paymentData.importId,
      });
      
      // Nếu có phiếu nhập liên quan, gọi hàm handleImportChange để lấy thông tin công nợ
      if (paymentData.importId) {
        console.log('Phiếu chi có phiếu nhập liên quan, lấy thông tin công nợ');
        // Sử dụng setTimeout để đảm bảo form đã được cập nhật trước khi gọi handleImportChange
        setTimeout(() => {
          handleImportChange(paymentData.importId);
        }, 100);
      }
    } catch (error) {
      console.error('Lỗi khi tải thông tin phiếu chi:', error);
      toast.error('Không thể tải thông tin phiếu chi: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleReceiptTypeChange = (value) => {
    setSelectedReceiptType(value);

    // Chuyển đổi ID sang số để so sánh chính xác
    const numericValue = Number(value);

    // Nếu ID là 2, đây là loại "Tài khoản" (transfer)
    if (numericValue === 2) {
      return; // Giữ nguyên giá trị tài khoản ngân hàng
    }

    // Nếu chọn loại chi không phải chuyển/rút, xóa giá trị tài khoản ngân hàng
    const receiptType = receiptTypes.find(type => Number(type.id) === numericValue);

    if (!receiptType || receiptType.type !== 'transfer') {
      form.setFieldsValue({ bankAccountId: undefined });
    }
  };

  const onFinish = async (values) => {
    setLoading(true);
    try {
      const paymentData = {
        ...values,
        paymentDate: values.paymentDate.format('YYYY-MM-DD HH:mm:ss'),
        // Đảm bảo các trường bắt buộc có giá trị
        amount: values.amount || 0,
        status: values.status || 'completed'
      };

      // Khi tạo mới, không gửi paymentCode để backend tự động tạo
      if (!isEdit) {
        delete paymentData.paymentCode;
      }

      let paymentResponse;
      
      if (isEdit) {
        paymentResponse = await PaymentService.updatePayment(id, paymentData);
        toast.success('Cập nhật phiếu chi thành công');
      } else {
        paymentResponse = await PaymentService.createPayment(paymentData);
        toast.success('Tạo phiếu chi thành công');
      }
      
      // Cập nhật công nợ nhà cung cấp nếu phiếu chi liên quan đến phiếu nhập và có trạng thái hoàn thành
      if (values.importId && values.status === 'completed') {
        try {
          // Cập nhật số tiền đã thanh toán của phiếu nhập
          await ImportService.updateImportPayment(values.importId, values.amount);
          toast.success('Đã cập nhật công nợ nhà cung cấp');
          
          console.log('Đã cập nhật công nợ cho phiếu nhập:', values.importId);
          console.log('Số tiền thanh toán:', values.amount);
        } catch (importError) {
          console.error('Lỗi khi cập nhật công nợ:', importError);
          toast.error('Không thể cập nhật công nợ: ' + importError.message);
        }
      }
      
      navigate('/payments');
    } catch (error) {
      console.error('Lỗi chi tiết:', error);
      toast.error('Lỗi: ' + error.message);
    } finally {
      setLoading(false);
    }  
  };

  // Kiểm tra xem loại chi có phải là chuyển/rút không
  const isTransferType = () => {
    if (!selectedReceiptType) return false;
    
    const transferTypes = ['transfer', 'withdraw'];
    const receiptType = receiptTypes.find(type => type.id === selectedReceiptType);
    
    if (receiptType && receiptType.type) {
      return transferTypes.includes(receiptType.type.toLowerCase());
    }
    
    return false;
  };

  return (
    <div>
      <Toaster position="top-right" />
      <Title level={2}>{isEdit ? 'Cập nhật phiếu chi' : 'Tạo phiếu chi mới'}</Title>
      <Divider />

      <Card>
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          initialValues={{
            paymentDate: moment(),
            status: 'completed',
          }}
        >
          <div style={{ display: 'flex', gap: '16px' }}>
            <Form.Item
              name="paymentCode"
              label="Mã phiếu chi"
              tooltip={isEdit ? "" : "Mã phiếu chi sẽ được tạo tự động (PCPN...)"}
              style={{ flex: 1 }}
            >
              <Input disabled={!isEdit} placeholder="Tự động tạo (PCPN...)" />
            </Form.Item>

            <Form.Item
              name="paymentDate"
              label="Ngày chi"
              rules={[{ required: true, message: 'Vui lòng chọn ngày chi' }]}
              style={{ flex: 1 }}
            >
              <DatePicker
                showTime
                format="DD/MM/YYYY HH:mm:ss"
                style={{ width: '100%' }}
              />
            </Form.Item>

            <Form.Item
              name="status"
              label="Trạng thái"
              rules={[{ required: true, message: 'Vui lòng chọn trạng thái' }]}
              style={{ flex: 1 }}
            >
              <Select>
                <Option value="pending">Chờ xử lý</Option>
                <Option value="completed">Đã hoàn thành</Option>
                <Option value="cancelled">Đã hủy</Option>
              </Select>
            </Form.Item>
          </div>

          <div style={{ display: 'flex', gap: '16px' }}>
            <Form.Item
              name="receiptTypeId"
              label="Loại chi"
              rules={[{ required: true, message: 'Vui lòng chọn loại chi' }]}
              style={{ flex: 1 }}
            >
              <Select
                placeholder="Chọn loại chi"
                onChange={handleReceiptTypeChange}
              >
                {receiptTypes.map(type => (
                  <Option key={type.id} value={type.id}>{type.name}</Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="amount"
              label="Số tiền"
              rules={[{ required: true, message: 'Vui lòng nhập số tiền' }]}
              style={{ flex: 1 }}
            >
              <InputNumber
                style={{ width: '100%' }}
                formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={value => value.replace(/\$\s?|(,*)/g, '')}
                min={0}
              />
            </Form.Item>

            <Form.Item
              name="branchId"
              label="Chi nhánh"
              style={{ flex: 1 }}
            >
              <Select placeholder="Chọn chi nhánh" allowClear>
                {branches.map(branch => (
                  <Option key={branch.id} value={branch.id}>{branch.name}</Option>
                ))}
              </Select>
            </Form.Item>
          </div>

          <div style={{ display: 'flex', gap: '16px' }}>
            <Form.Item
              name="receiverId"
              label="Nhà cung cấp"
              style={{ flex: 1 }}
            >
              <Space.Compact style={{ width: '100%' }}>
                <Select
                  placeholder="Chọn nhà cung cấp"
                  allowClear
                  style={{ width: 'calc(100% - 40px)' }}
                  value={selectedSupplierId} // Sử dụng state riêng thay vì lấy từ form
                  onChange={(value) => {
                    // Cập nhật cả state và form khi người dùng chọn nhà cung cấp
                    setSelectedSupplierId(value);
                    
                    if (value) {
                      const supplier = suppliers.find(s => s.id === value);
                      if (supplier) {
                        form.setFieldsValue({ 
                          receiverId: value,
                          receiverName: supplier.name 
                        });
                      }
                    } else {
                      form.setFieldsValue({ 
                        receiverId: undefined,
                        receiverName: undefined 
                      });
                    }
                  }}
                >
                  {suppliers.map(supplier => (
                    <Option key={supplier.id} value={supplier.id}>{supplier.name}</Option>
                  ))}
                </Select>
                <Button 
                  type="primary" 
                  icon={<PlusOutlined />} 
                  onClick={showAddSupplierModal}
                  style={{ width: '40px' }}
                />
              </Space.Compact>
            </Form.Item>

            <Form.Item
              name="receiverName"
              label="Tên người nhận"
              style={{ flex: 1 }}
            >
              <Input />
            </Form.Item>

            <Form.Item
              name="userId"
              label="Nhân viên chi"
              style={{ flex: 1 }}
            >
              <Select placeholder="Chọn nhân viên chi" allowClear>
                {users.map(user => (
                  <Option key={user.id} value={user.id}>{user.fullName}</Option>
                ))}
              </Select>
            </Form.Item>
          </div>

          <div style={{ display: 'flex', gap: '16px' }}>
            {isTransferType() && (
              <Form.Item
                name="bankAccountId"
                label="Tài khoản ngân hàng"
                rules={[{ required: isTransferType(), message: 'Vui lòng chọn tài khoản ngân hàng' }]}
                style={{ flex: 1 }}
              >
                <Space.Compact style={{ width: '100%' }}>
                  <Select 
                    placeholder="Chọn tài khoản ngân hàng" 
                    allowClear
                    style={{ width: 'calc(100% - 40px)' }}
                    value={selectedBankAccountId}
                    onChange={(value) => {
                      setSelectedBankAccountId(value);
                      form.setFieldsValue({ bankAccountId: value });
                    }}
                  >
                    {bankAccounts.map(account => (
                      <Option key={account.id} value={account.id}>
                        {account.accountNumber} - {account.bankName}
                      </Option>
                    ))}
                  </Select>
                  <Button 
                    type="primary" 
                    icon={<PlusOutlined />} 
                    onClick={showAddBankAccountModal}
                    style={{ width: '40px' }}
                  />
                </Space.Compact>
              </Form.Item>
            )}

            <Form.Item
              label="Phiếu nhập liên quan"
              name="importId"
            >
              <Select
                placeholder="Chọn phiếu nhập"
                allowClear
                onChange={handleImportChange}
                style={{ width: '100%' }}
              >
                {imports.map(importItem => (
                  <Option key={importItem.id} value={importItem.id}>
                    {importItem.importCode || `NK${importItem.id}`}
                  </Option>
                ))}
              </Select>
            </Form.Item>
            
            {/* Hiển thị thông tin công nợ của phiếu nhập */}
            {importDebtInfo && (
              <div style={{ 
                marginBottom: 24, 
                padding: 16, 
                background: '#f6ffed', 
                border: '1px solid #b7eb8f',
                borderRadius: 4
              }}>
                <div style={{ fontWeight: 'bold', marginBottom: 8, fontSize: 16, color: '#52c41a' }}>
                  Thông tin công nợ phiếu nhập: {importDebtInfo.importCode}
                </div>
                <div style={{ display: 'flex', flexDirection: 'column', gap: 8 }}>
                  <div>Tổng tiền: <span style={{ fontWeight: 'bold' }}>{new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(importDebtInfo.totalAmount)}</span></div>
                  <div>Đã thanh toán: <span style={{ fontWeight: 'bold' }}>{new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(importDebtInfo.paidAmount)}</span></div>
                  <div>Còn nợ: <span style={{ fontWeight: 'bold', color: importDebtInfo.debtAmount > 0 ? '#f5222d' : '#52c41a' }}>{new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(importDebtInfo.debtAmount)}</span></div>
                </div>
              </div>
            )}
          </div>

          <Form.Item
            name="note"
            label="Ghi chú"
          >
            <TextArea rows={4} />
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading}>
              {isEdit ? 'Cập nhật' : 'Tạo phiếu chi'}
            </Button>
            <Button style={{ marginLeft: 8 }} onClick={() => navigate('/payments')}>
              Hủy
            </Button>
          </Form.Item>
        </Form>
      </Card>

      {/* Modal thêm nhà cung cấp mới */}
      <Modal
        title="Thêm nhà cung cấp mới"
        open={supplierModalVisible}
        onOk={handleAddSupplierModalOk}
        onCancel={handleAddSupplierModalCancel}
        width={800}
        confirmLoading={loading}
      >
        <Form
          form={supplierForm}
          layout="vertical"
          initialValues={{ isActive: true }}
        >
          <div style={{ display: 'flex', gap: '16px' }}>
            <div style={{ flex: 1 }}>
              <Form.Item
                name="supplierCode"
                label="Mã nhà cung cấp"
                rules={[{ required: true, message: 'Vui lòng nhập mã nhà cung cấp' }]}
              >
                <Input />
              </Form.Item>

              <Form.Item
                name="name"
                label="Tên nhà cung cấp"
                rules={[{ required: true, message: 'Vui lòng nhập tên nhà cung cấp' }]}
              >
                <Input />
              </Form.Item>

              <Form.Item
                name="contactPerson"
                label="Người liên hệ"
              >
                <Input />
              </Form.Item>

              <Form.Item
                name="phone"
                label="Số điện thoại"
              >
                <Input />
              </Form.Item>

              <Form.Item
                name="email"
                label="Email"
                rules={[
                  { type: 'email', message: 'Email không hợp lệ' },
                ]}
              >
                <Input />
              </Form.Item>

              <Form.Item
                name="taxCode"
                label="Mã số thuế"
              >
                <Input />
              </Form.Item>
            </div>

            <div style={{ flex: 1 }}>
              <Form.Item
                name="address"
                label="Địa chỉ"
              >
                <Input />
              </Form.Item>

              <Form.Item
                name="area"
                label="Khu vực"
              >
                <Input />
              </Form.Item>

              <Form.Item
                name="ward"
                label="Phường xã"
              >
                <Input />
              </Form.Item>

              <Form.Item
                name="branchId"
                label="Chi nhánh"
              >
                <Select placeholder="Chọn chi nhánh" allowClear>
                  {branches.map(branch => (
                    <Option key={branch.id} value={branch.id}>{branch.name}</Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                name="note"
                label="Ghi chú"
              >
                <TextArea rows={3} />
              </Form.Item>

              <Form.Item
                name="isActive"
                label="Trạng thái"
                valuePropName="checked"
              >
                <Switch checkedChildren="Hoạt động" unCheckedChildren="Không hoạt động" defaultChecked />
              </Form.Item>
            </div>
          </div>
        </Form>
      </Modal>

      {/* Modal thêm tài khoản ngân hàng mới */}
      <Modal
        title="Thêm tài khoản ngân hàng mới"
        open={bankAccountModalVisible}
        onOk={handleAddBankAccountModalOk}
        onCancel={handleAddBankAccountModalCancel}
        width={600}
        confirmLoading={loading}
      >
        <Form
          form={bankAccountForm}
          layout="vertical"
          initialValues={{ type: 'company', isActive: true }}
        >
          <Form.Item
            name="accountNumber"
            label="Số tài khoản"
            rules={[{ required: true, message: 'Vui lòng nhập số tài khoản' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="bankName"
            label="Ngân hàng"
            rules={[{ required: true, message: 'Vui lòng nhập tên ngân hàng' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="accountHolder"
            label="Chủ tài khoản"
            rules={[{ required: true, message: 'Vui lòng nhập tên chủ tài khoản' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="branch"
            label="Chi nhánh ngân hàng"
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="type"
            label="Loại tài khoản"
            rules={[{ required: true, message: 'Vui lòng chọn loại tài khoản' }]}
          >
            <Select>
              <Option value="company">Công ty</Option>
              <Option value="personal">Cá nhân</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="note"
            label="Ghi chú"
          >
            <TextArea rows={4} />
          </Form.Item>

          <Form.Item
            name="isActive"
            label="Trạng thái"
            valuePropName="checked"
          >
            <Switch checkedChildren="Hoạt động" unCheckedChildren="Không hoạt động" defaultChecked />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default PaymentForm;
