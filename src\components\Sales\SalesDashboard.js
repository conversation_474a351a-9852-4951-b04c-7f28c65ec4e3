import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Input, Button, Table, Typography, Select, InputNumber, Form, message, Pagination, Tag, Divider, Badge, Avatar, Space, Empty } from 'antd';
import { SearchOutlined, ShoppingCartOutlined, UserOutlined, DeleteOutlined, SaveOutlined, PrinterOutlined, PlusOutlined, MinusOutlined, CreditCardOutlined, AppstoreOutlined, CheckCircleOutlined } from '@ant-design/icons';
import ProductService from '../../services/product.service';
import CustomerService from '../../services/customer.service';
import InvoiceService from '../../services/invoice.service';
import { formatCurrencyWithSymbol } from '../../utils/format';
import './SalesPrintStyles.css';
import { Toaster, toast } from 'react-hot-toast';

const { Title, Text } = Typography;
const { Option } = Select;
const { Search } = Input;

const SalesDashboard = () => {
  const [form] = Form.useForm();
  const [products, setProducts] = useState([]);
  const [filteredProducts, setFilteredProducts] = useState([]);
  const [customers, setCustomers] = useState([]);
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [cartItems, setCartItems] = useState([]);
  const [loading, setLoading] = useState(false);
  const [productSearchText, setProductSearchText] = useState('');
  const [customerSearchText, setCustomerSearchText] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalAmount, setTotalAmount] = useState(0);
  const [totalItems, setTotalItems] = useState(0);
  const pageSize = 12;

  useEffect(() => {
    fetchProducts();
    fetchCustomers();
  }, []);

  useEffect(() => {
    // Tính tổng tiền và số lượng khi giỏ hàng thay đổi
    const total = cartItems.reduce((sum, item) => sum + (item.quantity * item.price), 0);
    const itemCount = cartItems.reduce((sum, item) => sum + item.quantity, 0);
    setTotalAmount(total);
    setTotalItems(itemCount);
  }, [cartItems]);

  useEffect(() => {
    // Lọc sản phẩm khi có tìm kiếm
    if (products.length > 0) {
      const filtered = products.filter(product =>
        product.name.toLowerCase().includes(productSearchText.toLowerCase()) ||
        product.productCode.toLowerCase().includes(productSearchText.toLowerCase())
      );
      setFilteredProducts(filtered);
    }
  }, [productSearchText, products]);

  const fetchProducts = async () => {
    setLoading(true);
    try {
      const response = await ProductService.getAllProducts();

      // Đảm bảo tất cả sản phẩm đều có giá
      const productsWithPrice = response.data.map(product => {
        // Nếu không có sellingPrice hoặc price, gán giá mặc định là 0
        if (!product.sellingPrice && !product.price) {
          console.warn(`Sản phẩm ${product.name} (ID: ${product.id}) không có giá`);
          return { ...product, price: 0 };
        }
        return product;
      });

      // Log một vài sản phẩm để kiểm tra
      if (productsWithPrice.length > 0) {
        // Su1ea3n phu1ea9m u0111u1ea7u tiu00ean u0111u1ec3 tham khu1ea3o
      }

      setProducts(productsWithPrice);
      setFilteredProducts(productsWithPrice);
    } catch (error) {
      toast.error('Không thể tải danh sách sản phẩm: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const fetchCustomers = async () => {
    try {
      const response = await CustomerService.getAllCustomers();
      setCustomers(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách khách hàng: ' + error.message);
    }
  };

  const handleProductSearch = (value) => {
    setProductSearchText(value);
  };

  const handleCustomerSearch = (value) => {
    setCustomerSearchText(value);
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handleAddToCart = (product) => {
    // Kiểm tra xem sản phẩm đã có trong giỏ hàng chưa
    const existingItemIndex = cartItems.findIndex(item => item.productId === product.id);

    // Lấy giá sản phẩm (ưu tiên sellingPrice, nếu không có thì dùng price)
    const productPrice = product.sellingPrice || product.price || 0;
    
    // Lấy số lượng sản phẩm trong kho
    const stockQuantity = product.stock || 0;
    
    if (existingItemIndex >= 0) {
      // Nếu đã có, kiểm tra số lượng trước khi tăng
      const updatedItems = [...cartItems];
      const currentQuantity = updatedItems[existingItemIndex].quantity;
      
      // Kiểm tra nếu số lượng hiện tại đã đạt tối đa
      if (currentQuantity >= stockQuantity) {
        toast('Không thể thêm quá ' + stockQuantity + ' sản phẩm ' + product.name + ' (đã hết hàng trong kho)', { icon: '⚠️' });
        return;
      }
      
      updatedItems[existingItemIndex].quantity += 1;
      updatedItems[existingItemIndex].amount = updatedItems[existingItemIndex].quantity * updatedItems[existingItemIndex].price;
      setCartItems(updatedItems);
    } else {
      // Nếu chưa có, kiểm tra xem có hàng trong kho không
      if (stockQuantity <= 0) {
        toast('Sản phẩm ' + product.name + ' đã hết hàng trong kho', { icon: '⚠️' });
        return;
      }
      
      // Thêm mới vào giỏ hàng
      setCartItems([...cartItems, {
        productId: product.id,
        productCode: product.productCode || product.code,
        productName: product.name,
        price: productPrice,
        quantity: 1,
        amount: productPrice,
        maxQuantity: stockQuantity // Lưu số lượng tối đa có thể mua
      }]);
    }

    // Thu00eam su1ea3n phu1ea9m vu00e0o giu1ecf hu00e0ng thu00e0nh cu00f4ng
  };

  const handleRemoveFromCart = (index) => {
    const updatedItems = [...cartItems];
    updatedItems.splice(index, 1);

    setCartItems(updatedItems);
  };

  const handleQuantityChange = (value, index) => {
    if (value > 0) {
      const updatedItems = [...cartItems];
      const item = updatedItems[index];
      
      // Lấy số lượng tối đa từ item.maxQuantity hoặc tìm lại sản phẩm trong danh sách sản phẩm
      let maxQuantity = item.maxQuantity;
      if (!maxQuantity) {
        // Nếu không có maxQuantity, tìm sản phẩm trong danh sách sản phẩm
        const product = products.find(p => p.id === item.productId);
        maxQuantity = product ? (product.stock || 0) : 0;
        // Lưu lại để sử dụng sau này
        item.maxQuantity = maxQuantity;
      }
      
      // Kiểm tra nếu số lượng vượt quá số lượng trong kho
      if (value > maxQuantity) {
        toast('Không thể thêm quá ' + maxQuantity + ' sản phẩm ' + item.productName + ' (số lượng trong kho còn lại)', { icon: '⚠️' });
        value = maxQuantity;
      }
      
      updatedItems[index].quantity = value;
      updatedItems[index].amount = value * updatedItems[index].price;
      setCartItems(updatedItems);
    }
  };

  const handleCustomerSelect = (value) => {
    const customer = customers.find(c => c.id === value);
    setSelectedCustomer(customer);
    form.setFieldsValue({ customerId: value });
  };

  const handleCreateInvoice = async () => {
    if (cartItems.length === 0) {
      toast.error('Vui lòng thêm ít nhất một sản phẩm vào hóa đơn');
      return;
    }

    if (!selectedCustomer) {
      toast.error('Vui lòng chọn khách hàng');
      return;
    }

    setLoading(true);
    try {
      // Chuẩn bị dữ liệu hóa đơn - thử nhiều trạng thái khác nhau
      const invoiceData = {
        customerId: selectedCustomer.id,
        items: cartItems.map(item => ({
          productId: item.productId,
          quantity: parseInt(item.quantity) || 1,  // Đảm bảo quantity là số nguyên
          price: parseFloat(item.price) || 0,      // Đảm bảo price là số
          amount: parseFloat(item.quantity * item.price) || 0  // Đảm bảo amount là số
        })),
        totalAmount: parseFloat(totalAmount) || 0,  // Đảm bảo là số
        status: 'completed',                       // Thử trạng thái 'completed' thay vì 'pending' hoặc 'paid'
        paidAmount: parseFloat(totalAmount) || 0   // Thiết lập số tiền đã thanh toán bằng tổng số tiền
      };

      // Tạo hóa đơn với trạng thái 'completed' và đã thanh toán
      const response = await InvoiceService.createInvoice(invoiceData);
      const createdInvoice = response.data;
      
      // Cập nhật số lượng tồn kho của các sản phẩm đã bán
      try {
        // Cập nhật tồn kho cho từng sản phẩm trong giỏ hàng
        for (const item of cartItems) {
          // Lấy thông tin sản phẩm hiện tại
          const productResponse = await ProductService.getProductById(item.productId);
          const product = productResponse.data;
          
          // Tính toán số lượng tồn kho mới
          const currentStock = product.stock || 0;
          const soldQuantity = parseInt(item.quantity) || 0;
          const newStock = Math.max(0, currentStock - soldQuantity); // Đảm bảo tồn kho không âm
          
          // Cập nhật số lượng tồn kho mới
          await ProductService.updateProduct(item.productId, { stock: newStock });

        }
      } catch (stockUpdateError) {
        console.error('Lỗi khi cập nhật tồn kho:', stockUpdateError);
        // Vẫn tiếp tục xử lý, không dừng lại nếu cập nhật tồn kho thất bại
      }
      toast.success(`Thanh toán thành công! Tổng tiền: ${formatCurrencyWithSymbol(totalAmount)}. Đã tạo hóa đơn cho khách hàng: ${selectedCustomer?.name}`);
      setCartItems([]);
      setSelectedCustomer(null);
      form.resetFields();
    } catch (error) {
      toast.error('Không thể tạo hóa đơn: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Chức năng in hóa đơn
  const handlePrintInvoice = () => {
    if (cartItems.length === 0) {
      toast.error('Vui lòng thêm ít nhất một sản phẩm vào hóa đơn');
      return;
    }

    if (!selectedCustomer) {
      toast.error('Vui lòng chọn khách hàng');
      return;
    }

    // Tạo một trang in hoàn toàn mới
    const printWindow = window.open('', '_blank');

    if (!printWindow) {
      toast.error('Trình duyệt đã chặn cửa sổ pop-up. Vui lòng cho phép pop-up và thử lại.');
      return;
    }

    // Tạo mã hóa đơn tạm thời
    const tempInvoiceCode = `HD-TẠM-${new Date().getTime().toString().slice(-6)}`;

    // Tạo HTML cho trang in
    const printHtml = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Hóa đơn - ${tempInvoiceCode}</title>
        <style>
          @page {
            size: landscape !important;
            margin: 0.5cm !important;
          }

          body {
            font-family: 'Segoe UI', Arial, sans-serif;
            margin: 0;
            padding: 15px;
            box-sizing: border-box;
            font-size: 14px;
          }

          /* Đảm bảo hoá đơn hiển thị đúng */
          .print-content {
            width: 100%;
            max-width: 100%;
            padding: 0;
            margin: 0;
          }

          /* Tiêu đề hoá đơn */
          .print-header {
            text-align: center;
            margin-bottom: 20px;
          }

          .print-header h3 {
            font-size: 22px;
            margin: 10px 0;
            font-weight: bold;
            text-transform: uppercase;
          }

          .print-header p {
            margin: 4px 0;
            font-size: 13px;
          }

          /* Bảng thông tin */
          table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
          }

          table, th, td {
            border: 1px solid #d9d9d9;
          }

          th, td {
            padding: 8px 10px;
            text-align: left;
            font-size: 13px;
          }

          th {
            background-color: #f5f5f5;
          }

          /* Tổng tiền */
          .total-table td:first-child {
            width: 15%;
            font-weight: bold;
          }

          .divider {
            border-top: 1px solid #d9d9d9;
            margin: 15px 0;
          }
        </style>
      </head>
      <body>
        <div class="print-content">
          <!-- Phần tiêu đề -->
          <div class="print-header">
            <h3>HOÁ ĐƠN BÁN HÀNG</h3>
            <p>Mã hoá đơn tạm: ${tempInvoiceCode}</p>
            <p>Ngày: ${new Date().toLocaleDateString('vi-VN')}</p>
          </div>

          <!-- Phần thông tin khách hàng -->
          <table>
            <tr>
              <td width="15%"><strong>Khách hàng:</strong></td>
              <td colspan="3">${selectedCustomer.name}</td>
            </tr>
            <tr>
              <td><strong>Địa chỉ:</strong></td>
              <td colspan="3">${selectedCustomer.address || 'N/A'}</td>
            </tr>
            <tr>
              <td><strong>Số điện thoại:</strong></td>
              <td colspan="3">${selectedCustomer.phone || 'N/A'}</td>
            </tr>
            <tr>
              <td width="15%"><strong>Trạng thái:</strong></td>
              <td colspan="3">Chưa thanh toán</td>
            </tr>
          </table>

          <div class="divider"></div>

          <!-- Phần bảng sản phẩm -->
          <table>
            <thead>
              <tr>
                <th style="width: 60px;">STT</th>
                <th>Mã SP</th>
                <th>Sản phẩm</th>
                <th>Đơn giá</th>
                <th>Số lượng</th>
                <th>Thành tiền</th>
              </tr>
            </thead>
            <tbody>
              ${cartItems.map((item, index) => `
                <tr>
                  <td>${index + 1}</td>
                  <td>${item.productCode}</td>
                  <td>${item.productName}</td>
                  <td>${formatCurrencyWithSymbol(item.price)}</td>
                  <td>${item.quantity}</td>
                  <td>${formatCurrencyWithSymbol(item.quantity * item.price)}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>

          <!-- Phần tổng tiền -->
          <table class="total-table">
            <tr>
              <td width="15%"><strong>Tổng cộng:</strong></td>
              <td width="85%">${formatCurrencyWithSymbol(totalAmount)}</td>
            </tr>
            <tr>
              <td><strong>Đã thanh toán:</strong></td>
              <td>${formatCurrencyWithSymbol(0)}</td>
            </tr>
            <tr>
              <td><strong>Còn lại:</strong></td>
              <td>${formatCurrencyWithSymbol(totalAmount)}</td>
            </tr>
          </table>

          <div class="divider"></div>
          <div class="print-note">
            <strong>Ghi chú:</strong>
            <p>Đây là hóa đơn tạm thời. Vui lòng thanh toán để nhận hóa đơn chính thức.</p>
          </div>
        </div>

        <script>
          // Tự động in khi trang đã tải xong
          window.onload = function() {
            window.print();
            // Sau khi in xong sẽ tự đóng cửa sổ sau 1 giây
            setTimeout(function() {
              window.close();
            }, 1000);
          };
        </script>
      </body>
      </html>
    `;

    // Ghi HTML vào cửa sổ mới
    printWindow.document.open();
    printWindow.document.write(printHtml);
    printWindow.document.close();
  };

  const productColumns = [
    {
      title: 'Mã SP',
      dataIndex: 'productCode',
      key: 'productCode',
      width: 100,
    },
    {
      title: 'Tên sản phẩm',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Giá',
      key: 'price',
      render: (_, record) => formatCurrencyWithSymbol(record.sellingPrice || record.price || 0),
    },
    {
      title: 'Tồn kho',
      dataIndex: 'stock',
      key: 'stock',
      render: (stock) => (
        <Tag color={stock > 0 ? 'green' : 'red'}>
          {stock}
        </Tag>
      ),
    },
    {
      title: '',
      key: 'action',
      render: (_, record) => (
        <Button
          type="primary"
          icon={<ShoppingCartOutlined />}
          size="small"
          onClick={() => handleAddToCart(record)}
          disabled={record.stock <= 0}
        >
          Thêm
        </Button>
      ),
    },
  ];

  const cartColumns = [
    {
      title: 'Mã SP',
      dataIndex: 'productCode',
      key: 'productCode',
      width: 100,
    },
    {
      title: 'Sản phẩm',
      dataIndex: 'productName',
      key: 'productName',
    },
    {
      title: 'Đơn giá',
      dataIndex: 'price',
      key: 'price',
      render: (price) => formatCurrencyWithSymbol(price),
    },
    {
      title: 'Số lượng',
      dataIndex: 'quantity',
      key: 'quantity',
      render: (quantity, record, index) => (
        <InputNumber
          min={1}
          value={quantity}
          onChange={(value) => handleQuantityChange(value, index)}
          style={{ width: 60 }}
        />
      ),
    },
    {
      title: 'Thành tiền',
      key: 'amount',
      render: (_, record) => formatCurrencyWithSymbol(record.quantity * record.price),
    },
    {
      title: '',
      key: 'action',
      render: (_, record, index) => (
        <Button
          type="text"
          danger
          icon={<DeleteOutlined />}
          onClick={() => handleRemoveFromCart(index)}
        />
      ),
    },
  ];

  // Tính toán sản phẩm hiển thị cho trang hiện tại
  const paginatedProducts = filteredProducts.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  // Hàm tạo màu ngẫu nhiên từ một danh sách màu
  const getRandomColor = (index) => {
    const colors = [
      { bg: '#e6f7ff', border: '#91d5ff', header: '#1890ff' }, // Xanh dương nhạt
      { bg: '#f9f0ff', border: '#d3adf7', header: '#722ed1' }, // Tím nhạt
      { bg: '#f6ffed', border: '#b7eb8f', header: '#52c41a' }, // Xanh lá nhạt
      { bg: '#fff7e6', border: '#ffd591', header: '#fa8c16' }, // Cam nhạt
      { bg: '#fff1f0', border: '#ffa39e', header: '#f5222d' }, // Đỏ nhạt
      { bg: '#f0f5ff', border: '#adc6ff', header: '#2f54eb' }, // Xanh dương đậm nhạt
    ];
    return colors[index % colors.length];
  };

  const renderProductCard = (product) => {
    // Lấy màu dựa trên id sản phẩm hoặc index trong mảng
    const productId = product.id || 0;
    const colorScheme = getRandomColor(productId);
    
    return (
      <Card
        hoverable
        style={{ 
          width: '100%', 
          marginBottom: 16, 
          borderRadius: '8px',
          border: `1px solid ${colorScheme.border}`,
          boxShadow: '0 2px 8px rgba(0,0,0,0.09)'
        }}
        styles={{ body: { padding: '12px' } }}
        cover={
          <div style={{ 
            height: 120, 
            display: 'flex', 
            justifyContent: 'center', 
            alignItems: 'center', 
            background: colorScheme.bg,
            borderTopLeftRadius: '8px',
            borderTopRightRadius: '8px',
            borderBottom: `1px solid ${colorScheme.border}`
          }}>
            {product.imageUrl ? (
              <img alt={product.name} src={product.imageUrl} style={{ maxHeight: '100%', maxWidth: '100%' }} />
            ) : (
              <Avatar 
                shape="square" 
                size={80} 
                icon={<AppstoreOutlined />} 
                style={{ 
                  background: colorScheme.header,
                  color: 'white'
                }}
              />
            )}
          </div>
        }
        onClick={() => handleAddToCart(product)}
      >
        <Card.Meta 
          title={<div style={{ fontSize: '14px', fontWeight: 'bold', color: '#333' }}>{product.name}</div>}
          description={
            <>
              <div style={{ color: '#f5222d', fontWeight: 'bold', fontSize: '16px', marginTop: '4px' }}>
                {formatCurrencyWithSymbol(product.sellingPrice || product.price || 0)}
              </div>
              <div style={{ 
                color: product.stock > 0 ? '#52c41a' : '#ff4d4f', 
                fontSize: '12px',
                marginTop: '4px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between'
              }}>
                <span>Còn {product.stock || 0} sản phẩm</span>
                <Button 
                  type="link" 
                  size="small" 
                  icon={<ShoppingCartOutlined />} 
                  style={{ padding: 0, color: colorScheme.header }}
                />
              </div>
            </>
          }
        />
      </Card>
    );
  };

  const renderCartItem = (item, index) => {
    return (
      <div key={index} style={{ 
        borderBottom: '1px solid #f0f0f0', 
        padding: '10px 8px', 
        display: 'flex', 
        alignItems: 'center',
        background: index % 2 === 0 ? 'rgba(240, 245, 255, 0.3)' : 'white',
        borderRadius: '4px',
        margin: '4px 0',
        transition: 'all 0.3s ease',
        boxShadow: '0 1px 2px rgba(0,0,0,0.03)'
      }}>
        <div style={{ flex: 1 }}>
          <div style={{ 
            fontWeight: 'bold', 
            fontSize: '14px',
            color: '#333',
            marginBottom: '4px'
          }}>{item.productName || item.name}</div>
          <div style={{ 
            color: themeColors.danger, 
            fontWeight: '500',
            fontSize: '15px'
          }}>{formatCurrencyWithSymbol(item.price)}</div>
        </div>
        <div style={{ display: 'flex', alignItems: 'center', marginRight: 8 }}>
          <Button 
            icon={<MinusOutlined />} 
            size="small"
            style={{
              borderRadius: '4px',
              background: themeColors.lightBg,
              borderColor: '#d9d9d9',
              color: themeColors.primary
            }}
            onClick={() => {
              if (item.quantity > 1) {
                handleQuantityChange(item.quantity - 1, index);
              } else {
                handleRemoveFromCart(index);
              }
            }}
          />
          <InputNumber
            min={1}
            max={item.maxQuantity || 0}
            value={item.quantity}
            onChange={(value) => handleQuantityChange(value, index)}
            style={{ 
              width: 50, 
              margin: '0 8px',
              borderRadius: '4px',
              fontWeight: 'bold'
            }}
          />
          <Button 
            icon={<PlusOutlined />} 
            size="small"
            style={{
              borderRadius: '4px',
              background: item.quantity >= (item.maxQuantity || 0) ? '#f5f5f5' : themeColors.lightBg,
              borderColor: '#d9d9d9',
              color: item.quantity >= (item.maxQuantity || 0) ? '#d9d9d9' : themeColors.primary
            }}
            onClick={() => handleQuantityChange(item.quantity + 1, index)}
            disabled={item.quantity >= (item.maxQuantity || 0)}
          />
        </div>
        <Button 
          type="text" 
          danger 
          icon={<DeleteOutlined />} 
          style={{
            borderRadius: '50%',
            width: '32px',
            height: '32px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            transition: 'all 0.3s'
          }}
          onClick={() => handleRemoveFromCart(index)}
        />
      </div>
    );
  };

  // Màu sắc chính cho giao diện
  const themeColors = {
    primary: '#1890ff',
    success: '#52c41a',
    warning: '#faad14',
    danger: '#f5222d',
    secondary: '#722ed1',
    background: '#f5f5f5',
    headerColor: 'linear-gradient(90deg, #1890ff, #096dd9)', // Màu thống nhất cho tất cả tiêu đề
    paymentButton: 'linear-gradient(90deg, #f5222d, #cf1322)',
    printButton: 'linear-gradient(90deg, #1890ff, #096dd9)',
    lightBg: '#f0f5ff'
  };

  return (
    <div style={{ 
      height: 'calc(100vh - 112px)', 
      display: 'flex', 
      flexDirection: 'column', 
      overflow: 'hidden',
      background: themeColors.background,
      padding: '8px'
    }}>
      <Toaster position="top-right" />
      <Row gutter={16} style={{ height: '100%', overflow: 'hidden' }}>
        <Col span={10} style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
          <Card 
            title={
              <div style={{ 
                fontSize: 16, 
                fontWeight: 'bold', 
                color: 'white', 
                display: 'flex', 
                justifyContent: 'space-between',
                alignItems: 'center'
              }}>
                <span><ShoppingCartOutlined style={{ marginRight: 8 }} />Giỏ hàng</span>
                <Badge 
                  count={totalItems} 
                  style={{ 
                    backgroundColor: themeColors.success, 
                    boxShadow: '0 0 0 2px rgba(82, 196, 26, 0.2)' 
                  }} 
                />
              </div>
            }
            variant="outlined" 
            style={{ 
              flex: 1, 
              display: 'flex', 
              flexDirection: 'column', 
              overflow: 'hidden', 
              marginBottom: 16,
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.09)'
            }}
            styles={{ 
              header: { 
                background: themeColors.headerColor,
                borderTopLeftRadius: '8px',
                borderTopRightRadius: '8px',
                padding: '12px 16px'
              },
              body: { 
                flex: 1, 
                overflow: 'auto', 
                padding: '8px 12px',
                background: 'white'
              }
            }}
          >
            {cartItems.length > 0 ? (
              <div style={{ height: '100%', overflowY: 'auto' }}>
                {cartItems.map((item, index) => renderCartItem(item, index))}
              </div>
            ) : (
              <Empty description="Giỏ hàng trống" image={Empty.PRESENTED_IMAGE_SIMPLE} />
            )}
          </Card>
          
          <Card 
            variant="outlined" 
            style={{ 
              marginBottom: 16, 
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.09)'
            }}
            title={
              <div style={{ 
                fontSize: 16, 
                fontWeight: 'bold', 
                color: 'white',
                display: 'flex',
                alignItems: 'center'
              }}>
                <UserOutlined style={{ marginRight: 8 }} />
                Thông tin khách hàng
              </div>
            }
            styles={{ 
              header: { 
                background: themeColors.headerColor,
                borderTopLeftRadius: '8px',
                borderTopRightRadius: '8px',
                padding: '12px 16px'
              }
            }}
          >
            <Form form={form} layout="vertical">
              <Form.Item 
                name="customerId" 
                label={
                  <span style={{ fontWeight: 'bold', color: '#333' }}>
                    Khách hàng
                  </span>
                }
              >
                <Select
                  showSearch
                  placeholder="Chọn khách hàng"
                  optionFilterProp="children"
                  onChange={handleCustomerSelect}
                  filterOption={(input, option) =>
                    option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                  }
                  style={{ width: '100%', borderRadius: '4px' }}
                  dropdownStyle={{ borderRadius: '4px' }}
                  dropdownRender={menu => (
                    <div>
                      {menu}
                      <Divider style={{ margin: '4px 0' }} />
                      <div style={{ display: 'flex', padding: 8 }}>
                        <Input
                          placeholder="Tìm khách hàng..."
                          value={customerSearchText}
                          onChange={(e) => handleCustomerSearch(e.target.value)}
                          style={{ flex: 'auto', borderRadius: '4px' }}
                          prefix={<SearchOutlined style={{ color: themeColors.primary }} />}
                        />
                      </div>
                    </div>
                  )}
                >
                  {customers
                    .filter(customer =>
                      customer.name.toLowerCase().includes(customerSearchText.toLowerCase()) ||
                      (customer.phone && customer.phone.includes(customerSearchText))
                    )
                    .map(customer => (
                      <Option key={customer.id} value={customer.id}>
                        {customer.name} {customer.phone ? `- ${customer.phone}` : ''}
                      </Option>
                    ))
                  }
                </Select>
              </Form.Item>
            </Form>

            {selectedCustomer && (
              <div style={{ 
                marginTop: 12, 
                marginBottom: 12, 
                background: 'rgba(240, 245, 255, 0.6)',
                padding: '12px',
                borderRadius: '8px',
                border: '1px solid #e6f7ff',
                boxShadow: '0 2px 6px rgba(24, 144, 255, 0.1)'
              }}>
                <p style={{ 
                  fontWeight: 'bold', 
                  fontSize: '16px', 
                  color: '#333',
                  marginBottom: '8px'
                }}>
                  <UserOutlined style={{ color: themeColors.primary, marginRight: 8 }} /> 
                  {selectedCustomer.name}
                </p>
                <p style={{ margin: '4px 0', color: '#333' }}>
                  <span style={{ marginRight: 8, opacity: 0.7 }}>SĐT:</span> 
                  {selectedCustomer.phone || 'N/A'}
                </p>
                <p style={{ margin: '4px 0', color: '#333' }}>
                  <span style={{ marginRight: 8, opacity: 0.7 }}>Địa chỉ:</span> 
                  {selectedCustomer.address || 'N/A'}
                </p>
              </div>
            )}

            <div style={{ 
              marginTop: 16, 
              borderTop: '1px solid #e6f7ff', 
              paddingTop: 16,
              background: 'rgba(240, 245, 255, 0.4)',
              padding: '16px',
              borderRadius: '8px'
            }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8, alignItems: 'center' }}>
                <Text style={{ fontSize: '14px', color: '#333' }}>Tổng số lượng:</Text>
                <Text strong style={{ fontSize: '14px', color: themeColors.secondary }}>
                  <Badge count={totalItems} showZero style={{ backgroundColor: themeColors.secondary, fontWeight: 'bold', marginRight: 4 }} />
                  sản phẩm
                </Text>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Title level={4} style={{ margin: 0, color: '#333' }}>Tổng tiền:</Title>
                <Title level={3} style={{ margin: 0, color: themeColors.danger, fontWeight: 'bold' }}>
                  {formatCurrencyWithSymbol(totalAmount)}
                </Title>
              </div>
            </div>

            <div style={{ marginTop: 24 }}>
              <Button
                type="primary"
                icon={<CreditCardOutlined />}
                size="large"
                block
                onClick={handleCreateInvoice}
                loading={loading}
                disabled={cartItems.length === 0 || !selectedCustomer}
                style={{ 
                  height: '50px', 
                  fontSize: '18px', 
                  fontWeight: 'bold',
                  background: themeColors.paymentButton,
                  borderColor: themeColors.danger,
                  borderRadius: '8px',
                  boxShadow: '0 4px 12px rgba(245, 34, 45, 0.2)',
                  opacity: (cartItems.length === 0 || !selectedCustomer) ? 0.6 : 1
                }}
              >
                THANH TOÁN
              </Button>

              <Button
                icon={<PrinterOutlined />}
                style={{ 
                  marginTop: 12,
                  height: '40px',
                  borderRadius: '8px',
                  borderColor: themeColors.primary,
                  color: themeColors.primary
                }}
                block
                onClick={handlePrintInvoice}
                disabled={cartItems.length === 0 || !selectedCustomer}
              >
                In hóa đơn
              </Button>
            </div>
          </Card>
        </Col>

        <Col span={14} style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
          <Card 
            title={
              <div style={{ 
                fontSize: 16, 
                fontWeight: 'bold', 
                color: 'white', 
                display: 'flex',
                alignItems: 'center'
              }}>
                <AppstoreOutlined style={{ marginRight: 8 }} />
                Danh sách sản phẩm
              </div>
            }
            variant="outlined"
            style={{ 
              flex: 1, 
              display: 'flex', 
              flexDirection: 'column', 
              overflow: 'hidden',
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.09)'
            }}
            styles={{ 
              header: { 
                background: themeColors.headerColor,
                borderTopLeftRadius: '8px',
                borderTopRightRadius: '8px',
                padding: '12px 16px'
              },
              body: { 
                flex: 1, 
                overflow: 'auto', 
                padding: 12,
                background: '#fff'
              }
            }}
          >
            <Input
              prefix={<SearchOutlined style={{ color: themeColors.primary }} />}
              placeholder="Tìm kiếm sản phẩm theo tên hoặc mã"
              value={productSearchText}
              onChange={(e) => setProductSearchText(e.target.value)}
              style={{ 
                marginBottom: 16, 
                borderRadius: '8px', 
                padding: '8px 12px', 
                boxShadow: '0 2px 6px rgba(0, 0, 0, 0.05)',
                border: '1px solid #d9d9d9'
              }}
              allowClear
            />
            <div style={{ flex: 1, overflow: 'auto' }}>
              <Row gutter={[16, 16]}>
                {paginatedProducts.map(product => (
                  <Col xs={12} sm={8} md={8} lg={6} xl={6} key={product.id}>
                    {renderProductCard(product)}
                  </Col>
                ))}
              </Row>
            </div>

            <div style={{ marginTop: 16, textAlign: 'right' }}>
              <Pagination
                current={currentPage}
                total={filteredProducts.length}
                pageSize={pageSize}
                onChange={handlePageChange}
                size="small"
              />
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default SalesDashboard;
