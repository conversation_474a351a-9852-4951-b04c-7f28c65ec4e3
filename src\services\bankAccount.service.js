import api from './api';

const BankAccountService = {
  getAllBankAccounts: async () => {
    return api.get('/bank-accounts');
  },

  getBankAccountById: async (id) => {
    return api.get(`/bank-accounts/${id}`);
  },

  createBankAccount: async (bankAccountData) => {
    return api.post('/bank-accounts', bankAccountData);
  },

  updateBankAccount: async (id, bankAccountData) => {
    return api.put(`/bank-accounts/${id}`, bankAccountData);
  },

  deleteBankAccount: async (id) => {
    return api.delete(`/bank-accounts/${id}`);
  }
};

export default BankAccountService;
