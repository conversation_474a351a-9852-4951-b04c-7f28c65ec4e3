import React, { useState, useEffect } from 'react';
import { Table, Button, Space, Popconfirm, Typography, Input, Card, Tag, Modal } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, SearchOutlined } from '@ant-design/icons';
import { Link } from 'react-router-dom';
import { Toaster, toast } from 'react-hot-toast';
import BrandService from '../../services/brand.service';

const { Title } = Typography;

const BrandList = () => {
  const [brands, setBrands] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');

  useEffect(() => {
    fetchBrands();
  }, []);

  const fetchBrands = async () => {
    setLoading(true);
    try {
      const response = await BrandService.getAllBrands();
      setBrands(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách thương hiệu: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    try {
      // Kiểm tra xem thương hiệu có đang được sử dụng bởi sản phẩm nào không
      const response = await BrandService.checkBrandInUse(id);
      
      // Nếu API trả về thông tin sản phẩm đang sử dụng thương hiệu
      if (response.data && response.data.inUse === true) {
        // Chỉ hiển thị thông báo lỗi khi không thể xóa thương hiệu
        toast.error(`Không thể xóa thương hiệu! Đang được sử dụng bởi ${response.data.count || 'nhiều'} sản phẩm.`);
        return;
      }
      
      // Nếu thương hiệu không được sử dụng, tiến hành xóa
      await BrandService.deleteBrand(id);
      toast.success('Xóa thương hiệu thành công');
      fetchBrands();
    } catch (error) {
      // Nếu API checkBrandInUse không tồn tại hoặc có lỗi khác
      // Thử xóa trực tiếp và xử lý lỗi từ backend
      try {
        await BrandService.deleteBrand(id);
        toast.success('Xóa thương hiệu thành công');
        fetchBrands();
      } catch (deleteError) {
        // Kiểm tra nếu lỗi là do ràng buộc khóa ngoại
        if (deleteError.message.includes('400') || deleteError.message.includes('foreign key constraint') || deleteError.message.includes('in use')) {
          // Chỉ hiển thị thông báo lỗi khi không thể xóa thương hiệu
          toast.error('Không thể xóa thương hiệu! Đang được sử dụng bởi các sản phẩm.');
        } else {
          toast.error('Không thể xóa thương hiệu: ' + deleteError.message);
        }
      }
    }
  };

  const filteredBrands = brands.filter(
    (brand) =>
      brand.name?.toLowerCase().includes(searchText.toLowerCase()) ||
      brand.description?.toLowerCase().includes(searchText.toLowerCase())
  );

  const columns = [
    {
      title: <span style={{ fontSize: 16 }}>Tên thương hiệu</span>,
      dataIndex: 'name',
      key: 'name',
      sorter: (a, b) => a.name.localeCompare(b.name),
      render: (text) => <span style={{ fontSize: '16px' }}>{text}</span>
    },
    {
      title: <span style={{ fontSize: 16 }}>Mô tả</span>,
      dataIndex: 'description',
      key: 'description',
      render: (text) => <span style={{ fontSize: '16px' }}>{text}</span>
    },
    {
      title: <span style={{ fontSize: 16 }}>Trạng thái</span>,
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'} style={{ fontSize: '14px', padding: '2px 10px' }}>
          {isActive ? 'Hoạt động' : 'Không hoạt động'}
        </Tag>
      ),
    },
    {
      title: <span style={{ fontSize: 16 }}>Thao tác</span>,
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Link to={`/brands/edit/${record.id}`}>
            <Button 
              type="primary" 
              icon={<EditOutlined />} 
              style={{ fontSize: 16, height: 40, padding: '0 15px', display: 'flex', alignItems: 'center' }}
            >
              Sửa
            </Button>
          </Link>
          <Popconfirm
            title="Bạn có chắc chắn muốn xóa thương hiệu này?"
            onConfirm={() => handleDelete(record.id)}
            okText="Có"
            cancelText="Không"
          >
            <Button 
              type="primary" 
              danger 
              icon={<DeleteOutlined />} 
              style={{ fontSize: 16, height: 40, padding: '0 15px', display: 'flex', alignItems: 'center' }}
            >
              Xóa
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ fontSize: '16px' }}> {/* Tăng kích thước phông chữ cơ bản thêm 2px */}
      <Toaster position="top-right" />
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 20 }}>
        <Title level={2} style={{ fontSize: 28, background: 'linear-gradient(90deg, #1890ff, #096dd9)', WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent' }}>Quản lý thương hiệu</Title>
        <Link to="/brands/add">
          <Button 
            type="primary" 
            icon={<PlusOutlined />} 
            size="large" 
            style={{ height: '40px', fontWeight: 'bold', fontSize: '16px', padding: '0 20px' }}
          >
            Thêm thương hiệu
          </Button>
        </Link>
      </div>

      <Card style={{ marginBottom: 20 }}>
        <Input
          placeholder="Tìm kiếm thương hiệu..."
          prefix={<SearchOutlined style={{ fontSize: '18px' }} />}
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          style={{ width: 350, height: '42px', fontSize: '16px' }}
        />
      </Card>

      <Table
        columns={columns}
        dataSource={filteredBrands}
        rowKey="id"
        loading={loading}
        pagination={{ pageSize: 10, style: { fontSize: 16 } }}
        style={{ fontSize: '16px' }}
      />
    </div>
  );
};

export default BrandList;
