import React, { useState, useEffect } from 'react';
import { Table, Button, Space, Popconfirm, Typography, Input, Card, Tag, Modal } from 'antd';
import { Toaster, toast } from 'react-hot-toast';
import { PlusOutlined, EditOutlined, DeleteOutlined, SearchOutlined, PrinterOutlined } from '@ant-design/icons';
import { Link } from 'react-router-dom';
import Barcode from 'react-barcode';
import ProductService from '../../services/product.service';
import ProductCategoryService from '../../services/productCategory.service';
import BrandService from '../../services/brand.service';
import { formatCurrencyWithSymbol } from '../../utils/format';
import './PrintBarcode.css';

const { Title } = Typography;

const ProductList = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [printModalVisible, setPrintModalVisible] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [labelCount, setLabelCount] = useState(1);

  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    setLoading(true);
    try {
      const [productsResponse, categoriesResponse, brandsResponse] = await Promise.all([
        ProductService.getAllProducts(),
        ProductCategoryService.getAllCategories(),
        BrandService.getAllBrands()
      ]);

      const categoriesMap = {};
      const brandsMap = {};

      if (categoriesResponse.data) {
        categoriesResponse.data.forEach(category => {
          categoriesMap[category.id] = category.name;
        });
      }

      if (brandsResponse.data) {
        brandsResponse.data.forEach(brand => {
          brandsMap[brand.id] = brand.name;
        });
      }

      const productsWithDetails = productsResponse.data.map(product => {

        const categoryName = categoriesMap[product.categoryId];
        const category = categoryName ? { name: categoryName } : null;

        // Thêm thông tin thương hiệu
        const brandName = brandsMap[product.brandId];
        const brand = brandName ? { name: brandName } : null;

        return {
          ...product,
          category,
          brand
        };
      });

      setProducts(productsWithDetails);
    } catch (error) {
      toast.error('Không thể tải danh sách sản phẩm: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    try {
      await ProductService.deleteProduct(id);
      toast.success('Xóa sản phẩm thành công');
      fetchProducts();
    } catch (error) {
      // Kiểm tra nếu lỗi là do ràng buộc khóa ngoại
      if (error.message.includes('400') || error.message.includes('Bad Request')) {
        // Hiển thị thông báo chi tiết và gợi ý giải pháp
        Modal.confirm({
          title: <span style={{ fontSize: 20 }}>Không thể xóa sản phẩm</span>,
          content: (
            <div style={{ fontSize: 16 }}>
              <p>Sản phẩm này đang được sử dụng trong hóa đơn, phiếu nhập hoặc phiếu trả hàng.</p>
              <p>Thay vì xóa, bạn có thể vô hiệu hóa sản phẩm để không hiển thị trong các giao dịch mới.</p>
              <p>Bạn có muốn vô hiệu hóa sản phẩm này không?</p>
            </div>
          ),
          okText: 'Vô hiệu hóa',
          cancelText: 'Hủy',
          okButtonProps: { style: { fontSize: 16, height: 40 } },
          cancelButtonProps: { style: { fontSize: 16, height: 40 } },
          onOk: () => handleDeactivateProduct(id)
        });
      } else {
        toast.error('Không thể xóa sản phẩm: ' + error.message);
      }
    }
  };

  // Hàm vô hiệu hóa sản phẩm thay vì xóa
  const handleDeactivateProduct = async (id) => {
    try {
      // Lấy thông tin sản phẩm hiện tại
      const response = await ProductService.getProductById(id);
      const product = response.data;

      // Cập nhật trạng thái sản phẩm thành không hoạt động
      await ProductService.updateProduct(id, {
        ...product,
        isActive: false
      });

      toast.success('Đã vô hiệu hóa sản phẩm thành công');
      fetchProducts();
    } catch (error) {
      toast.error('Không thể vô hiệu hóa sản phẩm: ' + error.message);
    }
  };

  const handlePrintLabel = (product) => {
    setSelectedProduct(product);
    setLabelCount(1);
    setPrintModalVisible(true);
  };

  const handlePrint = () => {
    // Lấy thông tin mã vạch
    const barcodeValue = selectedProduct.barcode || selectedProduct.productCode || selectedProduct.code || '0000000000000';
    const productName = selectedProduct.name;
    const productPrice = formatCurrencyWithSymbol(selectedProduct.sellingPrice || selectedProduct.price);
    
    // Tạo một cửa sổ mới để in
    const printWindow = window.open('', '_blank');
    
    // Tạo nội dung HTML cho cửa sổ in
    const printContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>In tem mã vạch</title>
        <style>
          @page {
            size: A4;
            margin: 0;
          }
          body {
            margin: 0;
            padding: 10mm;
            font-family: Arial, sans-serif;
          }
          .barcode-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 5mm;
            margin: 0 auto;
            max-width: 180mm;
          }
          .barcode-item {
            border: 1px dashed #ccc;
            padding: 5mm;
            margin-bottom: 5mm;
            text-align: center;
            break-inside: avoid;
            overflow: hidden;
          }
          .product-name {
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 5px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .price {
            font-size: 10px;
            margin-top: 2px;
          }
          .barcode-container {
            text-align: center;
            margin: 5px 0;
          }
          .barcode-container svg {
            max-width: 100%;
            height: auto;
          }
        </style>
        <script src="https://unpkg.com/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
      </head>
      <body>
        <div class="barcode-grid">
          ${Array(labelCount).fill().map((_, index) => `
            <div class="barcode-item">
              <div class="product-name">${productName}</div>
              <div class="barcode-container">
                <svg id="barcode-${index}"></svg>
              </div>
              <div class="price">Giá: ${productPrice}</div>
            </div>
          `).join('')}
        </div>
        <script>
          window.onload = function() {
            // Tạo mã vạch cho từng tem
            ${Array(labelCount).fill().map((_, index) => `
              JsBarcode("#barcode-${index}", "${barcodeValue}", {
                format: "CODE128",
                width: 1,
                height: 30,
                displayValue: true,
                fontSize: 8,
                margin: 2
              });
            `).join('')}
            
            // In và đóng cửa sổ
            setTimeout(function() {
              window.print();
              setTimeout(function() {
                window.close();
              }, 500);
            }, 300);
          };
        </script>
      </body>
      </html>
    `;
    
    // Ghi nội dung vào cửa sổ in
    printWindow.document.open();
    printWindow.document.write(printContent);
    printWindow.document.close();
  };
  
  // Đóng modal và reset trạng thái
  const handleCloseModal = () => {
    setPrintModalVisible(false);
    setLabelCount(1);
  };

  const handleLabelCountChange = (e) => {
    const value = parseInt(e.target.value);
    if (value > 0) {
      setLabelCount(value);
    }
  };

  const filteredProducts = products.filter(
    (product) =>
      product.name?.toLowerCase().includes(searchText.toLowerCase()) ||
      product.productCode?.toLowerCase().includes(searchText.toLowerCase()) ||
      product.code?.toLowerCase().includes(searchText.toLowerCase()) ||
      product.barcode?.toLowerCase().includes(searchText.toLowerCase()) ||
      product.brand?.name?.toLowerCase().includes(searchText.toLowerCase()) ||
      product.category?.name?.toLowerCase().includes(searchText.toLowerCase())
  );

  const columns = [
    {
      title: <span style={{ fontSize: 16 }}>Mã SP</span>,
      dataIndex: 'productCode',
      key: 'productCode',
      width: 100,
      render: (text, record) => text || record.code,
    },
    {
      title: <span style={{ fontSize: 16 }}>Mã vạch</span>,
      dataIndex: 'barcode',
      key: 'barcode',
      width: 120,
    },
    {
      title: <span style={{ fontSize: 16 }}>Tên sản phẩm</span>,
      dataIndex: 'name',
      key: 'name',
      sorter: (a, b) => a.name.localeCompare(b.name),
    },
    {
      title: <span style={{ fontSize: 16 }}>Nhóm hàng</span>,
      key: 'category',
      render: (_, record) => {
        if (record.category && record.category.name) {
          return record.category.name;
        }
        if (record.Category && record.Category.name) {
          return record.Category.name;
        }
        return 'N/A';
      },
    },
    {
      title: <span style={{ fontSize: 16 }}>Thương hiệu</span>,
      key: 'brand',
      render: (_, record) => {
        if (record.brand && record.brand.name) {
          return record.brand.name;
        }
        if (record.Brand && record.Brand.name) {
          return record.Brand.name;
        }
        return 'N/A';
      },
    },
    {
      title: <span style={{ fontSize: 16 }}>Giá vốn</span>,
      dataIndex: 'costPrice',
      key: 'costPrice',
      render: (price) => formatCurrencyWithSymbol(price),
      sorter: (a, b) => a.costPrice - b.costPrice,
    },
    {
      title: <span style={{ fontSize: 16 }}>Giá bán</span>,
      dataIndex: 'sellingPrice',
      key: 'sellingPrice',
      render: (price, record) => formatCurrencyWithSymbol(price || record.price),
      sorter: (a, b) => (a.sellingPrice || a.price) - (b.sellingPrice || b.price),
    },
    {
      title: <span style={{ fontSize: 16 }}>Tồn kho</span>,
      dataIndex: 'stock',
      key: 'stock',
      render: (stock) => (
        <Tag color={stock > 0 ? 'green' : 'red'} style={{ fontSize: '16px', padding: '4px 12px' }}>
          {stock}
        </Tag>
      ),
      sorter: (a, b) => a.stock - b.stock,
    },
    {
      title: <span style={{ fontSize: 16 }}>Trạng thái</span>,
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'} style={{ fontSize: '14px', padding: '2px 10px' }}>
          {isActive ? 'Hoạt động' : 'Không hoạt động'}
        </Tag>
      ),
    },
    {
      title: <span style={{ fontSize: 16 }}>Thao tác</span>,
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Link to={`/products/edit/${record.id}`}>
            <Button 
              type="primary" 
              icon={<EditOutlined />} 
              style={{ fontSize: 16, height: 40, padding: '0 15px', display: 'flex', alignItems: 'center' }}
            >
              Sửa
            </Button>
          </Link>
          <Button
            type="default"
            icon={<PrinterOutlined />}
            style={{ fontSize: 16, height: 40, padding: '0 15px', display: 'flex', alignItems: 'center' }}
            onClick={() => handlePrintLabel(record)}
          >
            In tem
          </Button>
          {record.isActive && (
            <Popconfirm
              title="Bạn có chắc chắn muốn xóa sản phẩm này?"
              onConfirm={() => handleDelete(record.id)}
              okText="Có"
              cancelText="Không"
            >
              <Button 
                type="primary" 
                danger 
                icon={<DeleteOutlined />} 
                style={{ fontSize: 16, height: 40, padding: '0 15px', display: 'flex', alignItems: 'center' }}
              >
                Xóa
              </Button>
            </Popconfirm>
          )}
          {!record.isActive && (
            <Button
              type="default"
              icon={<DeleteOutlined />}
              style={{ fontSize: 16, height: 40, padding: '0 15px', display: 'flex', alignItems: 'center' }}
              disabled
              title="Sản phẩm đã bị vô hiệu hóa"
            >
              Đã vô hiệu hóa
            </Button>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div style={{ fontSize: '16px' }}> {/* Tăng kích thước phông chữ cơ bản thêm 2px */}
      <Toaster position="top-right" />
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 20 }}>
        <Title level={2} style={{ fontSize: 28, background: 'linear-gradient(90deg, #1890ff, #096dd9)', WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent' }}>Quản lý sản phẩm</Title>
        <Link to="/products/add">
          <Button 
            type="primary" 
            icon={<PlusOutlined />} 
            size="large" 
            style={{ height: '40px', fontWeight: 'bold', fontSize: '16px', padding: '0 20px' }}
          >
            Thêm sản phẩm
          </Button>
        </Link>
      </div>

      <Card style={{ marginBottom: 20 }}>
        <Input
          placeholder="Tìm kiếm sản phẩm..."
          prefix={<SearchOutlined style={{ fontSize: '18px' }} />}
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          style={{ width: 350, height: '42px', fontSize: '16px' }}
        />
      </Card>

      <Table
        columns={columns}
        dataSource={filteredProducts}
        rowKey="id"
        loading={loading}
        pagination={{ pageSize: 10, style: { fontSize: 16 } }}
        style={{ fontSize: '16px' }}
      />

      <Modal
        title={<span style={{ fontSize: 18 }}>In mã vạch</span>}
        open={printModalVisible}
        onCancel={handleCloseModal}
        footer={
          <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '8px' }}>
            <Button 
              key="cancel" 
              onClick={handleCloseModal}
              style={{ fontSize: 16, height: 40, padding: '0 15px' }}
            >
              Hủy
            </Button>
            <Button 
              key="print" 
              type="primary" 
              onClick={handlePrint}
              style={{ fontSize: 16, height: 40, padding: '0 15px' }}
            >
              In
            </Button>
          </div>
        }
        width={600}
      >
        {selectedProduct && (
          <>
            <div style={{ marginBottom: '15px' }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <span style={{ marginRight: '10px', fontSize: '16px' }}>Số lượng tem:</span>
                <Input
                  type="number"
                  min={1}
                  value={labelCount}
                  onChange={handleLabelCountChange}
                  style={{ width: '120px', height: '38px', fontSize: '16px' }}
                />
              </div>
            </div>

            {/* Phần xem trước - chỉ hiển thị một mẫu tem */}
            <div className="preview-container">
              <div style={{ textAlign: 'center', padding: '10px', border: '1px dashed #ccc', marginBottom: '10px', maxWidth: '250px', margin: '0 auto', overflow: 'hidden' }}>
                <div style={{ marginBottom: '5px', fontSize: '14px', fontWeight: 'bold', overflow: 'hidden', whiteSpace: 'nowrap', textOverflow: 'ellipsis' }}>
                  {selectedProduct.name}
                </div>
                <div style={{ overflow: 'hidden' }}>
                  <Barcode
                    value={selectedProduct.barcode || selectedProduct.productCode || selectedProduct.code || '0000000000000'}
                    width={1}
                    height={30}
                    fontSize={8}
                    displayValue={true}
                    margin={2}
                    format="CODE128"
                  />
                </div>
                <div style={{ marginTop: '5px', fontSize: '12px' }}>
                  Giá: {formatCurrencyWithSymbol(selectedProduct.sellingPrice || selectedProduct.price)}
                </div>
              </div>
              <div style={{ marginTop: '10px', fontSize: '12px', color: '#888', textAlign: 'center' }}>
                Xem trước: {labelCount} tem sẽ được in theo lưới 2 cột
              </div>
            </div>
          </>
        )}
      </Modal>
      
      {/* Phần in - tách riêng khỏi modal để không bị ảnh hưởng bởi việc đóng/mở modal */}
      {selectedProduct && (
        <div id="print-container" className="barcode-print-container">
          <div className="barcode-grid">
            {[...Array(labelCount)].map((_, index) => (
              <div key={index} className="barcode-item">
                <div style={{ fontSize: '12px', fontWeight: 'bold', marginBottom: '5px', overflow: 'hidden', whiteSpace: 'nowrap', textOverflow: 'ellipsis' }}>
                  {selectedProduct.name}
                </div>
                <div style={{ overflow: 'hidden' }}>
                  <Barcode
                    value={selectedProduct.barcode || selectedProduct.productCode || selectedProduct.code || '0000000000000'}
                    width={1}
                    height={30}
                    fontSize={8}
                    displayValue={true}
                    margin={2}
                    format="CODE128"
                  />
                </div>
                <div style={{ fontSize: '10px', marginTop: '2px' }}>
                  Giá: {formatCurrencyWithSymbol(selectedProduct.sellingPrice || selectedProduct.price)}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductList;
