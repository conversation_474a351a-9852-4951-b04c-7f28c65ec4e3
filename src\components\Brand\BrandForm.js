import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Card, Switch, Typography, Divider } from 'antd';
import { useNavigate, useParams } from 'react-router-dom';
import { Toaster, toast } from 'react-hot-toast';
import BrandService from '../../services/brand.service';

const { Title } = Typography;
const { TextArea } = Input;

const BrandForm = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const navigate = useNavigate();
  const { id } = useParams();

  useEffect(() => {
    if (id) {
      setIsEdit(true);
      fetchBrandData();
    }
  }, [id]);

  const fetchBrandData = async () => {
    setLoading(true);
    try {
      const response = await BrandService.getBrandById(id);
      const brandData = response.data;
      form.setFieldsValue({
        name: brandData.name,
        description: brandData.description,
        isActive: brandData.isActive,
      });
    } catch (error) {
      toast.error('Không thể tải thông tin thương hiệu: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const onFinish = async (values) => {
    setLoading(true);
    try {
      if (isEdit) {
        await BrandService.updateBrand(id, values);
        toast.success('Cập nhật thương hiệu thành công');
      } else {
        await BrandService.createBrand(values);
        toast.success('Thêm thương hiệu thành công');
      }
      navigate('/brands');
    } catch (error) {
      toast.error('Lỗi: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <Toaster position="top-right" />
      <Title level={2} style={{ 
        background: 'linear-gradient(to right, #1890ff, #52c41a)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        fontSize: '24px',
        fontWeight: 'bold'
      }}>{isEdit ? 'Cập nhật thương hiệu' : 'Thêm thương hiệu mới'}</Title>
      <Divider />

      <Card>
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          style={{ fontSize: '16px' }}
        >
          <Form.Item
            name="name"
            label="Tên thương hiệu"
            rules={[{ required: true, message: 'Vui lòng nhập tên thương hiệu' }]}
          >
            <Input style={{ height: '40px', fontSize: '16px' }} />
          </Form.Item>

          <Form.Item
            name="description"
            label="Mô tả"
          >
            <TextArea rows={4} style={{ fontSize: '16px' }} />
          </Form.Item>

          <Form.Item
            name="isActive"
            label="Trạng thái"
            valuePropName="checked"
          >
            <Switch checkedChildren="Hoạt động" unCheckedChildren="Không hoạt động" defaultChecked />
          </Form.Item>

          <Form.Item>
            <Button 
              type="primary" 
              htmlType="submit" 
              loading={loading}
              style={{ height: '40px', fontSize: '16px' }}
            >
              {isEdit ? 'Cập nhật' : 'Thêm mới'}
            </Button>
            <Button 
              style={{ marginLeft: 8, height: '40px', fontSize: '16px' }} 
              onClick={() => navigate('/brands')}
            >
              Hủy
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default BrandForm;
