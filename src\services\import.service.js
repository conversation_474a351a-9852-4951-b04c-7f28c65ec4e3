import api from './api';

const ImportService = {
  getAllImports: async () => {
    try {
      // Thêm tham số include để lấy thông tin đầy đủ về nhà cung cấp và các mục nhập hàng
      return await api.get('/imports?include=Supplier,ImportItems,ImportItems.Product');
    } catch (error) {
      console.error('Error fetching imports:', error);
      throw error;
    }
  },

  getImportById: async (id) => {
    try {
      // Thêm tham số include để lấy thông tin đầy đủ về nhà cung cấp, chi nh<PERSON>h và các mục nhập hàng
      return await api.get(`/imports/${id}?include=supplier,branch,Branch,ImportItems,ImportItems.Product`);
    } catch (error) {
      console.error(`Error fetching import with ID ${id}:`, error);
      throw error;
    }
  },

  createImport: async (importData) => {
    try {
      return await api.post('/imports', importData);
    } catch (error) {
      console.error('Error creating import:', error);
      console.error('Error response:', error.response?.data);
      throw error;
    }
  },

  updateImport: async (id, importData) => {
    try {
      return await api.put(`/imports/${id}`, importData);
    } catch (error) {
      console.error(`Error updating import with ID ${id}:`, error);
      console.error('Error response:', error.response?.data);
      throw error;
    }
  },

  deleteImport: async (id) => {
    try {
      return await api.delete(`/imports/${id}`);
    } catch (error) {
      console.error(`Error deleting import with ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Lấy danh sách các mục nhập hàng theo ID sản phẩm
   * @param {Number} productId - ID của sản phẩm
   * @returns {Promise<Object>} Danh sách các mục nhập hàng
   */
  getImportItemsByProductId: async (productId) => {
    try {
      // Sử dụng endpoint getAllImports với filter để lọc theo productId
      const response = await api.get('/imports?include=ImportItems,ImportItems.Product');
      const imports = response.data;

      // Lọc các mục nhập hàng có chứa sản phẩm với ID đã cho
      const filteredImports = imports.filter(imp =>
        imp.ImportItems && imp.ImportItems.some(item =>
          item.productId === productId ||
          (item.Product && item.Product.id === productId)
        )
      );

      // Tạo danh sách các mục nhập hàng chỉ chứa sản phẩm với ID đã cho
      const importItems = [];
      filteredImports.forEach(imp => {
        const items = imp.ImportItems.filter(item =>
          item.productId === productId ||
          (item.Product && item.Product.id === productId)
        );

        items.forEach(item => {
          importItems.push({
            ...item,
            Import: imp
          });
        });
      });

      return { data: importItems };
    } catch (error) {
      console.error(`Error fetching import items for product ${productId}:`, error);
      throw error;
    }
  },
  
  /**
   * Cập nhật số tiền đã thanh toán của phiếu nhập
   * @param {Number} importId - ID của phiếu nhập
   * @param {Number} paymentAmount - Số tiền thanh toán
   * @returns {Promise<Object>} Kết quả cập nhật
   */
  updateImportPayment: async (importId, paymentAmount) => {
    try {
      // Gọi API endpoint mới để cập nhật số tiền thanh toán của phiếu nhập
      return await api.patch(`/imports/${importId}/payment`, { paymentAmount });
    } catch (error) {
      console.error(`Error updating payment for import ${importId}:`, error);
      throw error;
    }
  }
};

export default ImportService;
