import React, { useState, useEffect, useCallback } from 'react';
import { useLocation } from 'react-router-dom';
import {
  Card,
  Form,
  Input,
  Button,
  InputNumber,
  Typography,
  Divider,
  Table,
  Space,
  message,
  Tabs,
  Alert,
  Spin,
  Tag,
} from 'antd';
import {
  SaveOutlined,
  SendOutlined,
  InfoCircleOutlined,
  WarningOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import SettingService from '../../services/setting.service';
import ProductService from '../../services/product.service';
import { formatCurrencyWithSymbol } from '../../utils/format';

const { Title, Paragraph } = Typography;
const { TextArea } = Input;

const InventoryNotificationSettings = () => {
  const location = useLocation();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [sending, setSending] = useState(false);
  const [settings, setSettings] = useState({});
  const [agingProducts, setAgingProducts] = useState([]);
  const [lowStockProducts, setLowStockProducts] = useState([]);
  const [outOfStockProducts, setOutOfStockProducts] = useState([]);
  const [loadingProducts, setLoadingProducts] = useState(false);

  // Lấy tab từ query parameter nếu có
  const queryParams = new URLSearchParams(location.search);
  const tabParam = queryParams.get('tab');
  const [activeTab, setActiveTab] = useState(tabParam || '1');

  const fetchInventoryData = useCallback(async (months = 6, threshold = 5) => {
    setLoadingProducts(true);
    try {
      // Lấy danh sách sản phẩm tồn kho lâu, sắp hết hàng và đã hết hàng
      const [agingResponse, lowStockResponse, outOfStockResponse] = await Promise.all([
        ProductService.getAgingInventory(months),
        ProductService.getLowStockProducts(threshold),
        ProductService.getOutOfStockProducts()
      ]);

      // Lấy thêm thông tin nhập hàng gần nhất cho các sản phẩm
      const importService = await import('../../services/import.service');
      const ImportService = importService.default;

      // Lấy danh sách nhập hàng với thông tin chi tiết
      // Sử dụng API riêng để lấy thông tin chi tiết về các mục nhập hàng
      const importItemsPromises = [];
      
      // Lấy tất cả các phiếu nhập một lần để tối ưu số lượng request
      const allImportsResponse = await ImportService.getAllImports();
      const allImports = allImportsResponse.data;

      // Xử lý dữ liệu sản phẩm tồn kho lâu
      for (const product of agingResponse.data) {
        if (product.id) {
          try {
            // Tìm các lần nhập hàng cho sản phẩm này
            const importItems = await ImportService.getImportItemsByProductId(product.id);
            importItemsPromises.push(Promise.resolve(importItems));
          } catch (error) {
            console.error(`Error fetching import items for product ${product.id}:`, error);
            // Nếu API riêng không thành công, tự lọc từ danh sách tất cả phiếu nhập
            const filteredImports = allImports.filter(imp =>
              imp.ImportItems && imp.ImportItems.some(item =>
                item.productId === product.id ||
                (item.Product && item.Product.id === product.id)
              )
            );

            const importItems = [];
            filteredImports.forEach(imp => {
              const items = imp.ImportItems.filter(item =>
                item.productId === product.id ||
                (item.Product && item.Product.id === product.id)
              );

              items.forEach(item => {
                importItems.push({
                  ...item,
                  Import: imp
                });
              });
            });

            importItemsPromises.push(Promise.resolve({ data: importItems }));
          }
        }
      }

      // Chờ tất cả các promise hoàn thành
      const importItemsResponses = await Promise.all(importItemsPromises);

      // Tạo map để lưu trữ ngày nhập hàng gần nhất cho mỗi sản phẩm
      const productLastImportDateMap = {};

      // Xử lý kết quả
      importItemsResponses.forEach((response, index) => {
        const productId = agingResponse.data[index]?.id;
        if (!productId) return;
        
        const importItems = response.data || [];

        if (importItems.length > 0) {
          // Sắp xếp theo ngày nhập hàng mới nhất
          importItems.sort((a, b) => {
            const dateA = a.Import ? new Date(a.Import.importDate) : new Date(0);
            const dateB = b.Import ? new Date(b.Import.importDate) : new Date(0);
            return dateB - dateA;
          });

          // Lấy ngày nhập hàng gần nhất
          const latestImportItem = importItems[0];
          if (latestImportItem.Import && latestImportItem.Import.importDate) {
            productLastImportDateMap[productId] = latestImportItem.Import.importDate;
          }
        }
      });

      // Sử dụng danh sách phiếu nhập đã lấy trước đó
      const imports = allImports;

      const processedAgingProducts = agingResponse.data.map(product => {
        // Kiểm tra xem đã có lastImportDate từ map không
        if (productLastImportDateMap[product.id]) {
          product.lastImportDate = productLastImportDateMap[product.id];
        } else {
          // Nếu không tìm thấy trong map, tự tìm trong danh sách phiếu nhập
          const productImports = imports.filter(imp =>
            imp.ImportItems && imp.ImportItems.some(item =>
              item.productId === product.id ||
              (item.Product && item.Product.id === product.id)
            )
          );

          if (productImports.length > 0) {
            // Sắp xếp theo ngày nhập hàng mới nhất
            productImports.sort((a, b) => {
              const dateA = new Date(a.importDate);
              const dateB = new Date(b.importDate);
              return dateB - dateA;
            });

            // Lấy ngày nhập hàng gần nhất
            product.lastImportDate = productImports[0].importDate;
          }
        }
        return product;
      });

      // Cập nhật state với dữ liệu đã xử lý
      setAgingProducts(processedAgingProducts);
      setLowStockProducts(lowStockResponse.data);
      setOutOfStockProducts(outOfStockResponse.data);
    } catch (error) {
      message.error('Không thể tải dữ liệu sản phẩm: ' + error.message);
    } finally {
      setLoadingProducts(false);
    }
  }, []);

  const fetchSettings = useCallback(async () => {
    setLoading(true);
    try {
      const response = await SettingService.getAllSettings();
      const settingsData = response.data;

      // Chuyển đổi danh sách cài đặt thành đối tượng để dễ truy cập
      const settingsObj = {};
      settingsData.forEach(setting => {
        let value = setting.value;

        // Chuyển đổi giá trị theo kiểu dữ liệu
        if (setting.type === 'number') {
          value = parseFloat(value);
        } else if (setting.type === 'boolean') {
          value = value === 'true';
        } else if (setting.type === 'json') {
          try {
            value = JSON.parse(value);
          } catch (e) {
            console.error('Error parsing JSON setting:', e);
          }
        }

        settingsObj[setting.key] = value;
      });

      setSettings(settingsObj);

      // Cập nhật form với giá trị từ cài đặt
      form.setFieldsValue({
        inventory_aging_months: settingsObj.inventory_aging_months || 6,
        low_stock_threshold: settingsObj.low_stock_threshold || 5,
        email_schedule: settingsObj.email_schedule || '0 8 * * *',
        notification_emails: settingsObj.notification_emails || ''
      });

      // Tải danh sách sản phẩm tồn kho lâu và sắp hết hàng
      fetchInventoryData(settingsObj.inventory_aging_months, settingsObj.low_stock_threshold);
    } catch (error) {
      message.error('Không thể tải cài đặt: ' + error.message);
    } finally {
      setLoading(false);
    }
  }, [form, fetchInventoryData, setLoading, setSettings]);

  // Cập nhật dependencies cho fetchSettings
  useEffect(() => {
    fetchSettings();
  }, [fetchSettings]);

  const onFinish = async (values) => {
    setSaving(true);
    try {
      // Cập nhật các cài đặt
      const settingsToUpdate = [
        {
          key: 'inventory_aging_months',
          value: values.inventory_aging_months.toString(),
          type: 'number'
        },
        {
          key: 'low_stock_threshold',
          value: values.low_stock_threshold.toString(),
          type: 'number'
        },
        {
          key: 'email_schedule',
          value: values.email_schedule,
          type: 'string'
        },
        {
          key: 'notification_emails',
          value: values.notification_emails,
          type: 'string'
        }
      ];

      // Gọi API cập nhật cài đặt
      const promises = settingsToUpdate.map(setting => 
        SettingService.updateSetting(setting.key, setting.value, setting.type)
      );

      await Promise.all(promises);

      // Cập nhật lại danh sách sản phẩm với cài đặt mới
      await fetchInventoryData(values.inventory_aging_months, values.low_stock_threshold);

      message.success('Đã lưu cài đặt thành công');
    } catch (error) {
      message.error('Lỗi khi lưu cài đặt: ' + error.message);
    } finally {
      setSaving(false);
    }
  };

  const handleSendNotification = async () => {
    setSending(true);
    try {
      // Gọi API gửi thông báo
      await SettingService.sendInventoryNotification();
      message.success('Đã gửi thông báo thành công');
    } catch (error) {
      message.error('Lỗi khi gửi thông báo: ' + error.message);
    } finally {
      setSending(false);
    }
  };

  // Table column definitions
  const agingProductColumns = [
    {
      title: 'Mã SP',
      dataIndex: 'productCode',
      key: 'productCode',
    },
    {
      title: 'Tên sản phẩm',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Tồn kho',
      dataIndex: 'stock',
      key: 'stock',
    },
    {
      title: 'Giá vốn',
      dataIndex: 'costPrice',
      key: 'costPrice',
      render: (price) => formatCurrencyWithSymbol(price)
    },
    {
      title: 'Giá bán',
      dataIndex: 'sellingPrice',
      key: 'sellingPrice',
      render: (price) => formatCurrencyWithSymbol(price)
    },
    {
      title: 'Thời gian tồn kho',
      dataIndex: 'monthsInStock',
      key: 'monthsInStock',
      render: (months) => {
        return months ? `${months} tháng` : 'Chưa xác định';
      }
    },
    {
      title: 'Ngày nhập cuối',
      dataIndex: 'lastImportDate',
      key: 'lastImportDate',
      render: (date, record) => {
        if (!date) {
          return (
            <span style={{ color: '#999' }}>
              Chưa có dữ liệu
            </span>
          );
        }

        // Tính số ngày từ lần nhập cuối
        const importDate = new Date(date);
        const today = new Date();
        const diffTime = Math.abs(today - importDate);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        return (
          <span>
            {new Date(date).toLocaleDateString('vi-VN')} 
            <Tag color="blue">{diffDays} ngày trước</Tag>
          </span>
        );
      }
    }
  ];

  const lowStockProductColumns = [
    {
      title: 'Mã SP',
      dataIndex: 'productCode',
      key: 'productCode',
    },
    {
      title: 'Tên sản phẩm',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Tồn kho',
      dataIndex: 'stock',
      key: 'stock',
      render: (stock) => {
        return <Tag color="orange">{stock}</Tag>;
      }
    },
    {
      title: 'Giá vốn',
      dataIndex: 'costPrice',
      key: 'costPrice',
      render: (price) => formatCurrencyWithSymbol(price)
    },
    {
      title: 'Giá bán',
      dataIndex: 'sellingPrice',
      key: 'sellingPrice',
      render: (price) => formatCurrencyWithSymbol(price)
    }
  ];

  const outOfStockProductColumns = [
    {
      title: 'Mã SP',
      dataIndex: 'productCode',
      key: 'productCode',
    },
    {
      title: 'Tên sản phẩm',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Giá vốn',
      dataIndex: 'costPrice',
      key: 'costPrice',
      render: (price) => formatCurrencyWithSymbol(price)
    },
    {
      title: 'Giá bán',
      dataIndex: 'sellingPrice',
      key: 'sellingPrice',
      render: (price) => formatCurrencyWithSymbol(price)
    },
    {
      title: 'Trạng thái',
      key: 'status',
      render: () => {
        return <Tag color="red">Hết hàng</Tag>;
      }
    }
  ];

  return (
    <div>
      <Title level={2}>Cài đặt thông báo tồn kho</Title>
      <Tabs 
        activeKey={activeTab} 
        onChange={key => setActiveTab(key)}
        items={[
          {
            key: '1',
            label: 'Cài đặt thông báo',
            children: (
              <Card>
                <Spin spinning={loading}>
                  <Form
                    form={form}
                    layout="vertical"
                    onFinish={onFinish}
                    initialValues={{
                      inventory_aging_months: 6,
                      low_stock_threshold: 5,
                      email_schedule: '0 8 * * *',
                      notification_emails: ''
                    }}
                  >
                    <Paragraph>
                      Cài đặt các ngưỡng cảnh báo và lịch gửi email thông báo tự động về tình trạng tồn kho.
                    </Paragraph>

                    <Divider orientation="left">Ngưỡng cảnh báo</Divider>

                    <Form.Item
                      label="Thời gian tồn kho lâu (tháng)"
                      name="inventory_aging_months"
                      tooltip="Sản phẩm sẽ được coi là tồn kho lâu nếu không được bán trong số tháng này"
                      rules={[{ required: true, message: 'Vui lòng nhập thời gian tồn kho lâu' }]}
                    >
                      <InputNumber min={1} max={36} style={{ width: '100%' }} />
                    </Form.Item>

                    <Form.Item
                      label="Ngưỡng tồn kho thấp"
                      name="low_stock_threshold"
                      tooltip="Sản phẩm sẽ được coi là sắp hết hàng nếu tồn kho thấp hơn hoặc bằng giá trị này"
                      rules={[{ required: true, message: 'Vui lòng nhập ngưỡng tồn kho thấp' }]}
                    >
                      <InputNumber min={1} max={100} style={{ width: '100%' }} />
                    </Form.Item>

                    <Divider orientation="left">Cài đặt email thông báo</Divider>

                    <Form.Item
                      label="Lịch gửi email (Cron expression)"
                      name="email_schedule"
                      tooltip="Định dạng: phút giờ ngày tháng thứ. Ví dụ: 0 8 * * * = 8 giờ sáng hàng ngày"
                      rules={[{ required: true, message: 'Vui lòng nhập lịch gửi email' }]}
                    >
                      <Input placeholder="0 8 * * *" />
                    </Form.Item>

                    <Form.Item
                      label="Danh sách email nhận thông báo"
                      name="notification_emails"
                      tooltip="Nhập danh sách email, phân cách bằng dấu phẩy"
                      rules={[{ required: true, message: 'Vui lòng nhập ít nhất một email' }]}
                    >
                      <TextArea 
                        placeholder="<EMAIL>, <EMAIL>" 
                        autoSize={{ minRows: 2, maxRows: 6 }}
                      />
                    </Form.Item>

                    <Form.Item>
                      <Space>
                        <Button
                          type="primary"
                          htmlType="submit"
                          icon={<SaveOutlined />}
                          loading={saving}
                        >
                          Lưu cài đặt
                        </Button>

                        <Button
                          type="default"
                          icon={<SendOutlined />}
                          onClick={handleSendNotification}
                          loading={sending}
                        >
                          Gửi thông báo ngay
                        </Button>
                      </Space>
                    </Form.Item>
                  </Form>
                </Spin>
              </Card>
            )
          },
          {
            key: '2',
            label: 'Sản phẩm tồn kho lâu',
            children: (
              <Card>
                <Spin spinning={loadingProducts}>
                  <Alert
                    message="Danh sách sản phẩm tồn kho lâu"
                    description={
                      <>
                        <div style={{ marginTop: 8 }}>
                          <InfoCircleOutlined /> Chỉ hiển thị sản phẩm có thời gian tồn kho từ {settings.inventory_aging_months || 6} tháng trở lên.
                        </div>
                      </>
                    }
                    type="info"
                    showIcon
                    icon={<ClockCircleOutlined />}
                    style={{ marginBottom: 16 }}
                  />

                  <Table
                    columns={agingProductColumns}
                    dataSource={agingProducts}
                    rowKey="id"
                    pagination={{ pageSize: 10 }}
                    locale={{ emptyText: 'Không có sản phẩm tồn kho lâu' }}
                  />
                </Spin>
              </Card>
            )
          },
          {
            key: '3',
            label: 'Sản phẩm sắp hết hàng',
            children: (
              <Card>
                <Spin spinning={loadingProducts}>
                  <Alert
                    message="Danh sách sản phẩm sắp hết hàng"
                    description={`Hiển thị các sản phẩm có tồn kho thấp hơn hoặc bằng ${settings.low_stock_threshold || 5}.`}
                    type="warning"
                    showIcon
                    icon={<WarningOutlined />}
                    style={{ marginBottom: 16 }}
                  />

                  <Table
                    columns={lowStockProductColumns}
                    dataSource={lowStockProducts}
                    rowKey="id"
                    pagination={{ pageSize: 10 }}
                    locale={{ emptyText: 'Không có sản phẩm sắp hết hàng' }}
                  />
                </Spin>
              </Card>
            )
          },
          {
            key: '4',
            label: 'Sản phẩm hết hàng',
            children: (
              <Card>
                <Spin spinning={loadingProducts}>
                  <Alert
                    message="Danh sách sản phẩm đã hết hàng"
                    description="Hiển thị các sản phẩm có tồn kho bằng 0."
                    type="error"
                    showIcon
                    icon={<WarningOutlined />}
                    style={{ marginBottom: 16 }}
                  />

                  <Table
                    columns={outOfStockProductColumns}
                    dataSource={outOfStockProducts}
                    rowKey="id"
                    pagination={{ pageSize: 10 }}
                    locale={{ emptyText: 'Không có sản phẩm hết hàng' }}
                  />
                </Spin>
              </Card>
            )
          }
        ]}
      />
    </div>
  );
};

export default InventoryNotificationSettings;
