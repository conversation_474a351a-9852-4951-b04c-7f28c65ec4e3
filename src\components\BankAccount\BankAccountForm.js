import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Card, Switch, Select, Typography, Divider } from 'antd';
import { useNavigate, useParams } from 'react-router-dom';
import { Toaster, toast } from 'react-hot-toast';
import BankAccountService from '../../services/bankAccount.service';

const { Title } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const BankAccountForm = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const navigate = useNavigate();
  const { id } = useParams();

  useEffect(() => {
    if (id) {
      setIsEdit(true);
      fetchBankAccountData();
    }
  }, [id]);

  const fetchBankAccountData = async () => {
    setLoading(true);
    try {
      const response = await BankAccountService.getBankAccountById(id);
      const bankAccountData = response.data;
      form.setFieldsValue({
        accountNumber: bankAccountData.accountNumber,
        bankName: bankAccountData.bankName,
        accountHolder: bankAccountData.accountHolder,
        branch: bankAccountData.branch,
        type: bankAccountData.type,
        note: bankAccountData.note,
        isActive: bankAccountData.isActive,
      });
    } catch (error) {
      toast.error('Không thể tải thông tin tài khoản ngân hàng: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const onFinish = async (values) => {
    setLoading(true);
    try {
      if (isEdit) {
        await BankAccountService.updateBankAccount(id, values);
        toast.success('Cập nhật tài khoản ngân hàng thành công');
      } else {
        await BankAccountService.createBankAccount(values);
        toast.success('Thêm tài khoản ngân hàng thành công');
      }
      navigate('/bank-accounts');
    } catch (error) {
      toast.error('Lỗi: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <Toaster position="top-right" />
      <Title level={2} style={{ 
        background: 'linear-gradient(to right, #1890ff, #52c41a)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        fontSize: '24px',
        fontWeight: 'bold'
      }}>{isEdit ? 'Cập nhật tài khoản ngân hàng' : 'Thêm tài khoản ngân hàng mới'}</Title>
      <Divider />

      <Card>
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          initialValues={{ type: 'company', isActive: true }}
          style={{ fontSize: '16px' }}
        >
          <Form.Item
            name="accountNumber"
            label="Số tài khoản"
            rules={[{ required: true, message: 'Vui lòng nhập số tài khoản' }]}
          >
            <Input style={{ height: '40px', fontSize: '16px' }} />
          </Form.Item>

          <Form.Item
            name="bankName"
            label="Ngân hàng"
            rules={[{ required: true, message: 'Vui lòng nhập tên ngân hàng' }]}
          >
            <Input style={{ height: '40px', fontSize: '16px' }} />
          </Form.Item>

          <Form.Item
            name="accountHolder"
            label="Chủ tài khoản"
            rules={[{ required: true, message: 'Vui lòng nhập tên chủ tài khoản' }]}
          >
            <Input style={{ height: '40px', fontSize: '16px' }} />
          </Form.Item>

          <Form.Item
            name="branch"
            label="Chi nhánh ngân hàng"
          >
            <Input style={{ height: '40px', fontSize: '16px' }} />
          </Form.Item>

          <Form.Item
            name="type"
            label="Loại tài khoản"
            rules={[{ required: true, message: 'Vui lòng chọn loại tài khoản' }]}
          >
            <Select style={{ height: '40px', fontSize: '16px' }}>
              <Option value="company">Công ty</Option>
              <Option value="personal">Cá nhân</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="note"
            label="Ghi chú"
          >
            <TextArea rows={4} style={{ fontSize: '16px' }} />
          </Form.Item>

          <Form.Item
            name="isActive"
            label="Trạng thái"
            valuePropName="checked"
          >
            <Switch checkedChildren="Hoạt động" unCheckedChildren="Không hoạt động" defaultChecked />
          </Form.Item>

          <Form.Item>
            <Button 
              type="primary" 
              htmlType="submit" 
              loading={loading}
              style={{ height: '40px', fontSize: '16px' }}
            >
              {isEdit ? 'Cập nhật' : 'Thêm mới'}
            </Button>
            <Button 
              style={{ marginLeft: 8, height: '40px', fontSize: '16px' }} 
              onClick={() => navigate('/bank-accounts')}
            >
              Hủy
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default BankAccountForm;
