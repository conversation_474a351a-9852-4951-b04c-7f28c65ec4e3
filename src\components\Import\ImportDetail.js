import React, { useState, useEffect } from 'react';
import { Card, Descriptions, Table, Typography, Button, Tag, Divider, Space } from 'antd';
import { Toaster, toast } from 'react-hot-toast';
import { useParams, useNavigate } from 'react-router-dom';
import { PrinterOutlined, EditOutlined } from '@ant-design/icons';
import ImportService from '../../services/import.service';
import BranchService from '../../services/branch.service';
import { formatCurrencyWithSymbol } from '../../utils/format';
import './PrintStyles.css';

const { Title } = Typography;

const ImportDetail = () => {
  const [importData, setImportData] = useState(null);
  const [loading, setLoading] = useState(true);
  const { id } = useParams();
  const navigate = useNavigate();

  useEffect(() => {
    fetchImportData();
  }, [id]);

  const fetchImportData = async () => {
    setLoading(true);
    try {
      const response = await ImportService.getImportById(id);

      const importData = { ...response.data };

      // Kiểm tra xem items có tồn tại không
      if (!importData.items || importData.items.length === 0) {
        if (importData.ImportItems && importData.ImportItems.length > 0) {
          importData.items = importData.ImportItems.map(item => {
            return {
              id: item.id,
              productId: item.productId,
              quantity: item.quantity,
              price: item.price,
              amount: item.amount,
              product: item.Product || item.product
            };
          });
        } else {
          console.warn('No items or ImportItems found in the response');
          importData.items = [];
        }
      }

      // Đảm bảo thông tin nhà cung cấp được chuẩn hóa
      if (importData.Supplier && !importData.supplier) {
        importData.supplier = importData.Supplier;
      }

      // Đảm bảo thông tin chi nhánh được chuẩn hóa
      if (importData.Branch && !importData.branch) {
        importData.branch = importData.Branch;
      }

      // Nếu không có thông tin chi nhánh nhưng có branchId, lấy thông tin chi nhánh từ API
      if ((!importData.branch || !importData.Branch) && importData.branchId) {
        try {
          const branchResponse = await BranchService.getBranchById(importData.branchId);
          importData.branch = branchResponse.data;
        } catch (branchError) {
          console.error('Error fetching branch data:', branchError);
          // Tạo thông tin chi nhánh tạm thời nếu không lấy được từ API
          if (importData.branchId === 1) {
            importData.branch = {
              id: 1,
              name: "Công ty TNHH Nguyên Luân",
              address: "265 Trần Quang Diệu, Xuân An, Phan Thiết, Bình Thuận"
            };
          } else {
            importData.branch = {
              id: importData.branchId,
              name: `Chi nhánh ${importData.branchId}`
            };
          }
        }
      }

      setImportData(importData);
    } catch (error) {
      console.error('Error fetching import data:', error);
      toast.error('Không thể tải thông tin phiếu nhập: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'green';
      case 'pending':
        return 'orange';
      case 'cancelled':
        return 'red';
      default:
        return 'default';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'completed':
        return 'Đã hoàn thành';
      case 'pending':
        return 'Chờ xử lý';
      case 'cancelled':
        return 'Đã hủy';
      default:
        return status;
    }
  };

  // Giải pháp triệt để - mở cửa sổ in mới
  const handlePrintFinal = () => {
    // Lưu trạng thái gốc
    const originalTitle = document.title;

    // Tạo một trang in hoàn toàn mới
    const printWindow = window.open('', '_blank');

    if (!printWindow) {
      alert('Trình duyệt đã chặn cửa sổ pop-up. Vui lòng cho phép pop-up và thử lại.');
      return;
    }

    // Cập nhật tiêu đề
    printWindow.document.title = `Phiếu nhập hàng - ${importData.importCode}`;

    // Tạo HTML cho trang mới với layout đã sửa
    const printHtml = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Phiếu nhập hàng - ${importData.importCode}</title>
        <style>
          @page {
            size: landscape !important;
            margin: 0.5cm !important;
          }

          body {
            font-family: 'Segoe UI', Arial, sans-serif;
            margin: 0;
            padding: 15px;
            box-sizing: border-box;
            font-size: 14px;
          }

          /* Đảm bảo phiếu nhập hiển thị đúng */
          .print-content {
            width: 100%;
            max-width: 100%;
            padding: 0;
            margin: 0;
          }

          /* Tiêu đề phiếu nhập */
          .print-header {
            text-align: center;
            margin-bottom: 20px;
          }

          .print-header h3 {
            font-size: 22px;
            margin: 10px 0;
            font-weight: bold;
            text-transform: uppercase;
          }

          .print-header p {
            margin: 4px 0;
            font-size: 13px;
          }

          /* Bảng thông tin */
          table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
          }

          table, th, td {
            border: 1px solid #000;
          }

          th, td {
            padding: 8px 10px;
            text-align: left;
            font-size: 13px;
          }

          th {
            background-color: #f5f5f5;
            font-weight: bold;
          }

          /* Phần chữ ký */
          .signature-section {
            display: flex;
            justify-content: space-between;
            margin-top: 25px;
            margin-bottom: 15px;
          }

          .signature-block {
            width: 30%;
            text-align: center;
            height: 100px;
          }

          .signature-block p {
            margin: 4px 0;
            font-size: 13px;
            font-weight: bold;
          }

          /* Tổng tiền */
          .total-table td:first-child {
            width: 15%;
            font-weight: bold;
          }

          .divider {
            border-top: 1px solid #000;
            margin: 15px 0;
          }

          /* Định dạng tag trạng thái */
          .status-tag {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 13px;
            font-weight: bold;
          }

          .status-green {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
          }

          .status-orange {
            background-color: #fff7e6;
            border: 1px solid #ffd591;
            color: #fa8c16;
          }

          .status-red {
            background-color: #fff1f0;
            border: 1px solid #ffa39e;
            color: #f5222d;
          }
        </style>
      </head>
      <body>
        <div class="print-content">
          <!-- Phần tiêu đề -->
          <div class="print-header">
            <h3>PHIẾU NHẬP HÀNG</h3>
            <p>Mã phiếu nhập: ${importData.importCode}</p>
            <p>Ngày: ${new Date(importData.importDate).toLocaleDateString('vi-VN')}</p>
          </div>

          <!-- Phần thông tin nhà cung cấp - CẢI TIẾN: mỗi trường thông tin chiếm 1 dòng -->
          <table>
            <tr>
              <td width="15%"><strong>Nhà cung cấp:</strong></td>
              <td colspan="3">${(importData.supplier && importData.supplier.name) ||
      (importData.Supplier && importData.Supplier.name) || 'N/A'}</td>
            </tr>
            <tr>
              <td><strong>Địa chỉ:</strong></td>
              <td colspan="3">${(importData.supplier && importData.supplier.address) ||
      (importData.Supplier && importData.Supplier.address) ||
      (importData.branch && importData.branch.address) ||
      (importData.Branch && importData.Branch.address) || 'N/A'}</td>
            </tr>
            <tr>
              <td><strong>Số điện thoại:</strong></td>
              <td colspan="3">${(importData.supplier && importData.supplier.phone) ||
      (importData.Supplier && importData.Supplier.phone) || 'N/A'}</td>
            </tr>
            <tr>
              <td><strong>Chi nhánh:</strong></td>
              <td>${(importData.branch && importData.branch.name) ||
      (importData.Branch && importData.Branch.name) || 'N/A'}</td>
              <td width="15%"><strong>Trạng thái:</strong></td>
              <td>
                <span class="status-tag status-${getStatusColor(importData.status)}">
                  ${getStatusText(importData.status)}
                </span>
              </td>
            </tr>
          </table>

          <div class="divider"></div>

          <!-- Phần bảng sản phẩm -->
          <table>
            <thead>
              <tr>
                <th style="width: 60px;">STT</th>
                <th>Sản phẩm</th>
                <th>Đơn giá</th>
                <th>Số lượng</th>
                <th>Thành tiền</th>
              </tr>
            </thead>
            <tbody>
              ${importData.items.map((item, index) => `
                <tr>
                  <td>${index + 1}</td>
                  <td>${(item.product && item.product.name) ||
        (item.Product && item.Product.name) || 'N/A'}</td>
                  <td>${formatCurrencyWithSymbol(item.price)}</td>
                  <td>${item.quantity}</td>
                  <td>${formatCurrencyWithSymbol(item.amount)}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>

          <!-- Phần tổng tiền nằm ở table riêng -->
          <table class="total-table">
            <tr>
              <td width="15%"><strong>Tổng cộng:</strong></td>
              <td width="85%">${formatCurrencyWithSymbol(importData.totalAmount)}</td>
            </tr>
            <tr>
              <td><strong>Đã thanh toán:</strong></td>
              <td>${formatCurrencyWithSymbol(importData.paidAmount)}</td>
            </tr>
            <tr>
              <td><strong>Còn nợ:</strong></td>
              <td>${formatCurrencyWithSymbol(importData.totalAmount - importData.paidAmount)}</td>
            </tr>
          </table>

          ${importData.note ? `
          <div class="divider"></div>
          <div class="print-note">
            <strong>Ghi chú:</strong>
            <p>${importData.note}</p>
          </div>
          ` : ''}

          <div class="divider"></div>

          <!-- Phần chữ ký -->
          <div class="signature-section">
            <div class="signature-block">
              <p><strong>Người lập phiếu</strong></p>
              <p>(Ký, họ tên)</p>
            </div>
            <div class="signature-block">
              <p><strong>Người giao hàng</strong></p>
              <p>(Ký, họ tên)</p>
            </div>
            <div class="signature-block">
              <p><strong>Thủ kho</strong></p>
              <p>(Ký, họ tên)</p>
            </div>
          </div>
        </div>

        <script>
          // Tự động in khi trang đã tải xong
          window.onload = function() {
            window.print();
            // Sau khi in xong sẽ tự đóng cửa sổ sau 1 giây
            setTimeout(function() {
              window.close();
            }, 1000);
          };
        </script>
      </body>
      </html>
    `;

    // Ghi HTML vào cửa sổ mới
    printWindow.document.open();
    printWindow.document.write(printHtml);
    printWindow.document.close();
  };

  const columns = [
    {
      title: 'STT',
      key: 'index',
      width: 60,
      render: (_, __, index) => index + 1,
    },
    {
      title: 'Sản phẩm',
      key: 'product',
      render: (_, record) => {
        if (record.product && record.product.name) {
          return record.product.name;
        }
        if (record.Product && record.Product.name) {
          return record.Product.name;
        }
        return 'N/A';
      },
    },
    {
      title: 'Đơn giá',
      dataIndex: 'price',
      key: 'price',
      render: (price) => formatCurrencyWithSymbol(price),
    },
    {
      title: 'Số lượng',
      dataIndex: 'quantity',
      key: 'quantity',
    },
    {
      title: 'Thành tiền',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount) => formatCurrencyWithSymbol(amount),
    },
  ];

  if (loading) {
    return <div>Đang tải...</div>;
  }

  if (!importData) {
    return <div>Không tìm thấy phiếu nhập</div>;
  }

  return (
    <div className="import-detail-container">
      <Toaster position="top-right" />
      <Card loading={loading}>
        <div className="no-print page-header" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
          <Title level={2}>Chi tiết phiếu nhập</Title>
          <Space>
            <Button type="primary" icon={<EditOutlined />} onClick={() => navigate(`/imports/edit/${id}`)}>
              Sửa
            </Button>
            <Button icon={<PrinterOutlined />} onClick={handlePrintFinal}>
              In phiếu nhập
            </Button>
          </Space>
        </div>

        <div className="print-container">
          <div className="print-header" style={{ textAlign: 'center', marginBottom: 24 }}>
            <Title level={3} className="print-title">PHIẾU NHẬP HÀNG</Title>
            <p className="print-code">Mã phiếu nhập: {importData.importCode}</p>
            <p className="print-date">Ngày: {new Date(importData.importDate).toLocaleDateString('vi-VN')}</p>
          </div>
          <Descriptions bordered column={2} className="print-descriptions">
          <Descriptions.Item label="Nhà cung cấp" span={2}>
            {(importData.supplier && importData.supplier.name) ||
              (importData.Supplier && importData.Supplier.name) || 'N/A'}
          </Descriptions.Item>
          <Descriptions.Item label="Địa chỉ">
            {(importData.supplier && importData.supplier.address) ||
              (importData.Supplier && importData.Supplier.address) ||
              (importData.branch && importData.branch.address) ||
              (importData.Branch && importData.Branch.address) || 'N/A'}
          </Descriptions.Item>
          <Descriptions.Item label="Số điện thoại">
            {(importData.supplier && importData.supplier.phone) ||
              (importData.Supplier && importData.Supplier.phone) || 'N/A'}
          </Descriptions.Item>
          <Descriptions.Item label="Chi nhánh">
            {(importData.branch && importData.branch.name) ||
             (importData.Branch && importData.Branch.name) || 'N/A'}
          </Descriptions.Item>
          <Descriptions.Item label="Trạng thái">
            <Tag color={getStatusColor(importData.status)} className={`status-tag status-${importData.status}`}>
              {getStatusText(importData.status)}
            </Tag>
          </Descriptions.Item>
        </Descriptions>

        <Divider className="print-divider" />

        <Table
          className="print-table"
          columns={columns}
          dataSource={importData.items}
          rowKey={(record) => record.id || record.key || record._id || Math.random().toString(36).substr(2, 9)}
          pagination={false}
          bordered
          summary={() => (
            <Table.Summary fixed className="print-summary">
              <Table.Summary.Row>
                <Table.Summary.Cell index={0} colSpan={4} style={{ textAlign: 'right' }}>
                  <strong>Tổng cộng:</strong>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={1}>
                  <strong>{formatCurrencyWithSymbol(importData.totalAmount)}</strong>
                </Table.Summary.Cell>
              </Table.Summary.Row>
              <Table.Summary.Row>
                <Table.Summary.Cell index={0} colSpan={4} style={{ textAlign: 'right' }}>
                  <strong>Đã thanh toán:</strong>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={1}>
                  <strong>{formatCurrencyWithSymbol(importData.paidAmount)}</strong>
                </Table.Summary.Cell>
              </Table.Summary.Row>
              <Table.Summary.Row>
                <Table.Summary.Cell index={0} colSpan={4} style={{ textAlign: 'right' }}>
                  <strong>Còn nợ:</strong>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={1}>
                  {(() => {
                    const remainingAmount = importData.remainingAmount || (importData.totalAmount - importData.paidAmount);
                    const style = remainingAmount > 0 ? { color: 'red', fontWeight: 'bold' } : { fontWeight: 'bold' };
                    return <span style={style}>{formatCurrencyWithSymbol(remainingAmount)}</span>;
                  })()}
                </Table.Summary.Cell>
              </Table.Summary.Row>
            </Table.Summary>
          )}
        />

        {importData.note && (
          <>
            <Divider className="print-divider" />
            <div className="print-note">
              <strong>Ghi chú:</strong>
              <p>{importData.note}</p>
            </div>
          </>
        )}

        <Divider className="print-divider" />

        <div className="print-signatures" style={{ display: 'flex', justifyContent: 'space-between', marginTop: 30 }}>
          <div className="signature-block" style={{ textAlign: 'center', width: '30%' }}>
            <p><strong>Người lập phiếu</strong></p>
            <p>(Ký, họ tên)</p>
          </div>
          <div className="signature-block" style={{ textAlign: 'center', width: '30%' }}>
            <p><strong>Người giao hàng</strong></p>
            <p>(Ký, họ tên)</p>
          </div>
          <div className="signature-block" style={{ textAlign: 'center', width: '30%' }}>
            <p><strong>Thủ kho</strong></p>
            <p>(Ký, họ tên)</p>
          </div>
        </div>
        </div>
      </Card>
    </div>
  );
};

export default ImportDetail;