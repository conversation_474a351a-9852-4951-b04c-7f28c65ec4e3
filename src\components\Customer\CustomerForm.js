import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Card, Typography, Divider } from 'antd';
import { Toaster, toast } from 'react-hot-toast';
import { useNavigate, useParams } from 'react-router-dom';
import CustomerService from '../../services/customer.service';

const { Title } = Typography;
const { TextArea } = Input;

const CustomerForm = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const navigate = useNavigate();
  const { id } = useParams();

  useEffect(() => {
    if (id) {
      setIsEdit(true);
      fetchCustomerData();
    }
  }, [id]);

  const fetchCustomerData = async () => {
    setLoading(true);
    try {
      const response = await CustomerService.getCustomerById(id);
      const customerData = response.data;
      form.setFieldsValue({
        customerCode: customerData.customerCode,
        name: customerData.name,
        email: customerData.email,
        phone: customerData.phone,
        address: customerData.address,
        area: customerData.area,
        ward: customerData.ward,
        taxCode: customerData.taxCode,
        note: customerData.note,
      });
    } catch (error) {
      toast.error('Không thể tải thông tin khách hàng: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const onFinish = async (values) => {
    setLoading(true);
    try {
      if (isEdit) {
        await CustomerService.updateCustomer(id, values);
        toast.success('Cập nhật khách hàng thành công');
      } else {
        await CustomerService.createCustomer(values);
        toast.success('Thêm khách hàng thành công');
      }
      navigate('/customers');
    } catch (error) {
      toast.error('Lỗi: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <Toaster position="top-right" />
      <Title level={2} style={{ 
        background: 'linear-gradient(to right, #1890ff, #52c41a)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        fontSize: '24px',
        fontWeight: 'bold'
      }}>{isEdit ? 'Cập nhật khách hàng' : 'Thêm khách hàng mới'}</Title>
      <Divider />

      <Card>
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          style={{ fontSize: '16px' }}
        >
          <Form.Item
            name="customerCode"
            label="Mã khách hàng"
            rules={[{ required: true, message: 'Vui lòng nhập mã khách hàng' }]}
          >
            <Input style={{ height: '40px', fontSize: '16px' }} />
          </Form.Item>

          <Form.Item
            name="name"
            label="Tên khách hàng"
            rules={[{ required: true, message: 'Vui lòng nhập tên khách hàng' }]}
          >
            <Input style={{ height: '40px', fontSize: '16px' }} />
          </Form.Item>

          <Form.Item
            name="email"
            label="Email"
            rules={[
              { type: 'email', message: 'Email không hợp lệ' },
            ]}
          >
            <Input style={{ height: '40px', fontSize: '16px' }} />
          </Form.Item>

          <Form.Item
            name="phone"
            label="Số điện thoại"
            rules={[
              { required: true, message: 'Vui lòng nhập số điện thoại' },
              { pattern: /^[0-9]+$/, message: 'Vui lòng chỉ nhập số' }
            ]}
          >
            <Input 
              maxLength={15}
              style={{ height: '40px', fontSize: '16px' }}
              onKeyPress={(e) => {
                const charCode = e.which ? e.which : e.keyCode;
                // Chỉ cho phép nhập số (0-9)
                if (charCode < 48 || charCode > 57) {
                  e.preventDefault();
                }
              }}
            />
          </Form.Item>

          <Form.Item
            name="address"
            label="Địa chỉ"
          >
            <Input style={{ height: '40px', fontSize: '16px' }} />
          </Form.Item>

          <Form.Item
            name="area"
            label="Khu vực"
          >
            <Input style={{ height: '40px', fontSize: '16px' }} />
          </Form.Item>

          <Form.Item
            name="ward"
            label="Phường xã"
          >
            <Input style={{ height: '40px', fontSize: '16px' }} />
          </Form.Item>

          <Form.Item
            name="taxCode"
            label="Mã số thuế"
          >
            <Input style={{ height: '40px', fontSize: '16px' }} />
          </Form.Item>

          <Form.Item
            name="note"
            label="Ghi chú"
          >
            <TextArea rows={4} style={{ fontSize: '16px' }} />
          </Form.Item>

          <Form.Item>
            <Button 
              type="primary" 
              htmlType="submit" 
              loading={loading}
              style={{ height: '40px', fontSize: '16px' }}
            >
              {isEdit ? 'Cập nhật' : 'Thêm mới'}
            </Button>
            <Button 
              style={{ marginLeft: 8, height: '40px', fontSize: '16px' }} 
              onClick={() => navigate('/customers')}
            >
              Hủy
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default CustomerForm;
