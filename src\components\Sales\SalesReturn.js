import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Input, Button, Table, Typography, Select, InputNumber, Form, Pagination, Tag, Divider, Modal, Badge, Avatar, Space, Empty } from 'antd';
import { Toaster, toast } from 'react-hot-toast';
import { SearchOutlined, RollbackOutlined, SaveOutlined, EyeOutlined, PrinterOutlined, DeleteOutlined, UserOutlined, FileTextOutlined, CheckCircleOutlined, CloseCircleOutlined, ExclamationCircleOutlined, CalendarOutlined, DollarOutlined, ShoppingCartOutlined, ClockCircleOutlined } from '@ant-design/icons';
import InvoiceService from '../../services/invoice.service';
import ReturnService from '../../services/return.service';
import ReturnCheckService from '../../services/returnCheck.service';
import ProductService from '../../services/product.service';
import { formatCurrencyWithSymbol } from '../../utils/format';
import './SalesPrintStyles.css';

const { Title, Text } = Typography;
const { Option } = Select;
const { Search } = Input;

const SalesReturn = () => {
  // Màu sắc chính cho giao diện
  const themeColors = {
    primary: '#1890ff',
    success: '#52c41a',
    warning: '#faad14',
    danger: '#f5222d',
    secondary: '#722ed1',
    background: '#f5f5f5',
    headerBg: 'linear-gradient(90deg, #722ed1, #531dab)',
    invoiceHeaderBg: 'linear-gradient(90deg, #1890ff, #096dd9)',
    returnHeaderBg: 'linear-gradient(90deg, #fa8c16, #d46b08)',
    saveButton: 'linear-gradient(90deg, #f5222d, #cf1322)',
    printButton: 'linear-gradient(90deg, #1890ff, #096dd9)',
    lightBg: '#f0f5ff'
  };

  const [form] = Form.useForm();
  const [invoices, setInvoices] = useState([]);
  const [filteredInvoices, setFilteredInvoices] = useState([]);
  const [selectedInvoice, setSelectedInvoice] = useState(null);
  const [invoiceItems, setInvoiceItems] = useState([]);
  const [returnItems, setReturnItems] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalAmount, setTotalAmount] = useState(0);
  const [modalVisible, setModalVisible] = useState(false);
  const pageSize = 10;

  useEffect(() => {
    fetchInvoices();
  }, []);

  useEffect(() => {
    // Tính tổng tiền khi danh sách sản phẩm trả hàng thay đổi
    const total = returnItems.reduce((sum, item) => sum + (item.quantity * item.price), 0);
    setTotalAmount(total);
  }, [returnItems]);

  useEffect(() => {
    // Lọc hóa đơn khi có tìm kiếm
    if (invoices.length > 0) {
      const filtered = invoices.filter(invoice =>
        invoice.invoiceCode.toLowerCase().includes(searchText.toLowerCase()) ||
        (invoice.Customer && invoice.Customer.name &&
         invoice.Customer.name.toLowerCase().includes(searchText.toLowerCase()))
      );
      setFilteredInvoices(filtered);
    }
  }, [searchText, invoices]);

  const fetchInvoices = async () => {
    setLoading(true);
    try {
      const response = await InvoiceService.getAllInvoices();
      // Chỉ lấy các hóa đơn đã thanh toán
      const paidInvoices = response.data.filter(invoice => invoice.status === 'completed' || invoice.status === 'paid');
      setInvoices(paidInvoices);
      setFilteredInvoices(paidInvoices);
    } catch (error) {
      toast.error('Không thể tải danh sách hóa đơn: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const fetchInvoiceDetail = async (id) => {
    setLoading(true);
    try {
      const response = await InvoiceService.getInvoiceById(id);

      // Kiểm tra dữ liệu trả về
      if (!response.data) {
        throw new Error('Không nhận được dữ liệu hóa đơn từ server');
      }

      setSelectedInvoice(response.data);

      // Lấy danh sách sản phẩm từ hóa đơn
      const items = response.data.InvoiceItems || [];

      // Log để debug


      
      // Lấy số lượng đã trả của từng sản phẩm trong hóa đơn này
      try {
        const returnedQuantities = await ReturnCheckService.getReturnedQuantities(id);

        
        // Kiểm tra và đảm bảo mỗi sản phẩm đều có giá và ID
        const itemsWithPrice = items.map(item => {
          // Tạo bản sao của item để tránh thay đổi dữ liệu gốc
          const updatedItem = { ...item };

          // Đảm bảo có productId
          if (!updatedItem.productId && updatedItem.Product) {
            updatedItem.productId = updatedItem.Product.id;

          }

          // Đảm bảo có giá
          if (!updatedItem.price && updatedItem.Product) {
            updatedItem.price = updatedItem.Product.sellingPrice || updatedItem.Product.price || 0;
            console.warn(`Sản phẩm ${updatedItem.Product.name} không có giá trong hóa đơn, sử dụng giá từ sản phẩm: ${updatedItem.price}`);
          }
          
          // Tính toán số lượng còn có thể trả (số lượng ban đầu - số lượng đã trả)
          const productId = updatedItem.productId;
          const originalQuantity = parseInt(updatedItem.quantity) || 0;
          const returnedQuantity = returnedQuantities.returnedItems[productId] || 0;
          const remainingQuantity = Math.max(0, originalQuantity - returnedQuantity);
          
          // Thêm thông tin về số lượng đã trả và số lượng còn có thể trả
          updatedItem.originalQuantity = originalQuantity;
          updatedItem.returnedQuantity = returnedQuantity;
          updatedItem.remainingQuantity = remainingQuantity;
          


          return updatedItem;
        });



        setInvoiceItems(itemsWithPrice);
      } catch (returnCheckError) {
        console.error('Lỗi khi lấy số lượng đã trả:', returnCheckError);
        
        // Nếu không lấy được số lượng đã trả, vẫn hiển thị danh sách sản phẩm ban đầu
        const itemsWithPrice = items.map(item => {
          // Tạo bản sao của item để tránh thay đổi dữ liệu gốc
          const updatedItem = { ...item };

          // Đảm bảo có productId
          if (!updatedItem.productId && updatedItem.Product) {
            updatedItem.productId = updatedItem.Product.id;

          }

          // Đảm bảo có giá
          if (!updatedItem.price && updatedItem.Product) {
            updatedItem.price = updatedItem.Product.sellingPrice || updatedItem.Product.price || 0;
            console.warn(`Sản phẩm ${updatedItem.Product.name} không có giá trong hóa đơn, sử dụng giá từ sản phẩm: ${updatedItem.price}`);
          }
          
          // Gán số lượng ban đầu là số lượng còn có thể trả
          updatedItem.originalQuantity = parseInt(updatedItem.quantity) || 0;
          updatedItem.returnedQuantity = 0;
          updatedItem.remainingQuantity = updatedItem.originalQuantity;

          return updatedItem;
        });


        setInvoiceItems(itemsWithPrice);
      }

      // Reset danh sách sản phẩm trả hàng
      setReturnItems([]);
    } catch (error) {
      console.error('Lỗi khi tải chi tiết hóa đơn:', error);
      toast.error('Không thể tải chi tiết hóa đơn: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (value) => {
    setSearchText(value);
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handleSelectInvoice = (id) => {
    fetchInvoiceDetail(id);
    setModalVisible(true);
  };

  const handleAddReturnItem = async (item) => {
    // Log thông tin sản phẩm để debug

    
    // Xác định ID sản phẩm - kiểm tra cả productId và Product.id
    const productId = item.productId || (item.Product && item.Product.id);

    if (!productId) {
      console.error('Không thể xác định ID sản phẩm:', item);
      toast.error('Không thể xác định sản phẩm. Vui lòng thử lại.');
      return;
    }

    // Kiểm tra xem sản phẩm đã có trong danh sách trả hàng chưa

    const existingItemIndex = returnItems.findIndex(returnItem => {

      return returnItem.productId === productId;
    });

    if (existingItemIndex >= 0) {
      toast.warning('Sản phẩm này đã được thêm vào danh sách trả hàng');
      return;
    }

    // Sử dụng trực tiếp số lượng còn có thể trả đã được tính toán trong fetchInvoiceDetail
    const remainingQuantity = item.remainingQuantity || 0;
    const originalQuantity = item.originalQuantity || item.quantity || 0;
    const alreadyReturnedQuantity = item.returnedQuantity || 0;






    if (remainingQuantity <= 0) {
      toast.error(`Sản phẩm này đã được trả hết (Đã trả: ${alreadyReturnedQuantity}, Mua: ${originalQuantity})`);
      return;
    }

    // Lấy giá sản phẩm (đảm bảo luôn có giá)
    const productPrice = item.price || 0;
    const productName = item.Product ? item.Product.name : (item.productName || 'N/A');

    // Tạo một ID tạm thời cho mỗi mục trả hàng
    const tempId = `temp-${Math.random().toString(36).substr(2, 9)}`;

    // Thêm sản phẩm vào danh sách trả hàng
    const newReturnItem = {
      tempId: tempId,
      productId: productId,
      productName: productName,
      price: productPrice,
      quantity: 1,
      maxQuantity: remainingQuantity, // Giới hạn số lượng tối đa có thể trả
      reason: '',
    };
    

    setReturnItems(prevItems => [...prevItems, newReturnItem]);
    
    // Hiển thị thông báo thành công
    toast.success(`Đã thêm ${productName} vào danh sách trả hàng`);
  };

  const handleRemoveReturnItem = (index) => {
    const updatedItems = [...returnItems];
    updatedItems.splice(index, 1);
    setReturnItems(updatedItems);
  };

  const handleQuantityChange = (value, index) => {
    if (value > 0 && value <= returnItems[index].maxQuantity) {
      const updatedItems = [...returnItems];
      updatedItems[index].quantity = value;
      setReturnItems(updatedItems);
    } else {
      toast.warning(`Số lượng phải từ 1 đến ${returnItems[index].maxQuantity}`);
    }
  };

  const handleReasonChange = (value, index) => {
    const updatedItems = [...returnItems];
    updatedItems[index].reason = value;
    setReturnItems(updatedItems);
  };

  const handleCreateReturn = async () => {
    if (returnItems.length === 0) {
      toast.error('Vui lòng thêm ít nhất một sản phẩm vào phiếu trả hàng');
      return;
    }

    // Đã loại bỏ kiểm tra bắt buộc nhập lý do trả hàng

    setLoading(true);
    try {
      // Hiển thị thông báo đang xử lý ngay từ đầu
      const loadingToast = toast.loading('Đang xử lý phiếu trả hàng...', 0);
      
      // Kiểm tra lại thông tin cần thiết
      if (!selectedInvoice || !selectedInvoice.id) {
        toast.dismiss(loadingToast);
        toast.error('Không tìm thấy thông tin hóa đơn');
        throw new Error('Không tìm thấy thông tin hóa đơn');
      }

      if (!selectedInvoice.customerId) {
        toast.dismiss(loadingToast);
        toast.error('Không tìm thấy thông tin khách hàng');
        throw new Error('Không tìm thấy thông tin khách hàng');
      }

      // Kiểm tra số lượng trả hàng có hợp lệ không
      const invoiceItems = selectedInvoice.InvoiceItems || [];
      
      // Lấy lại số lượng đã trả mới nhất từ API
      try {
        const returnedQuantities = await ReturnCheckService.getReturnedQuantities(selectedInvoice.id);

        
        // Cập nhật lại số lượng còn có thể trả cho từng sản phẩm trong hóa đơn
        const updatedInvoiceItems = invoiceItems.map(item => {
          const productId = item.productId || (item.Product && item.Product.id);
          const originalQuantity = parseInt(item.originalQuantity || item.quantity) || 0;
          const returnedQuantity = returnedQuantities.returnedItems[productId] || 0;
          const remainingQuantity = Math.max(0, originalQuantity - returnedQuantity);
          

          
          return {
            ...item,
            returnedQuantity,
            remainingQuantity
          };
        });
        
        // Cập nhật lại danh sách sản phẩm trong hóa đơn với số lượng còn có thể trả mới nhất
        setInvoiceItems(updatedInvoiceItems);
        
        // Tạo map để tra cứu nhanh số lượng còn có thể trả của từng sản phẩm
        const remainingQuantityMap = {};
        updatedInvoiceItems.forEach(item => {
          const productId = item.productId || (item.Product && item.Product.id);
          if (productId) {
            remainingQuantityMap[productId] = item.remainingQuantity || 0;
          }
        });
        

        
        // Kiểm tra từng sản phẩm trong danh sách trả hàng
        const invalidItems = [];
        returnItems.forEach(item => {
          const productId = item.productId;
          const returnQuantity = item.quantity;
          const remainingQuantity = remainingQuantityMap[productId] || 0;
          

          
          // Nếu số lượng trả vượt quá số lượng còn có thể trả, thêm vào danh sách không hợp lệ
          if (returnQuantity > remainingQuantity) {
            invalidItems.push({
              productId,
              productName: item.productName,
              requestedQuantity: returnQuantity,
              remainingQuantity: remainingQuantity
            });
          }
        });
        
        // Nếu có sản phẩm không hợp lệ, hiển thị thông báo lỗi
        if (invalidItems.length > 0) {
          const invalidItemsInfo = invalidItems.map(item =>
            `- ${item.productName}: Yêu cầu trả ${item.requestedQuantity}, nhưng chỉ còn có thể trả ${item.remainingQuantity}`
          ).join('\n');
          
          toast.dismiss(loadingToast);
          toast.error(`Số lượng trả hàng vượt quá số lượng còn có thể trả:\n${invalidItemsInfo}`);
          throw new Error(`Số lượng trả hàng vượt quá số lượng còn có thể trả:\n${invalidItemsInfo}`);
        }
      } catch (error) {
        console.error('Lỗi khi cập nhật số lượng đã trả:', error);
        toast.dismiss(loadingToast);
        toast.error(`Không thể kiểm tra số lượng trả hàng: ${error.message}`);
        throw new Error(`Không thể kiểm tra số lượng trả hàng: ${error.message}`);
      }
      
      // Log dữ liệu để debug


      
      // Tạo phiếu trả hàng với trạng thái 'completed' ngay lập tức
      const returnData = {
        customerId: selectedInvoice.customerId,
        invoiceId: selectedInvoice.id,
        items: returnItems.map(item => ({
          productId: item.productId,
          quantity: item.quantity,
          price: item.price,
          amount: item.quantity * item.price,
          reason: item.reason || '' // Đảm bảo reason không bị null hoặc undefined
        })),
        totalAmount,
        status: 'completed' // Tạo với trạng thái 'completed' ngay lập tức
      };


      
      // Cập nhật giao diện trước khi API hoàn thành
      // Đóng modal và hiển thị thông báo thành công ngay lập tức
      toast.success('Đã tạo phiếu trả hàng, đang cập nhật hệ thống...');
      setModalVisible(false);
      
      // Gọi API trong background để tạo phiếu trả hàng
      ReturnService.createReturn(returnData)
        .then(response => {

          // Xu1eed lu00fd phu1ea3n hu1ed3i tu1eeb API

          if (response.data) {

          }
          
          toast.dismiss(loadingToast);
          toast.success('Cập nhật hệ thống thành công!');
          
          // Kiểm tra xem có ID phiếu trả hàng không
          // Truy cập đúng vị trí của ID phiếu trả hàng trong response.data.data
          let returnId = null;
          
          // Kiểm tra xem response.data.data có tồn tại không
          if (response.data && response.data.data) {

            
            // Kiểm tra các trường hợp có thể chứa ID trong response.data.data
            if (response.data.data.id) {
              returnId = response.data.data.id;

            } else if (response.data.data._id) {
              returnId = response.data.data._id;

            } else if (typeof response.data.data === 'object') {
              // Log tất cả các thuộc tính của response.data.data

              
              // Tìm kiếm bất kỳ trường nào có thể là ID
              for (const key in response.data.data) {
                if (key.toLowerCase().includes('id') && key !== 'invoiceId' && key !== 'customerId') {
                  returnId = response.data.data[key];

                  break;
                }
              }
            }
          }
          
          if (returnId) {
            // Sau khi tạo thành công, cập nhật trạng thái thành 'completed'

            
            // Cập nhật trạng thái thành 'completed'
            ReturnService.updateReturn(returnId, { status: 'completed' })
              .then(updateResponse => {

                
                // Sau khi cập nhật trạng thái thành công, cập nhật số lượng tồn kho
                updateInventoryAfterReturn(returnItems);
              })
              .catch(updateError => {
                console.error('Lỗi khi cập nhật trạng thái phiếu trả hàng:', updateError);
              });
          } else {
            console.warn('Không tìm thấy ID phiếu trả hàng trong phản hồi API');
          }
          
          // Tải lại danh sách hóa đơn
          fetchInvoices();
        })
        .catch(error => {
          console.error('Lỗi khi tạo phiếu trả hàng:', error);
          toast.dismiss(loadingToast);
          toast.error('Đã xảy ra lỗi khi cập nhật hệ thống: ' + (error.response?.data?.message || error.message));
        })
        .finally(() => {
          setLoading(false);
        });
      
      // Reset form ngay lập tức không cần đợi API hoàn thành
      setReturnItems([]);
      setSelectedInvoice(null);
      setInvoiceItems([]);
      
    } catch (error) {
      console.error('Lỗi khi tạo phiếu trả hàng:', error);
      toast.error('Không thể tạo phiếu trả hàng: ' + (error.response?.data?.message || error.message));
      setLoading(false);
    }
  };

  // Chức năng in phiếu trả hàng
  // Hàm cập nhật số lượng tồn kho sau khi trả hàng
  const updateInventoryAfterReturn = async (items) => {
    try {

      
      // Xử lý từng sản phẩm trong danh sách trả hàng
      for (const item of items) {
        const productId = item.productId;
        const returnQuantity = item.quantity;
        
        // Lấy thông tin sản phẩm hiện tại
        const productResponse = await ProductService.getProductById(productId);
        const product = productResponse.data;
        
        if (product) {

          
          // Tính toán số lượng mới (tăng số lượng tồn kho sau khi trả hàng)
          const newStock = (parseInt(product.stock) || 0) + returnQuantity;
          
          // Cập nhật số lượng tồn kho mới
          await ProductService.updateProduct(productId, { stock: newStock });

        } else {
          console.warn(`Không tìm thấy sản phẩm với ID: ${productId}`);
        }
      }
      

      toast.success('Đã cập nhật số lượng tồn kho');
    } catch (error) {
      console.error('Lỗi khi cập nhật số lượng tồn kho:', error);
      toast.error('Lỗi khi cập nhật số lượng tồn kho: ' + error.message);
    }
  };

  const handlePrintReturn = () => {
    if (returnItems.length === 0) {
      toast.error('Vui lòng thêm ít nhất một sản phẩm vào phiếu trả hàng');
      return;
    }

    // Đã loại bỏ kiểm tra bắt buộc nhập lý do trả hàng

    // Tạo một trang in hoàn toàn mới
    const printWindow = window.open('', '_blank');

    if (!printWindow) {
      toast.error('Trình duyệt đã chặn cửa sổ pop-up. Vui lòng cho phép pop-up và thử lại.');
      return;
    }

    // Tạo mã phiếu trả hàng tạm thời
    const tempReturnCode = `TH-TẠM-${new Date().getTime().toString().slice(-6)}`;

    // Tạo HTML cho trang in
    const printHtml = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Phiếu trả hàng - ${tempReturnCode}</title>
        <style>
          @page {
            size: landscape !important;
            margin: 0.5cm !important;
          }

          body {
            font-family: 'Segoe UI', Arial, sans-serif;
            margin: 0;
            padding: 15px;
            box-sizing: border-box;
            font-size: 14px;
          }

          /* Đảm bảo phiếu trả hàng hiển thị đúng */
          .print-content {
            width: 100%;
            max-width: 100%;
            padding: 0;
            margin: 0;
          }

          /* Tiêu đề phiếu trả hàng */
          .print-header {
            text-align: center;
            margin-bottom: 20px;
          }

          .print-header h3 {
            font-size: 22px;
            margin: 10px 0;
            font-weight: bold;
            text-transform: uppercase;
          }

          .print-header p {
            margin: 4px 0;
            font-size: 13px;
          }

          /* Bảng thông tin */
          table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
          }

          table, th, td {
            border: 1px solid #d9d9d9;
          }

          th, td {
            padding: 8px 10px;
            text-align: left;
            font-size: 13px;
          }

          th {
            background-color: #f5f5f5;
          }

          /* Tổng tiền */
          .total-table td:first-child {
            width: 15%;
            font-weight: bold;
          }

          .divider {
            border-top: 1px solid #d9d9d9;
            margin: 15px 0;
          }
        </style>
      </head>
      <body>
        <div class="print-content">
          <!-- Phần tiêu đề -->
          <div class="print-header">
            <h3>PHIẾU TRẢ HÀNG</h3>
            <p>Mã phiếu tạm: ${tempReturnCode}</p>
            <p>Ngày: ${new Date().toLocaleDateString('vi-VN')}</p>
          </div>

          <!-- Phần thông tin khách hàng và hóa đơn -->
          <table>
            <tr>
              <td width="15%"><strong>Khách hàng:</strong></td>
              <td colspan="3">${selectedInvoice.Customer ? selectedInvoice.Customer.name : 'N/A'}</td>
            </tr>
            <tr>
              <td><strong>Hóa đơn gốc:</strong></td>
              <td colspan="3">${selectedInvoice.invoiceCode || 'N/A'}</td>
            </tr>
            <tr>
              <td><strong>Ngày mua:</strong></td>
              <td colspan="3">${new Date(selectedInvoice.createdAt).toLocaleDateString('vi-VN')}</td>
            </tr>
            <tr>
              <td width="15%"><strong>Trạng thái:</strong></td>
              <td colspan="3">Chờ xử lý</td>
            </tr>
          </table>

          <div class="divider"></div>

          <!-- Phần bảng sản phẩm -->
          <table>
            <thead>
              <tr>
                <th style="width: 60px;">STT</th>
                <th>Sản phẩm</th>
                <th>Đơn giá</th>
                <th>Số lượng</th>
                <th>Thành tiền</th>
                <th>Lý do trả hàng (nếu có)</th>
              </tr>
            </thead>
            <tbody>
              ${returnItems.map((item, index) => `
                <tr>
                  <td>${index + 1}</td>
                  <td>${item.productName}</td>
                  <td>${formatCurrencyWithSymbol(item.price)}</td>
                  <td>${item.quantity}</td>
                  <td>${formatCurrencyWithSymbol(item.quantity * item.price)}</td>
                  <td>${item.reason}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>

          <!-- Phần tổng tiền -->
          <table class="total-table">
            <tr>
              <td width="15%"><strong>Tổng tiền trả:</strong></td>
              <td width="85%">${formatCurrencyWithSymbol(totalAmount)}</td>
            </tr>
          </table>

          <div class="divider"></div>
          <div class="print-note">
            <strong>Ghi chú:</strong>
            <p>Đây là phiếu trả hàng tạm thời. Vui lòng liên hệ nhân viên để hoàn tất thủ tục trả hàng.</p>
          </div>
        </div>

        <script>
          // Tự động in khi trang đã tải xong
          window.onload = function() {
            window.print();
            // Sau khi in xong sẽ tự đóng cửa sổ sau 1 giây
            setTimeout(function() {
              window.close();
            }, 1000);
          };
        </script>
      </body>
      </html>
    `;

    // Ghi HTML vào cửa sổ mới
    printWindow.document.open();
    printWindow.document.write(printHtml);
    printWindow.document.close();
  };

  const invoiceColumns = [
    {
      title: 'Mã hóa đơn',
      dataIndex: 'invoiceCode',
      key: 'invoiceCode',
      render: (code) => (
        <div style={{ 
          fontWeight: 'bold', 
          color: themeColors.primary,
          display: 'flex',
          alignItems: 'center'
        }}>
          <FileTextOutlined style={{ marginRight: 8, color: themeColors.primary }} />
          {code}
        </div>
      )
    },
    {
      title: 'Khách hàng',
      dataIndex: 'Customer',
      key: 'customer',
      render: (_, record) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Avatar 
            icon={<UserOutlined />} 
            style={{ 
              backgroundColor: themeColors.secondary,
              marginRight: 8
            }} 
            size="small" 
          />
          <span>{record.Customer ? record.Customer.name : 'N/A'}</span>
        </div>
      )
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <CalendarOutlined style={{ marginRight: 8, color: themeColors.secondary }} />
          {new Date(date).toLocaleDateString('vi-VN')}
        </div>
      )
    },
    {
      title: 'Tổng tiền',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      render: (amount) => (
        <div style={{ 
          fontWeight: 'bold', 
          color: themeColors.danger
        }}>
          {formatCurrencyWithSymbol(amount)}
        </div>
      )
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        let color = 'default';
        let text = 'Không xác định';
        let icon = <ExclamationCircleOutlined />;
        
        switch (status) {
          case 'pending':
            color = themeColors.warning;
            text = 'Chờ thanh toán';
            icon = <ClockCircleOutlined />;
            break;
          case 'completed':
          case 'paid':
            color = themeColors.success;
            text = 'Đã thanh toán';
            icon = <CheckCircleOutlined />;
            break;
          case 'cancelled':
            color = themeColors.danger;
            text = 'Đã hủy';
            icon = <CloseCircleOutlined />;
            break;
          default:
            break;
        }
        
        return (
          <Tag 
            color={color}
            icon={icon}
            style={{ 
              display: 'inline-flex', 
              alignItems: 'center',
              padding: '2px 8px',
              borderRadius: '4px',
              width: 'auto'
            }}
          >
            {text}
          </Tag>
        );
      }
    },
    {
      title: 'Hành động',
      key: 'action',
      render: (_, record) => (
        <Button
          type="primary"
          icon={<EyeOutlined />}
          onClick={() => handleSelectInvoice(record.id)}
          style={{ 
            background: themeColors.primary,
            borderColor: themeColors.primary,
            borderRadius: '6px'
          }}
        >
          Xem chi tiết
        </Button>
      ),
    },
  ];

  const invoiceItemColumns = [
    {
      title: 'Tên sản phẩm',
      dataIndex: 'Product',
      key: 'productName',
      render: (_, record) => {
        const productName = record.Product ? record.Product.name : (record.productName || 'N/A');
        return (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Avatar 
              shape="square" 
              size="small" 
              icon={<ShoppingCartOutlined />} 
              style={{ 
                backgroundColor: themeColors.primary,
                marginRight: 8
              }} 
            />
            <span style={{ fontWeight: 'bold' }}>{productName}</span>
          </div>
        );
      },
    },
    {
      title: 'Giá',
      dataIndex: 'price',
      key: 'price',
      render: (price) => (
        <div style={{ fontWeight: 'bold', color: themeColors.danger }}>
          {formatCurrencyWithSymbol(price)}
        </div>
      ),
    },
    {
      title: 'Số lượng',
      dataIndex: 'quantity',
      key: 'quantity',
      render: (quantity, record) => (
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          <Badge 
            count={quantity} 
            style={{ 
              backgroundColor: themeColors.secondary,
              fontWeight: 'bold',
              fontSize: '14px',
              marginBottom: '4px'
            }} 
            showZero 
          />
          {record.remainingQuantity !== undefined && (
            <div style={{ 
              fontSize: '12px', 
              color: record.remainingQuantity > 0 ? themeColors.success : themeColors.danger,
              fontWeight: 'bold'
            }}>
              Còn trả: {record.remainingQuantity}
            </div>
          )}
        </div>
      )
    },
    {
      title: 'Thành tiền',
      key: 'amount',
      render: (_, record) => (
        <div style={{ fontWeight: 'bold', color: themeColors.danger }}>
          {formatCurrencyWithSymbol(record.price * record.quantity)}
        </div>
      ),
    },
    {
      title: 'Hành động',
      key: 'action',
      render: (_, record) => {
        const isDisabled = returnItems.some(item => item.productId === record.productId);
        return (
          <Button
            type="primary"
            icon={<RollbackOutlined />}
            onClick={() => handleAddReturnItem(record)}
            disabled={isDisabled}
            style={{ 
              background: isDisabled ? '#f5f5f5' : themeColors.returnHeaderBg,
              borderColor: isDisabled ? '#d9d9d9' : themeColors.warning,
              color: isDisabled ? '#bfbfbf' : 'white',
              borderRadius: '6px'
            }}
          >
            Trả hàng
          </Button>
        );
      },
    },
  ];

  const returnItemColumns = [
    {
      title: 'Tên sản phẩm',
      dataIndex: 'productName',
      key: 'productName',
      render: (productName) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Avatar 
            shape="square" 
            size="small" 
            icon={<ShoppingCartOutlined />} 
            style={{ 
              backgroundColor: themeColors.warning,
              marginRight: 8
            }} 
          />
          <span style={{ fontWeight: 'bold' }}>{productName}</span>
        </div>
      ),
    },
    {
      title: 'Giá',
      dataIndex: 'price',
      key: 'price',
      render: (price) => (
        <div style={{ fontWeight: 'bold', color: themeColors.danger }}>
          {formatCurrencyWithSymbol(price)}
        </div>
      ),
    },
    {
      title: 'Số lượng',
      dataIndex: 'quantity',
      key: 'quantity',
      render: (quantity, record, index) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <InputNumber
            min={1}
            max={record.maxQuantity}
            value={quantity}
            onChange={(value) => handleQuantityChange(value, index)}
            style={{ 
              width: 70,
              borderRadius: '4px',
              marginRight: '8px'
            }}
          />
          <Badge 
            count={`/${record.maxQuantity}`} 
            style={{ 
              backgroundColor: themeColors.secondary,
              fontSize: '12px'
            }} 
          />
        </div>
      ),
    },
    {
      title: 'Thành tiền',
      key: 'amount',
      render: (_, record) => (
        <div style={{ fontWeight: 'bold', color: themeColors.danger }}>
          {formatCurrencyWithSymbol(record.price * record.quantity)}
        </div>
      ),
    },
    {
      title: 'Lý do trả hàng',
      dataIndex: 'reason',
      key: 'reason',
      render: (reason, record, index) => (
        <Input
          value={reason}
          onChange={(e) => handleReasonChange(e.target.value, index)}
          placeholder="Nhập lý do trả hàng (không bắt buộc)"
          style={{ 
            borderRadius: '4px',
            border: '1px solid #d9d9d9'
          }}
          prefix={<ExclamationCircleOutlined style={{ color: themeColors.warning }} />}
        />
      ),
    },
    {
      title: 'Hành động',
      key: 'action',
      render: (_, record, index) => (
        <Button
          type="text"
          danger
          icon={<DeleteOutlined />}
          onClick={() => handleRemoveReturnItem(index)}
          style={{ 
            borderRadius: '50%',
            width: '32px',
            height: '32px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        />
      ),
    },
  ];

  // Tính toán hóa đơn hiển thị cho trang hiện tại
  const paginatedInvoices = filteredInvoices.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  return (
    <div style={{ 
      height: 'calc(100vh - 112px)', 
      display: 'flex', 
      flexDirection: 'column', 
      overflow: 'hidden',
      background: themeColors.background,
      padding: '8px'
    }}>
      <Toaster position="top-right" toastOptions={{
        success: {
          duration: 5000,
          style: {
            background: '#4caf50',
            color: 'white',
            fontSize: '16px',
            padding: '16px'
          },
        },
        error: {
          duration: 5000,
          style: {
            background: '#f44336',
            color: 'white',
            fontSize: '16px',
            padding: '16px'
          },
        },
        loading: {
          style: {
            background: '#2196f3',
            color: 'white',
            fontSize: '16px',
            padding: '16px'
          },
        }
      }}/>


      <Card 
        title={
          <div style={{ 
            fontSize: 16, 
            fontWeight: 'bold', 
            color: 'white', 
            display: 'flex',
            alignItems: 'center'
          }}>
            <FileTextOutlined style={{ marginRight: 8 }} />
            Danh sách hóa đơn đã thanh toán
          </div>
        }
        variant="outlined"
        style={{ 
          flex: 1, 
          display: 'flex', 
          flexDirection: 'column', 
          overflow: 'hidden',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.09)'
        }}
        styles={{ 
          header: { 
            background: themeColors.invoiceHeaderBg,
            borderTopLeftRadius: '8px',
            borderTopRightRadius: '8px',
            padding: '12px 16px'
          },
          body: { 
            flex: 1, 
            overflow: 'auto', 
            padding: '16px',
            background: '#fff'
          }
        }}
      >
        <Search
          placeholder="Tìm kiếm hóa đơn theo mã hoặc tên khách hàng..."
          allowClear
          enterButton={<SearchOutlined />}
          size="large"
          onSearch={handleSearch}
          onChange={(e) => setSearchText(e.target.value)}
          style={{ 
            marginBottom: 16, 
            borderRadius: '8px', 
            boxShadow: '0 2px 6px rgba(0, 0, 0, 0.05)'
          }}
        />

        <div style={{ flex: 1, overflow: 'auto' }}>
          <Table
            columns={invoiceColumns}
            dataSource={paginatedInvoices}
            rowKey="id"
            pagination={false}
            loading={loading}
            style={{ 
              background: 'white',
              borderRadius: '8px',
              overflow: 'hidden'
            }}
          />

          <Pagination
            current={currentPage}
            total={filteredInvoices.length}
            pageSize={pageSize}
            onChange={handlePageChange}
            style={{ marginTop: 16, textAlign: 'right' }}
          />
        </div>
      </Card>

      <Modal
        title={
          <div style={{ 
            display: 'flex', 
            alignItems: 'center',
            color: themeColors.primary,
            fontSize: '18px'
          }}>
            <FileTextOutlined style={{ marginRight: 10, fontSize: '20px' }} />
            Chi tiết hóa đơn: <span style={{ fontWeight: 'bold', marginLeft: 5 }}>{selectedInvoice?.invoiceCode || ''}</span>
          </div>
        }
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        width={1000}
        footer={null}
        styles={{ body: { padding: '20px' } }}
        style={{ borderRadius: '8px', overflow: 'hidden' }}
      >
        {selectedInvoice && (
          <>
            <Card 
              style={{ 
                marginBottom: 16, 
                borderRadius: '8px',
                background: themeColors.lightBg,
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.09)'
              }}
              styles={{ body: { padding: '16px' } }}
            >
              <Row gutter={16}>
                <Col span={8}>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <UserOutlined style={{ fontSize: '18px', color: themeColors.primary, marginRight: 8 }} />
                    <div>
                      <div style={{ fontSize: '14px', color: '#8c8c8c' }}>Khách hàng</div>
                      <div style={{ fontSize: '16px', fontWeight: 'bold' }}>{selectedInvoice.Customer ? selectedInvoice.Customer.name : 'N/A'}</div>
                    </div>
                  </div>
                </Col>
                <Col span={8}>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <CalendarOutlined style={{ fontSize: '18px', color: themeColors.secondary, marginRight: 8 }} />
                    <div>
                      <div style={{ fontSize: '14px', color: '#8c8c8c' }}>Ngày tạo</div>
                      <div style={{ fontSize: '16px', fontWeight: 'bold' }}>{new Date(selectedInvoice.createdAt).toLocaleDateString('vi-VN')}</div>
                    </div>
                  </div>
                </Col>
                <Col span={8}>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <DollarOutlined style={{ fontSize: '18px', color: themeColors.warning, marginRight: 8 }} />
                    <div>
                      <div style={{ fontSize: '14px', color: '#8c8c8c' }}>Tổng tiền</div>
                      <div style={{ fontSize: '16px', fontWeight: 'bold', color: themeColors.danger }}>{formatCurrencyWithSymbol(selectedInvoice.totalAmount)}</div>
                    </div>
                  </div>
                </Col>
              </Row>
            </Card>

            <Card
              title={
                <div style={{ 
                  fontSize: 16, 
                  fontWeight: 'bold', 
                  color: 'white', 
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  <ShoppingCartOutlined style={{ marginRight: 8 }} />
                  Danh sách sản phẩm trong hóa đơn
                </div>
              }
              style={{ 
                marginBottom: 16, 
                borderRadius: '8px',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.09)'
              }}
              styles={{
                header: {
                  background: themeColors.invoiceHeaderBg,
                  borderTopLeftRadius: '8px',
                  borderTopRightRadius: '8px',
                  padding: '12px 16px'
                }
              }}
            >
              <Table
                columns={invoiceItemColumns}
                dataSource={invoiceItems}
                rowKey="id"
                pagination={false}
                size="small"
                style={{ 
                  borderRadius: '8px',
                  overflow: 'hidden'
                }}
              />
            </Card>

            {returnItems.length > 0 && (
              <Card
                title={
                  <div style={{ 
                    fontSize: 16, 
                    fontWeight: 'bold', 
                    color: 'white', 
                    display: 'flex',
                    alignItems: 'center'
                  }}>
                    <RollbackOutlined style={{ marginRight: 8 }} />
                    Danh sách sản phẩm trả hàng
                  </div>
                }
                style={{ 
                  marginBottom: 16, 
                  borderRadius: '8px',
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.09)'
                }}
                styles={{
                  header: {
                    background: themeColors.returnHeaderBg,
                    borderTopLeftRadius: '8px',
                    borderTopRightRadius: '8px',
                    padding: '12px 16px'
                  }
                }}
              >
                <Table
                  columns={returnItemColumns}
                  dataSource={returnItems}
                  rowKey={record => record.id || record.tempId || Math.random().toString(36).substr(2, 9)}
                  pagination={false}
                  size="small"
                  style={{ 
                    borderRadius: '8px',
                    overflow: 'hidden',
                    marginBottom: '16px'
                  }}
                />

                <div style={{ 
                  padding: '12px 16px', 
                  background: themeColors.lightBg, 
                  borderRadius: '8px',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}>
                  <Text style={{ fontSize: '16px' }}>Tổng tiền trả:</Text>
                  <Title level={3} style={{ margin: 0, color: themeColors.danger }}>
                    {formatCurrencyWithSymbol(totalAmount)}
                  </Title>
                </div>
              </Card>
            )}

            <div style={{ 
              marginTop: 24, 
              display: 'flex', 
              justifyContent: 'space-between',
              padding: '16px',
              background: '#f9f9f9',
              borderRadius: '8px'
            }}>
              <Button
                icon={<PrinterOutlined />}
                size="large"
                onClick={handlePrintReturn}
                disabled={returnItems.length === 0}
                style={{ 
                  borderRadius: '8px',
                  background: themeColors.printButton,
                  color: 'white',
                  border: 'none',
                  opacity: returnItems.length === 0 ? 0.5 : 1,
                  boxShadow: '0 2px 6px rgba(24, 144, 255, 0.2)'
                }}
              >
                In phiếu trả hàng
              </Button>

              <Button
                type="primary"
                icon={<SaveOutlined />}
                size="large"
                onClick={handleCreateReturn}
                loading={loading}
                disabled={returnItems.length === 0}
                style={{ 
                  borderRadius: '8px',
                  background: themeColors.saveButton,
                  border: 'none',
                  opacity: returnItems.length === 0 ? 0.5 : 1,
                  boxShadow: '0 2px 6px rgba(245, 34, 45, 0.2)'
                }}
              >
                Tạo phiếu trả hàng
              </Button>
            </div>
          </>
        )}
      </Modal>
    </div>
  );
};

export default SalesReturn;
