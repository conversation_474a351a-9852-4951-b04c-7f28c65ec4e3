/* Global styles */
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, 'Helvetica Neue', Arial, sans-serif;
}

/* Layout styles */
.site-layout-background {
  background: #fff;
}

.logo {
  height: 32px;
  margin: 16px;
  color: white;
  font-size: 18px;
  font-weight: bold;
  text-align: center;
}

/* Table styles */
.ant-table-wrapper {
  width: 100%;
  overflow-x: auto;
}

/* Form styles */
.ant-form-item-label {
  font-weight: 500;
}

/* Label styles */
.label-item {
  border: 1px dashed #ccc;
  padding: 10px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

/* Print styles */
@media print {
  .ant-layout-header,
  .ant-layout-sider,
  .ant-btn,
  .ant-divider,
  .ant-modal-header,
  .ant-modal-footer,
  .ant-modal-close,
  .ant-modal-mask,
  .preview-container,
  .ant-input-number,
  .ant-input,
  input {
    display: none !important;
  }

  .site-layout-background {
    padding: 0 !important;
    margin: 0 !important;
  }

  .print-container {
    display: block !important;
    box-shadow: none !important;
    border: none !important;
  }

  @page {
    size: A4;
    margin: 0;
  }

  body * {
    visibility: hidden;
  }

  .print-container, .print-container * {
    visibility: visible;
  }

  .print-container {
    position: absolute !important;
    left: 50% !important;
    top: 0 !important;
    display: grid !important;
    grid-template-columns: repeat(2, 8cm) !important; /* 2 cột */
    grid-auto-rows: 3.5cm !important;
    gap: 0.3cm !important;
    padding: 0.5cm !important;
    width: 18cm !important;
    margin: 0 auto !important;
    justify-content: center !important;
    transform: translateX(-50%) !important;
  }

  /* Styles for product labels */
  .print-container > div {
    page-break-inside: avoid !important;
    break-inside: avoid !important;
    border: none !important;
    margin-bottom: 0 !important;
    padding: 0.3cm !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    align-items: center !important;
    height: 100% !important;
    box-sizing: border-box !important;
  }
}
