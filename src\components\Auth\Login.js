import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Card, Typography, Alert, Spin, Radio } from 'antd';
import { UserOutlined, LockOutlined, InboxOutlined, ShopOutlined, AppstoreOutlined } from '@ant-design/icons';
import { useNavigate, Navigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import './Login.css'; // Sẽ tạo file CSS riêng cho trang đăng nhập

const { Title, Text } = Typography;

const Login = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [animateForm, setAnimateForm] = useState(false);
  const [interfaceType, setInterfaceType] = useState('management');
  const navigate = useNavigate();
  const { login, isAuthenticated } = useAuth();

  // Animation effect when component mounts
  useEffect(() => {
    // Delay animation to make it visible after page load
    const timer = setTimeout(() => {
      setAnimateForm(true);
    }, 300);

    return () => clearTimeout(timer);
  }, []);

  // Redirect if already logged in
  if (isAuthenticated()) {
    // Chuyển hướng dựa vào loại giao diện đã chọn
    const redirectPath = localStorage.getItem('interfaceType') === 'sales' ? '/sales' : '/';
    return <Navigate to={redirectPath} />;
  }

  const onFinish = async (values) => {
    setLoading(true);
    setError('');
    try {
      await login(values);
      // Lưu loại giao diện vào localStorage
      localStorage.setItem('interfaceType', interfaceType);
      // Chuyển hướng dựa vào loại giao diện đã chọn
      navigate(interfaceType === 'sales' ? '/sales' : '/');
    } catch (err) {
      setError(err.response?.data?.message || 'Đăng nhập thất bại. Vui lòng thử lại.');
    } finally {
      setLoading(false);
    }
  };

  const handleInterfaceChange = (e) => {
    setInterfaceType(e.target.value);
    form.setFieldsValue({ interfaceType: e.target.value });
  };

  const handleInterfaceClick = (type) => {
    setInterfaceType(type);
    form.setFieldsValue({ interfaceType: type });
  };

  return (
    <div className="login-container">
      <div className="login-background">
        <div className="login-shape shape-1"></div>
        <div className="login-shape shape-2"></div>
        <div className="login-shape shape-3"></div>
      </div>

      <div className={`login-card-container ${animateForm ? 'animate' : ''}`}>
        <Card className="login-card">
          <div className="login-logo">
            <InboxOutlined className="login-icon" />
          </div>

          <div className="login-header">
            <Title level={2} className="login-title">QUẢN LÝ KHO</Title>
            <Text className="login-subtitle">Đăng nhập vào hệ thống</Text>
          </div>

          {error && <Alert message={error} type="error" showIcon className="login-alert" />}

          <Form
            form={form}
            name="login"
            initialValues={{ remember: true, interfaceType: 'management' }}
            onFinish={onFinish}
            layout="vertical"
            className="login-form"
          >
            <Form.Item name="interfaceType" className="interface-type-selector">
              <div className="interface-options">
                <div
                  className={`interface-option ${interfaceType === 'management' ? 'active' : ''}`}
                  onClick={() => handleInterfaceClick('management')}
                >
                  <div className="interface-icon management-icon">
                    <AppstoreOutlined />
                  </div>
                  <div className="interface-label">Quản lý</div>
                </div>
                <div
                  className={`interface-option ${interfaceType === 'sales' ? 'active' : ''}`}
                  onClick={() => handleInterfaceClick('sales')}
                >
                  <div className="interface-icon sales-icon">
                    <ShopOutlined />
                  </div>
                  <div className="interface-label">Bán hàng</div>
                </div>
              </div>
              <Radio.Group
                onChange={handleInterfaceChange}
                value={interfaceType}
                className="hidden-radio-group"
              >
                <Radio.Button value="management">Quản lý</Radio.Button>
                <Radio.Button value="sales">Bán hàng</Radio.Button>
              </Radio.Group>
            </Form.Item>
            <Form.Item
              name="username"
              rules={[{ required: true, message: 'Vui lòng nhập tên đăng nhập!' }]}
            >
              <Input
                prefix={<UserOutlined className="login-input-icon" />}
                placeholder="Tên đăng nhập"
                size="large"
                className="login-input"
              />
            </Form.Item>

            <Form.Item
              name="password"
              rules={[{ required: true, message: 'Vui lòng nhập mật khẩu!' }]}
            >
              <Input.Password
                prefix={<LockOutlined className="login-input-icon" />}
                placeholder="Mật khẩu"
                size="large"
                className="login-input"
              />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                block
                size="large"
                className="login-button"
              >
                {loading ? 'Đang đăng nhập...' : 'Đăng nhập'}
              </Button>
            </Form.Item>
          </Form>

          <div className="login-footer">
            <Text className="login-copyright">© {new Date().getFullYear()} Hệ thống Quản lý kho | NLTECH</Text>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default Login;
