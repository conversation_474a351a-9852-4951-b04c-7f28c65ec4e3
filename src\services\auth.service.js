import api from './api';

const AuthService = {
  login: async (credentials) => {
    const response = await api.post('/auth/login', credentials);
    if (response.data.token) {
      localStorage.setItem('token', response.data.token);
    }
    return response.data;
  },

  logout: () => {
    localStorage.removeItem('token');
  },

  getCurrentUser: async () => {
    try {
      const response = await api.get('/auth/me');

      // Kiểm tra xem response có dữ liệu không
      if (!response.data) {
        console.error('API /auth/me trả về response không có dữ liệu');
        throw new Error('Không nhận được dữ liệu người dùng');
      }

      return response;
    } catch (error) {
      console.error('Lỗi khi lấy thông tin người dùng hiện tại:', error);
      throw error;
    }
  },

  isAuthenticated: () => {
    return !!localStorage.getItem('token');
  }
};

export default AuthService;
