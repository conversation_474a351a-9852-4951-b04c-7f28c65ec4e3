/**
 * Đ<PERSON><PERSON> dạng số tiền theo chuẩn Việt Nam
 * @param {number|string} amount - Số tiền cần định dạng
 * @returns {string} Chuỗi đã được định dạng (ví dụ: 19.900.000)
 */
export const formatCurrency = (amount) => {
  if (amount === null || amount === undefined) return '0';

  // Chuyển đổi số thành chuỗi và loại bỏ tất cả phần thập phân
  let amountStr = '';

  try {
    if (typeof amount === 'number') {
      // Làm tròn số và chuyển thành chuỗi
      amountStr = Math.round(amount).toString();
    } else if (typeof amount === 'string') {
      // Nếu là chuỗi, loại bỏ phần thập phân và ký tự không phải số
      const numericValue = amount.replace(/[^\d.-]/g, '');
      amountStr = Math.round(parseFloat(numericValue)).toString();
    } else {
      // Trư<PERSON><PERSON> hợ<PERSON>h<PERSON>, chuy<PERSON><PERSON> về 0
      amountStr = '0';
    }
  } catch (error) {
    console.error('Error formatting currency:', error);
    amountStr = '0';
  }

  // Định dạng số với dấu chấm phân cách hàng nghìn
  return amountStr.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
};

/**
 * Định dạng số tiền theo chuẩn Việt Nam và thêm đơn vị tiền tệ
 * @param {number} amount - Số tiền cần định dạng
 * @returns {string} Chuỗi đã được định dạng (ví dụ: 14.900.000 đ)
 */
export const formatCurrencyWithSymbol = (amount) => {
  return `${formatCurrency(amount)} đ`;
};
