import React, { useState, useEffect } from 'react';
import { Table, Button, Space, Popconfirm, Typography, Input, Card, Tag } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, SearchOutlined } from '@ant-design/icons';
import { Toaster, toast } from 'react-hot-toast';
import { Link } from 'react-router-dom';
import PayerService from '../../services/payer.service';

const { Title } = Typography;

const PayerList = () => {
  const [payers, setPayers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');

  useEffect(() => {
    fetchPayers();
  }, []);

  const fetchPayers = async () => {
    setLoading(true);
    try {
      const response = await PayerService.getAllPayers();
      setPayers(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách người nộp: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    try {
      await PayerService.deletePayer(id);
      toast.success('Xóa người nộp thành công');
      fetchPayers();
    } catch (error) {
      toast.error('Không thể xóa người nộp: ' + error.message);
    }
  };

  const filteredPayers = payers.filter(
    (payer) =>
      payer.name?.toLowerCase().includes(searchText.toLowerCase()) ||
      payer.phone?.includes(searchText) ||
      payer.address?.toLowerCase().includes(searchText.toLowerCase()) ||
      payer.email?.toLowerCase().includes(searchText.toLowerCase())
  );

  const columns = [
    {
      title: <span style={{ fontSize: 16 }}>Tên người nộp</span>,
      dataIndex: 'name',
      key: 'name',
      sorter: (a, b) => a.name.localeCompare(b.name),
    },
    {
      title: <span style={{ fontSize: 16 }}>Số điện thoại</span>,
      dataIndex: 'phone',
      key: 'phone',
    },
    {
      title: <span style={{ fontSize: 16 }}>Địa chỉ</span>,
      dataIndex: 'address',
      key: 'address',
    },
    {
      title: <span style={{ fontSize: 16 }}>Email</span>,
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: <span style={{ fontSize: 16 }}>Trạng thái</span>,
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? 'Hoạt động' : 'Không hoạt động'}
        </Tag>
      ),
    },
    {
      title: <span style={{ fontSize: 16 }}>Thao tác</span>,
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Link to={`/payers/edit/${record.id}`}>
            <Button type="primary" icon={<EditOutlined />} size="small" style={{ height: 40, fontSize: 14, display: 'flex', alignItems: 'center', padding: '0 15px' }}>
              Sửa
            </Button>
          </Link>
          <Popconfirm
            title="Bạn có chắc chắn muốn xóa người nộp này?"
            onConfirm={() => handleDelete(record.id)}
            okText="Có"
            cancelText="Không"
          >
            <Button type="primary" danger icon={<DeleteOutlined />} size="small" style={{ height: 40, fontSize: 14, display: 'flex', alignItems: 'center', padding: '0 15px' }}>
              Xóa
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Toaster position="top-right" />
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 20 }}>
        <Title level={2} style={{ fontSize: 28, background: 'linear-gradient(90deg, #1890ff, #096dd9)', WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent' }}>Quản lý người nộp</Title>
        <Link to="/payers/add">
          <Button type="primary" icon={<PlusOutlined />} style={{ height: 40, fontSize: 16, display: 'flex', alignItems: 'center' }}>
            Thêm người nộp
          </Button>
        </Link>
      </div>

      <Card style={{ marginBottom: 16 }}>
        <Input
          placeholder="Tìm kiếm người nộp..."
          prefix={<SearchOutlined />}
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          style={{ width: 300, height: 42, fontSize: 16 }}
        />
      </Card>

      <Table
        columns={columns}
        dataSource={filteredPayers}
        rowKey="id"
        loading={loading}
        pagination={{ pageSize: 10, style: { fontSize: 16 } }}
        style={{ fontSize: 16 }}
      />
    </div>
  );
};

export default PayerList;
