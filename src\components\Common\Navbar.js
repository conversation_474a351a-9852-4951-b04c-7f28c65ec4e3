import React, { useState } from 'react';
import { Layout, Menu, Button, Avatar, Dropdown, Tooltip } from 'antd';
import {
  UserOutlined,
  ShoppingOutlined,
  AppstoreOutlined,
  TagOutlined,
  TeamOutlined,
  FileTextOutlined,
  MenuUnfoldOutlined,
  MenuFoldOutlined,
  LogoutOutlined,
  DashboardOutlined,
  RollbackOutlined,
  BankOutlined,
  HomeOutlined,
  DollarOutlined,
  AuditOutlined,
  ShopOutlined,
  Bar<PERSON>hartOutlined,
  PieChartOutlined,
  SettingOutlined,
  BellOutlined,
  ClockCircleOutlined,
  SwapOutlined
} from '@ant-design/icons';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';

const { Header, Sider, Content } = Layout;

const Navbar = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const { currentUser, logout } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();

  const handleLogout = () => {
    // Xóa loại giao diện khi đăng xuất
    localStorage.removeItem('interfaceType');
    logout();
    navigate('/login');
  };

  const switchToSalesInterface = () => {
    localStorage.setItem('interfaceType', 'sales');
    navigate('/sales');
  };

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: <Link to="/profile">Hồ sơ</Link>
    },
    {
      key: 'switchInterface',
      icon: <SwapOutlined />,
      label: 'Chuyển sang giao diện bán hàng',
      onClick: switchToSalesInterface
    },
    {
      type: 'divider'
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: 'Đăng xuất',
      onClick: handleLogout
    }
  ];

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider trigger={null} collapsible collapsed={collapsed} theme="dark">
        <div className="logo" style={{ height: '64px', display: 'flex', justifyContent: 'center', alignItems: 'center', color: 'white', fontSize: '18px', fontWeight: 'bold' }}>
          {collapsed ? 'QLK' : 'QUẢN LÝ KHO'}
        </div>
        <Menu 
          theme="dark" 
          mode="inline" 
          selectedKeys={[location.pathname]} 
          defaultSelectedKeys={['/']} 
          defaultOpenKeys={['sub1', 'sub2', 'sub3']}
          items={[
            {
              key: '/',
              icon: <DashboardOutlined />,
              label: <Link to="/">Trang chủ</Link>
            },
            {
              key: 'sub1',
              icon: <ShoppingOutlined />,
              label: 'Quản lý hàng hóa',
              children: [
                {
                  key: '/products',
                  icon: <ShoppingOutlined />,
                  label: <Link to="/products">Sản phẩm</Link>
                },
                {
                  key: '/product-categories',
                  icon: <AppstoreOutlined />,
                  label: <Link to="/product-categories">Nhóm hàng</Link>
                },
                {
                  key: '/brands',
                  icon: <TagOutlined />,
                  label: <Link to="/brands">Thương hiệu</Link>
                },
                {
                  key: '/suppliers',
                  icon: <ShopOutlined />,
                  label: <Link to="/suppliers">Nhà cung cấp</Link>
                },
                {
                  key: '/imports',
                  icon: <ShoppingOutlined />,
                  label: <Link to="/imports">Nhập hàng</Link>
                }
              ]
            },
            {
              key: 'sub2',
              icon: <FileTextOutlined />,
              label: 'Quản lý bán hàng',
              children: [
                {
                  key: '/customers',
                  icon: <TeamOutlined />,
                  label: <Link to="/customers">Khách hàng</Link>
                },
                {
                  key: '/invoices',
                  icon: <FileTextOutlined />,
                  label: <Link to="/invoices">Hoá đơn</Link>
                },
                {
                  key: '/returns',
                  icon: <RollbackOutlined />,
                  label: <Link to="/returns">Trả hàng</Link>
                }
              ]
            },
            {
              key: 'sub3',
              icon: <DollarOutlined />,
              label: 'Quản lý thu chi',
              children: [
                {
                  key: '/receipts',
                  icon: <DollarOutlined />,
                  label: <Link to="/receipts">Phiếu thu</Link>
                },
                {
                  key: '/receipt-types',
                  icon: <AuditOutlined />,
                  label: <Link to="/receipt-types">Loại thu chi</Link>
                },
                {
                  key: '/payments',
                  icon: <DollarOutlined />,
                  label: <Link to="/payments">Phiếu chi</Link>
                },
                {
                  key: '/payers',
                  icon: <TeamOutlined />,
                  label: <Link to="/payers">Người nộp</Link>
                },
                {
                  key: '/bank-accounts',
                  icon: <BankOutlined />,
                  label: <Link to="/bank-accounts">Tài khoản ngân hàng</Link>
                }
              ]
            },
            {
              key: '/branches',
              icon: <HomeOutlined />,
              label: <Link to="/branches">Chi nhánh</Link>
            },
            {
              key: '/users',
              icon: <UserOutlined />,
              label: <Link to="/users">Người dùng</Link>
            },
            {
              key: 'sub4',
              icon: <BarChartOutlined />,
              label: 'Báo cáo thống kê',
              children: [
                {
                  key: '/reports',
                  icon: <PieChartOutlined />,
                  label: <Link to="/reports">Báo cáo</Link>
                }
              ]
            },
            {
              key: 'sub5',
              icon: <SettingOutlined />,
              label: 'Cài đặt hệ thống',
              children: [
                {
                  key: '/inventory-notification',
                  icon: <BellOutlined />,
                  label: <Link to="/inventory-notification">Thông báo tồn kho</Link>
                }
              ]
            }
          ]}
        />
      </Sider>
      <Layout className="site-layout">
        <Header className="site-layout-background" style={{ padding: 0, background: '#fff', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => setCollapsed(!collapsed)}
            style={{ fontSize: '16px', width: 64, height: 64 }}
          />
          <div style={{ marginRight: 20 }}>
            <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
              <div style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}>
                <Avatar icon={<UserOutlined />} />
                <span style={{ marginLeft: 8 }}>{currentUser?.name || 'Người dùng'}</span>
              </div>
            </Dropdown>
          </div>
        </Header>
        <Content
          className="site-layout-background"
          style={{
            margin: '24px 16px',
            padding: 24,
            minHeight: 280,
            background: '#fff',
            overflow: 'auto'
          }}
        >
          {children}
        </Content>
      </Layout>
    </Layout>
  );
};

export default Navbar;
