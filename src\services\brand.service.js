import api from './api';

const BrandService = {
  getAllBrands: async () => {
    return api.get('/brands');
  },

  getBrandById: async (id) => {
    return api.get(`/brands/${id}`);
  },

  createBrand: async (brandData) => {
    return api.post('/brands', brandData);
  },

  updateBrand: async (id, brandData) => {
    return api.put(`/brands/${id}`, brandData);
  },

  deleteBrand: async (id) => {
    return api.delete(`/brands/${id}`);
  },
  
  // Kiểm tra xem thương hiệu có đang được sử dụng bởi sản phẩm nào không
  checkBrandInUse: async (id) => {
    return api.get(`/brands/${id}/check-in-use`);
  },
  
  // Vô hiệu hóa thương hiệu thay vì xóa
  deactivateBrand: async (id) => {
    return api.put(`/brands/${id}/deactivate`, {});
  }
};

export default BrandService;
