import React, { useState, useEffect } from 'react';
import { Table, Button, Space, Popconfirm, Typography, Input, Card, Tag } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, SearchOutlined, EyeOutlined } from '@ant-design/icons';
import { Toaster, toast } from 'react-hot-toast';
import { Link } from 'react-router-dom';
import PaymentService from '../../services/payment.service';
import ReceiptTypeService from '../../services/receiptType.service';
import { formatCurrencyWithSymbol } from '../../utils/format';

const { Title } = Typography;

const PaymentList = () => {
  const [payments, setPayments] = useState([]);
  const [receiptTypes, setReceiptTypes] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    setLoading(true);
    try {
      const [paymentsResponse, receiptTypesResponse] = await Promise.all([
        PaymentService.getAllPayments(),
        ReceiptTypeService.getAllReceiptTypes()
      ]);

      // Lưu danh sách phiếu chi
      setPayments(paymentsResponse.data);

      // Lưu danh sách loại thu/chi
      setReceiptTypes(receiptTypesResponse.data);

      const enhancedPayments = paymentsResponse.data.map(payment => {

        // Kiểm tra nếu có bankAccountId thì đây là loại Tài khoản (ID: 2)
        if (payment.bankAccountId) {

          const transferType = receiptTypesResponse.data.find(type => Number(type.id) === 2);

          if (transferType) {
            return {
              ...payment,
              paymentType: transferType,
              receiptType: transferType
            };
          } else {
            const tempTransferType = {
              id: 2,
              name: 'Tài khoản',
              type: 'transfer'
            };
            return {
              ...payment,
              paymentType: tempTransferType,
              receiptType: tempTransferType
            };
          }
        } else {
          // Nếu không có bankAccountId, kiểm tra các trường khác
          const typeId = payment.paymentTypeId || payment.receiptTypeId;

          if (typeId) {
            // Chuyển đổi ID sang số để so sánh chính xác
            const numericTypeId = Number(typeId);

            const matchingType = receiptTypesResponse.data.find(type => Number(type.id) === numericTypeId);

            if (matchingType) {
              return {
                ...payment,
                paymentType: matchingType,
                receiptType: matchingType
              };
            } else {
              const tempType = {
                id: numericTypeId,
                name: numericTypeId === 1 ? 'Tiền mặt' : `Loại chi #${numericTypeId}`,
                type: numericTypeId === 2 ? 'transfer' : 'other'
              };
              return {
                ...payment,
                paymentType: tempType,
                receiptType: tempType
              };
            }
          } else {
            const cashType = receiptTypesResponse.data.find(type => Number(type.id) === 1);

            if (cashType) {
              return {
                ...payment,
                paymentType: cashType,
                receiptType: cashType
              };
            } else {
              const tempCashType = {
                id: 1,
                name: 'Tiền mặt',
                type: 'other'
              };
              return {
                ...payment,
                paymentType: tempCashType,
                receiptType: tempCashType
              };
            }
          }
        }
      });

      setPayments(enhancedPayments);
    } catch (error) {
      toast.error('Không thể tải danh sách phiếu chi: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    try {
      await PaymentService.deletePayment(id);
      toast.success('Xóa phiếu chi thành công');
      fetchData();
    } catch (error) {
      toast.error('Không thể xóa phiếu chi: ' + error.message);
    }
  };

  const filteredPayments = payments.filter(
    (payment) =>
      payment.paymentCode?.toLowerCase().includes(searchText.toLowerCase()) ||
      payment.receiverName?.toLowerCase().includes(searchText.toLowerCase())
  );

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'green';
      case 'pending':
        return 'orange';
      case 'cancelled':
        return 'red';
      default:
        return 'default';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'completed':
        return 'Đã hoàn thành';
      case 'pending':
        return 'Chờ xử lý';
      case 'cancelled':
        return 'Đã hủy';
      default:
        return status;
    }
  };

  const columns = [
    {
      title: <span style={{ fontSize: 16 }}>Mã phiếu chi</span>,
      dataIndex: 'paymentCode',
      key: 'paymentCode',
    },
    {
      title: <span style={{ fontSize: 16 }}>Ngày chi</span>,
      dataIndex: 'paymentDate',
      key: 'paymentDate',
      render: (date) => new Date(date).toLocaleDateString('vi-VN'),
      sorter: (a, b) => new Date(a.paymentDate) - new Date(b.paymentDate),
    },
    {
      title: <span style={{ fontSize: 16 }}>Người nhận</span>,
      dataIndex: 'receiverName',
      key: 'receiverName',
    },
    {
      title: <span style={{ fontSize: 16 }}>Loại chi</span>,
      key: 'paymentType',
      render: (_, record) => {
        // Kiểm tra nếu có bankAccountId thì đây là loại Tài khoản
        if (record.bankAccountId) {
          return 'Tài khoản';
        }

        // Ưu tiên hiển thị từ paymentType, nếu không có thì dùng receiptType
        if (record.paymentType && record.paymentType.name) {
          return record.paymentType.name;
        } else if (record.receiptType && record.receiptType.name) {
          return record.receiptType.name;
        } else if (record.paymentTypeId) {
          // Tìm trong danh sách receiptTypes
          const matchingType = receiptTypes.find(type => Number(type.id) === Number(record.paymentTypeId));
          return matchingType ? matchingType.name : 'N/A';
        } else if (record.receiptTypeId) {
          // Tìm trong danh sách receiptTypes
          const matchingType = receiptTypes.find(type => Number(type.id) === Number(record.receiptTypeId));
          return matchingType ? matchingType.name : 'N/A';
        }

        // Mặc định là Tiền mặt nếu không có thông tin
        return 'Tiền mặt';
      },
    },
    {
      title: <span style={{ fontSize: 16 }}>Số tiền</span>,
      dataIndex: 'amount',
      key: 'amount',
      render: (amount) => formatCurrencyWithSymbol(amount),
      sorter: (a, b) => a.amount - b.amount,
    },
    {
      title: <span style={{ fontSize: 16 }}>Trạng thái</span>,
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: <span style={{ fontSize: 16 }}>Thao tác</span>,
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Link to={`/payments/view/${record.id}`}>
            <Button type="default" icon={<EyeOutlined />} size="small" style={{ height: 40, fontSize: 14, display: 'flex', alignItems: 'center', padding: '0 15px' }}>
              Xem
            </Button>
          </Link>
          <Link to={`/payments/edit/${record.id}`}>
            <Button type="primary" icon={<EditOutlined />} size="small" style={{ height: 40, fontSize: 14, display: 'flex', alignItems: 'center', padding: '0 15px' }}>
              Sửa
            </Button>
          </Link>
          <Popconfirm
            title="Bạn có chắc chắn muốn xóa phiếu chi này?"
            onConfirm={() => handleDelete(record.id)}
            okText="Có"
            cancelText="Không"
          >
            <Button type="primary" danger icon={<DeleteOutlined />} size="small" style={{ height: 40, fontSize: 14, display: 'flex', alignItems: 'center', padding: '0 15px' }}>
              Xóa
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Toaster position="top-right" />
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 20 }}>
        <Title level={2} style={{ fontSize: 28, background: 'linear-gradient(90deg, #1890ff, #096dd9)', WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent' }}>Quản lý phiếu chi</Title>
        <Link to="/payments/add">
          <Button type="primary" icon={<PlusOutlined />} style={{ height: 40, fontSize: 16, display: 'flex', alignItems: 'center' }}>
            Tạo phiếu chi
          </Button>
        </Link>
      </div>

      <Card style={{ marginBottom: 16 }}>
        <Input
          placeholder="Tìm kiếm phiếu chi..."
          prefix={<SearchOutlined />}
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          style={{ width: 300, height: 42, fontSize: 16 }}
        />
      </Card>

      <Table
        columns={columns}
        dataSource={filteredPayments}
        rowKey="id"
        loading={loading}
        pagination={{ pageSize: 10, style: { fontSize: 16 } }}
        style={{ fontSize: 16 }}
      />
    </div>
  );
};

export default PaymentList;
