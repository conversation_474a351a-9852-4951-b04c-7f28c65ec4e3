import React, { useState, useEffect } from 'react';
import { Table, Button, Space, Popconfirm, Typography, Input, Card, Tag } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, SearchOutlined, EyeOutlined } from '@ant-design/icons';
import { Toaster, toast } from 'react-hot-toast';
import { Link } from 'react-router-dom';
import ReceiptService from '../../services/receipt.service';
import ReceiptTypeService from '../../services/receiptType.service';
import { formatCurrencyWithSymbol } from '../../utils/format';

const { Title } = Typography;

const ReceiptList = () => {
  const [receipts, setReceipts] = useState([]);
  const [receiptTypes, setReceiptTypes] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    setLoading(true);
    try {
      // Lấy danh sách loại thu chi trước
      const receiptTypeResponse = await ReceiptTypeService.getAllReceiptTypes();
      setReceiptTypes(receiptTypeResponse.data);

      // Sau đó lấy danh sách phiếu thu
      const receiptResponse = await ReceiptService.getAllReceipts();

      // Bổ sung thông tin loại thu nếu thiếu
      const enhancedReceipts = receiptResponse.data.map(receipt => {
        // Nếu đã có thông tin đầy đủ về receiptType, giữ nguyên
        if (receipt.receiptType && receipt.receiptType.name) {
          return receipt;
        }

        // Nếu chỉ có receiptTypeId, tìm thông tin loại thu tương ứng
        if (receipt.receiptTypeId) {
          const matchingType = receiptTypeResponse.data.find(
            type => type.id === receipt.receiptTypeId
          );

          if (matchingType) {
            return {
              ...receipt,
              receiptType: matchingType
            };
          }
        }

        return receipt;
      });

      setReceipts(enhancedReceipts);
    } catch (error) {
      toast.error('Không thể tải dữ liệu: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const fetchReceipts = async () => {
    await fetchData();
  };

  const handleDelete = async (id) => {
    try {
      await ReceiptService.deleteReceipt(id);
      toast.success('Xóa phiếu thu thành công');
      fetchReceipts();
    } catch (error) {
      toast.error('Không thể xóa phiếu thu: ' + error.message);
    }
  };

  const filteredReceipts = receipts.filter(
    (receipt) =>
      receipt.receiptCode?.toLowerCase().includes(searchText.toLowerCase()) ||
      receipt.payerName?.toLowerCase().includes(searchText.toLowerCase())
  );

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'green';
      case 'pending':
        return 'orange';
      case 'cancelled':
        return 'red';
      default:
        return 'default';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'completed':
        return 'Đã hoàn thành';
      case 'pending':
        return 'Chờ xử lý';
      case 'cancelled':
        return 'Đã hủy';
      default:
        return status;
    }
  };

  const columns = [
    {
      title: <span style={{ fontSize: 16 }}>Mã phiếu thu</span>,
      dataIndex: 'receiptCode',
      key: 'receiptCode',
      render: (text) => <span style={{ fontSize: '16px' }}>{text}</span>
    },
    {
      title: <span style={{ fontSize: 16 }}>Ngày thu</span>,
      dataIndex: 'receiptDate',
      key: 'receiptDate',
      render: (date) => <span style={{ fontSize: '16px' }}>{new Date(date).toLocaleDateString('vi-VN')}</span>,
      sorter: (a, b) => new Date(a.receiptDate) - new Date(b.receiptDate),
    },
    {
      title: <span style={{ fontSize: 16 }}>Người nộp</span>,
      dataIndex: 'payerName',
      key: 'payerName',
      render: (text) => <span style={{ fontSize: '16px' }}>{text}</span>
    },
    {
      title: <span style={{ fontSize: 16 }}>Loại thu</span>,
      dataIndex: ['receiptType', 'name'],
      key: 'receiptType',
      render: (text, record) => {
        // Kiểm tra cả receiptType và receiptType.name
        let displayText = 'N/A';
        if (record.receiptType && record.receiptType.name) {
          displayText = record.receiptType.name;
        }
        // Nếu có receiptTypeId nhưng không có receiptType.name
        else if (record.receiptTypeId) {
          displayText = `Loại thu #${record.receiptTypeId}`;
        }
        return <span style={{ fontSize: '16px' }}>{displayText}</span>;
      },
    },
    {
      title: <span style={{ fontSize: 16 }}>Số tiền</span>,
      dataIndex: 'amount',
      key: 'amount',
      render: (amount) => <span style={{ fontSize: '16px' }}>{formatCurrencyWithSymbol(amount)}</span>,
      sorter: (a, b) => a.amount - b.amount,
    },
    {
      title: <span style={{ fontSize: 16 }}>Trạng thái</span>,
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)} style={{ fontSize: '14px', padding: '2px 10px' }}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: <span style={{ fontSize: 16 }}>Thao tác</span>,
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Link to={`/receipts/view/${record.id}`}>
            <Button 
              type="default" 
              icon={<EyeOutlined />} 
              style={{ fontSize: 16, height: 40, padding: '0 15px', display: 'flex', alignItems: 'center' }}
            >
              Xem
            </Button>
          </Link>
          <Link to={`/receipts/edit/${record.id}`}>
            <Button 
              type="primary" 
              icon={<EditOutlined />} 
              style={{ fontSize: 16, height: 40, padding: '0 15px', display: 'flex', alignItems: 'center' }}
            >
              Sửa
            </Button>
          </Link>
          <Popconfirm
            title="Bạn có chắc chắn muốn xóa phiếu thu này?"
            onConfirm={() => handleDelete(record.id)}
            okText="Có"
            cancelText="Không"
          >
            <Button 
              type="primary" 
              danger 
              icon={<DeleteOutlined />} 
              style={{ fontSize: 16, height: 40, padding: '0 15px', display: 'flex', alignItems: 'center' }}
            >
              Xóa
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ fontSize: '16px' }}> {/* Tăng kích thước phông chữ cơ bản thêm 2px */}
      <Toaster position="top-right" />
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 20 }}>
        <Title level={2} style={{ fontSize: 28, background: 'linear-gradient(90deg, #1890ff, #096dd9)', WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent' }}>Quản lý phiếu thu</Title>
        <Link to="/receipts/add">
          <Button 
            type="primary" 
            icon={<PlusOutlined />} 
            size="large" 
            style={{ height: '40px', fontWeight: 'bold', fontSize: '16px', padding: '0 20px' }}
          >
            Tạo phiếu thu
          </Button>
        </Link>
      </div>

      <Card style={{ marginBottom: 20 }}>
        <Input
          placeholder="Tìm kiếm phiếu thu..."
          prefix={<SearchOutlined style={{ fontSize: '18px' }} />}
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          style={{ width: 350, height: '42px', fontSize: '16px' }}
        />
      </Card>

      <Table
        columns={columns}
        dataSource={filteredReceipts}
        rowKey="id"
        loading={loading}
        pagination={{ pageSize: 10, style: { fontSize: 16 } }}
        style={{ fontSize: '16px' }}
      />
    </div>
  );
};

export default ReceiptList;
