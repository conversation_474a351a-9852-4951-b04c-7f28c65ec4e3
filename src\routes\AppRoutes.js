import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import Navbar from '../components/Common/Navbar';
import PrivateRoute from '../components/Common/PrivateRoute';
import NotFound from '../components/Common/NotFound';
import Dashboard from '../components/Common/Dashboard';
import Login from '../components/Auth/Login';
import Profile from '../components/Auth/Profile';

// Sales components
import SalesLayout from '../components/Sales/SalesLayout';
import SalesDashboard from '../components/Sales/SalesDashboard';
import SalesReturn from '../components/Sales/SalesReturn';

// User components
import UserList from '../components/User/UserList';
import UserForm from '../components/User/UserForm';

// Product components
import ProductList from '../components/Product/ProductList';
import ProductForm from '../components/Product/ProductForm';

// Product Category components
import CategoryList from '../components/ProductCategory/CategoryList';
import CategoryForm from '../components/ProductCategory/CategoryForm';

// Brand components
import BrandList from '../components/Brand/BrandList';
import BrandForm from '../components/Brand/BrandForm';

// Customer components
import CustomerList from '../components/Customer/CustomerList';
import CustomerForm from '../components/Customer/CustomerForm';

// Invoice components
import InvoiceList from '../components/Invoice/InvoiceList';
import InvoiceForm from '../components/Invoice/InvoiceForm';
import InvoiceDetail from '../components/Invoice/InvoiceDetail';

// Return components
import ReturnList from '../components/Return/ReturnList';
import ReturnForm from '../components/Return/ReturnForm';
import ReturnDetail from '../components/Return/ReturnDetail';

// Supplier components
import SupplierList from '../components/Supplier/SupplierList';
import SupplierForm from '../components/Supplier/SupplierForm';

// Receipt components
import ReceiptList from '../components/Receipt/ReceiptList';
import ReceiptForm from '../components/Receipt/ReceiptForm';
import ReceiptDetail from '../components/Receipt/ReceiptDetail';

// ReceiptType components
import ReceiptTypeList from '../components/ReceiptType/ReceiptTypeList';
import ReceiptTypeForm from '../components/ReceiptType/ReceiptTypeForm';

// Import components
import ImportList from '../components/Import/ImportList';
import ImportForm from '../components/Import/ImportForm';
import ImportDetail from '../components/Import/ImportDetail';

// Payment components
import PaymentList from '../components/Payment/PaymentList';
import PaymentForm from '../components/Payment/PaymentForm';
import PaymentDetail from '../components/Payment/PaymentDetail';

// Đã loại bỏ PaymentType components vì sử dụng chung với ReceiptType

// Payer components
import PayerList from '../components/Payer/PayerList';
import PayerForm from '../components/Payer/PayerForm';

// BankAccount components
import BankAccountList from '../components/BankAccount/BankAccountList';
import BankAccountForm from '../components/BankAccount/BankAccountForm';

// Branch components
import BranchList from '../components/Branch/BranchList';
import BranchForm from '../components/Branch/BranchForm';

// Report components
import ReportDashboard from '../components/Report/ReportDashboard';

// Setting components
import InventoryNotificationSettings from '../components/Setting/InventoryNotificationSettings';

const AppRoutes = () => {
  return (
    <Routes>
      {/* Public routes */}
      <Route path="/login" element={<Login />} />

      {/* Protected routes */}
      <Route element={<PrivateRoute />}>
        <Route
          path="/"
          element={
            <Navbar>
              <Dashboard />
            </Navbar>
          }
        />

        <Route
          path="/profile"
          element={
            <Navbar>
              <Profile />
            </Navbar>
          }
        />

        {/* User routes */}
        <Route
          path="/users"
          element={
            <Navbar>
              <UserList />
            </Navbar>
          }
        />
        <Route
          path="/users/add"
          element={
            <Navbar>
              <UserForm />
            </Navbar>
          }
        />
        <Route
          path="/users/edit/:id"
          element={
            <Navbar>
              <UserForm />
            </Navbar>
          }
        />

        {/* Product routes */}
        <Route
          path="/products"
          element={
            <Navbar>
              <ProductList />
            </Navbar>
          }
        />
        <Route
          path="/products/add"
          element={
            <Navbar>
              <ProductForm />
            </Navbar>
          }
        />
        <Route
          path="/products/edit/:id"
          element={
            <Navbar>
              <ProductForm />
            </Navbar>
          }
        />

        {/* Product Category routes */}
        <Route
          path="/product-categories"
          element={
            <Navbar>
              <CategoryList />
            </Navbar>
          }
        />
        <Route
          path="/product-categories/add"
          element={
            <Navbar>
              <CategoryForm />
            </Navbar>
          }
        />
        <Route
          path="/product-categories/edit/:id"
          element={
            <Navbar>
              <CategoryForm />
            </Navbar>
          }
        />

        {/* Brand routes */}
        <Route
          path="/brands"
          element={
            <Navbar>
              <BrandList />
            </Navbar>
          }
        />
        <Route
          path="/brands/add"
          element={
            <Navbar>
              <BrandForm />
            </Navbar>
          }
        />
        <Route
          path="/brands/edit/:id"
          element={
            <Navbar>
              <BrandForm />
            </Navbar>
          }
        />

        {/* Customer routes */}
        <Route
          path="/customers"
          element={
            <Navbar>
              <CustomerList />
            </Navbar>
          }
        />
        <Route
          path="/customers/add"
          element={
            <Navbar>
              <CustomerForm />
            </Navbar>
          }
        />
        <Route
          path="/customers/edit/:id"
          element={
            <Navbar>
              <CustomerForm />
            </Navbar>
          }
        />

        {/* Invoice routes */}
        <Route
          path="/invoices"
          element={
            <Navbar>
              <InvoiceList />
            </Navbar>
          }
        />
        <Route
          path="/invoices/add"
          element={
            <Navbar>
              <InvoiceForm />
            </Navbar>
          }
        />
        <Route
          path="/invoices/edit/:id"
          element={
            <Navbar>
              <InvoiceForm />
            </Navbar>
          }
        />
        <Route
          path="/invoices/view/:id"
          element={
            <Navbar>
              <InvoiceDetail />
            </Navbar>
          }
        />

        {/* Return routes */}
        <Route
          path="/returns"
          element={
            <Navbar>
              <ReturnList />
            </Navbar>
          }
        />
        <Route
          path="/returns/add"
          element={
            <Navbar>
              <ReturnForm />
            </Navbar>
          }
        />
        <Route
          path="/returns/edit/:id"
          element={
            <Navbar>
              <ReturnForm />
            </Navbar>
          }
        />
        <Route
          path="/returns/view/:id"
          element={
            <Navbar>
              <ReturnDetail />
            </Navbar>
          }
        />

        {/* Supplier routes */}
        <Route
          path="/suppliers"
          element={
            <Navbar>
              <SupplierList />
            </Navbar>
          }
        />
        <Route
          path="/suppliers/add"
          element={
            <Navbar>
              <SupplierForm />
            </Navbar>
          }
        />
        <Route
          path="/suppliers/edit/:id"
          element={
            <Navbar>
              <SupplierForm />
            </Navbar>
          }
        />

        {/* Receipt routes */}
        <Route
          path="/receipts"
          element={
            <Navbar>
              <ReceiptList />
            </Navbar>
          }
        />
        <Route
          path="/receipts/add"
          element={
            <Navbar>
              <ReceiptForm />
            </Navbar>
          }
        />
        <Route
          path="/receipts/edit/:id"
          element={
            <Navbar>
              <ReceiptForm />
            </Navbar>
          }
        />
        <Route
          path="/receipts/view/:id"
          element={
            <Navbar>
              <ReceiptDetail />
            </Navbar>
          }
        />

        {/* ReceiptType routes */}
        <Route
          path="/receipt-types"
          element={
            <Navbar>
              <ReceiptTypeList />
            </Navbar>
          }
        />
        <Route
          path="/receipt-types/add"
          element={
            <Navbar>
              <ReceiptTypeForm />
            </Navbar>
          }
        />
        <Route
          path="/receipt-types/edit/:id"
          element={
            <Navbar>
              <ReceiptTypeForm />
            </Navbar>
          }
        />

        {/* Branch routes */}
        <Route
          path="/branches"
          element={
            <Navbar>
              <BranchList />
            </Navbar>
          }
        />
        <Route
          path="/branches/add"
          element={
            <Navbar>
              <BranchForm />
            </Navbar>
          }
        />
        <Route
          path="/branches/edit/:id"
          element={
            <Navbar>
              <BranchForm />
            </Navbar>
          }
        />

        {/* Import routes */}
        <Route
          path="/imports"
          element={
            <Navbar>
              <ImportList />
            </Navbar>
          }
        />
        <Route
          path="/imports/add"
          element={
            <Navbar>
              <ImportForm />
            </Navbar>
          }
        />
        <Route
          path="/imports/edit/:id"
          element={
            <Navbar>
              <ImportForm />
            </Navbar>
          }
        />
        <Route
          path="/imports/view/:id"
          element={
            <Navbar>
              <ImportDetail />
            </Navbar>
          }
        />

        {/* Payment routes */}
        <Route
          path="/payments"
          element={
            <Navbar>
              <PaymentList />
            </Navbar>
          }
        />
        <Route
          path="/payments/add"
          element={
            <Navbar>
              <PaymentForm />
            </Navbar>
          }
        />
        <Route
          path="/payments/edit/:id"
          element={
            <Navbar>
              <PaymentForm />
            </Navbar>
          }
        />
        <Route
          path="/payments/view/:id"
          element={
            <Navbar>
              <PaymentDetail />
            </Navbar>
          }
        />

        {/* PaymentType routes đã được loại bỏ vì sử dụng chung với ReceiptType */}

        {/* Payer routes */}
        <Route
          path="/payers"
          element={
            <Navbar>
              <PayerList />
            </Navbar>
          }
        />
        <Route
          path="/payers/add"
          element={
            <Navbar>
              <PayerForm />
            </Navbar>
          }
        />
        <Route
          path="/payers/edit/:id"
          element={
            <Navbar>
              <PayerForm />
            </Navbar>
          }
        />

        {/* BankAccount routes */}
        <Route
          path="/bank-accounts"
          element={
            <Navbar>
              <BankAccountList />
            </Navbar>
          }
        />
        <Route
          path="/bank-accounts/add"
          element={
            <Navbar>
              <BankAccountForm />
            </Navbar>
          }
        />
        <Route
          path="/bank-accounts/edit/:id"
          element={
            <Navbar>
              <BankAccountForm />
            </Navbar>
          }
        />

        {/* Report routes */}
        <Route
          path="/reports"
          element={
            <Navbar>
              <ReportDashboard />
            </Navbar>
          }
        />

        {/* Setting routes */}
        <Route
          path="/inventory-notification"
          element={
            <Navbar>
              <InventoryNotificationSettings />
            </Navbar>
          }
        />
      </Route>

      {/* Sales routes */}
      <Route element={<PrivateRoute />}>
        <Route
          path="/sales"
          element={
            <SalesLayout>
              <SalesDashboard />
            </SalesLayout>
          }
        />
        <Route
          path="/sales/returns"
          element={
            <SalesLayout>
              <SalesReturn />
            </SalesLayout>
          }
        />
      </Route>

      {/* 404 route */}
      <Route path="/404" element={<NotFound />} />
      <Route path="*" element={<Navigate to="/404" replace />} />
    </Routes>
  );
};

export default AppRoutes;
