import api from './api';

const InvoiceService = {
  getAllInvoices: async () => {
    return api.get('/invoices?include=customer');
  },

  getInvoiceById: async (id) => {
    return api.get(`/invoices/${id}?include=Customer,customer,InvoiceItems,InvoiceItems.Product&customer_fields=id,name,phone,address,customerCode`);
  },

  createInvoice: async (invoiceData) => {
    // Đảm bảo dữ liệu gửi đi là hợp lệ
    const cleanedData = { ...invoiceData };

    // Đảm bảo các trường số là số hợp lệ
    if (typeof cleanedData.totalAmount === 'string') {
      cleanedData.totalAmount = parseFloat(cleanedData.totalAmount) || 0;
    }

    if (typeof cleanedData.paidAmount === 'string') {
      cleanedData.paidAmount = parseFloat(cleanedData.paidAmount) || 0;
    }

    return api.post('/invoices', cleanedData);
  },

  updateInvoice: async (id, invoiceData) => {
    // Đảm bảo dữ liệu gửi đi là hợp lệ
    const cleanedData = { ...invoiceData };

    // Xóa các trường không cần thiết hoặc có thể gây lỗi
    if (cleanedData.InvoiceItems) delete cleanedData.InvoiceItems;
    if (cleanedData.Customer) delete cleanedData.Customer;
    if (cleanedData.customer) delete cleanedData.customer;

    // Đảm bảo các trường số là số hợp lệ
    if (typeof cleanedData.totalAmount === 'string') {
      cleanedData.totalAmount = parseFloat(cleanedData.totalAmount) || 0;
    }

    if (typeof cleanedData.paidAmount === 'string') {
      cleanedData.paidAmount = parseFloat(cleanedData.paidAmount) || 0;
    }

    return api.put(`/invoices/${id}`, cleanedData);
  },

  deleteInvoice: async (id) => {
    return api.delete(`/invoices/${id}`);
  },

  // Thêm hàm mới để cập nhật trạng thái hóa đơn
  updateInvoiceStatus: async (id, status) => {
    return api.put(`/invoices/${id}/status`, { status });
  },
  
  // Kiểm tra xem hóa đơn có phiếu trả hàng liên quan không
  checkInvoiceHasReturns: async (id) => {
    try {
      // Sử dụng API mới từ backend để kiểm tra phiếu trả hàng liên quan
      const response = await api.get(`/invoices/${id}/check-returns`);
      return response.data;
    } catch (error) {
      console.error('Lỗi khi kiểm tra phiếu trả hàng liên quan:', error);
      throw error;
    }
  }
};

export default InvoiceService;
