/* CSS cho việc in tem mã vạch */
@media print {
  /* Ẩn tất cả các phần tử khác khi in */
  body * {
    visibility: hidden;
  }
  
  /* Chỉ hiển thị phần tử cần in */
  .barcode-print-container,
  .barcode-print-container * {
    visibility: visible !important;
    display: block !important;
  }
  
  /* Đặt vị trí của phần tử cần in */
  .barcode-print-container {
    position: fixed !important;
    left: 0 !important;
    top: 0 !important;
    width: 100% !important;
    height: 100% !important;
    padding: 10px !important;
    z-index: 9999 !important;
    background-color: white !important;
  }
  
  /* Ẩn các phần tử không cần thiết */
  .preview-container {
    display: none !important;
  }
  
  /* Định dạng cho container chứa các tem */
  .barcode-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 5mm;
    margin: 0 auto;
    max-width: 180mm; /* <PERSON><PERSON><PERSON><PERSON> hạn chiều rộng để nằm gi<PERSON>a khổ giấy */
  }
  
  /* Đ<PERSON>nh dạng cho từng tem */
  .barcode-item {
    page-break-inside: avoid;
    border: 1px dashed #ccc;
    padding: 5mm;
    margin-bottom: 5mm;
    text-align: center;
    break-inside: avoid;
    overflow: hidden;
    width: 100%;
    box-sizing: border-box;
  }
  
  /* Đảm bảo mã vạch hiển thị đúng kích thước */
  .barcode-item svg {
    max-width: 100% !important;
    height: auto !important;
    width: auto !important;
  }
  
  /* Đảm bảo mã vạch không bị tràn ra ngoài */
  .barcode-item > div {
    overflow: hidden;
    width: 100%;
  }
}

/* Ẩn phần in khi không in */
.barcode-print-container {
  display: none;
}

/* CSS cho phần xem trước */
.barcode-preview {
  border: 1px dashed #ccc;
  padding: 10px;
  margin-bottom: 10px;
  text-align: center;
}

.barcode-grid-preview {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  max-width: 400px;
  margin: 0 auto;
}

.barcode-item-preview {
  border: 1px dashed #ccc;
  padding: 5px;
  text-align: center;
}
