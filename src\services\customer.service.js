import api from './api';

const CustomerService = {
  getAllCustomers: async () => {
    return api.get('/customers');
  },

  getCustomerById: async (id) => {
    return api.get(`/customers/${id}`);
  },

  createCustomer: async (customerData) => {
    return api.post('/customers', customerData);
  },

  updateCustomer: async (id, customerData) => {
    return api.put(`/customers/${id}`, customerData);
  },

  deleteCustomer: async (id) => {
    return api.delete(`/customers/${id}`);
  }
};

export default CustomerService;
