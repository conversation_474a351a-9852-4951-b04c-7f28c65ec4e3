# Kho Hàng - Frontend

Ứng dụng quản lý kho hàng - Phần frontend

## Cài đặt

1. Clone repository
2. Cài đặt các dependencies:

```bash
npm install
```

## Cấu hình

Đã tạo file `.env` với nội dung sau:

```
PORT=3001
REACT_APP_API_URL=http://localhost:3000/api
```

Lưu ý: Đ<PERSON>m bảo cổng (PORT) không trùng với cổng của backend.

## Chạy ứng dụng

```bash
npm start
```

Ứng dụng sẽ chạy tại địa chỉ [http://localhost:3001](http://localhost:3001).

## Cấu trúc thư mục

- `src/components`: Chứa các components của ứng dụng
  - `Auth`: Components liên quan đến xác thực
  - `User`: Components quản lý người dùng
  - `Product`: Components quản lý sản phẩm
  - `ProductCategory`: Components quản lý nhóm hàng
  - `Brand`: Components quản lý thương hiệu
  - `Customer`: Components quản lý khách hàng
  - `Invoice`: Components quản lý hoá đơn
  - `Common`: Components dùng chung
- `src/services`: Chứa các service gọi API
- `src/context`: Chứa các context của ứng dụng
- `src/routes`: Cấu hình routing
- `src/utils`: Các tiện ích

## Công nghệ sử dụng

- React
- React Router
- Ant Design
- Axios

## Các lệnh khác

### `npm test`

Chạy các bài kiểm thử.

### `npm run build`

Build ứng dụng cho môi trường production.
