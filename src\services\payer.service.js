import api from './api';

const PayerService = {
  getAllPayers: async () => {
    return api.get('/payers');
  },

  getPayerById: async (id) => {
    return api.get(`/payers/${id}`);
  },

  createPayer: async (payerData) => {
    return api.post('/payers', payerData);
  },

  updatePayer: async (id, payerData) => {
    return api.put(`/payers/${id}`, payerData);
  },

  deletePayer: async (id) => {
    return api.delete(`/payers/${id}`);
  }
};

export default PayerService;
