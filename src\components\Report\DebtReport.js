import React, { useState, useEffect } from 'react';
import { Card, DatePicker, Button, Select, Form, Space, Typography, Table, Tabs, Statistic, Row, Col, Tag } from 'antd';
import { Toaster, toast } from 'react-hot-toast';
import { SearchOutlined, ReloadOutlined, UserOutlined, ShopOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import moment from 'moment';
import ReportService from '../../services/report.service';
import BranchService from '../../services/branch.service';
import CustomerService from '../../services/customer.service';
import SupplierService from '../../services/supplier.service';
import { formatCurrencyWithSymbol } from '../../utils/format';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

const DebtReport = () => {
  const [activeTab, setActiveTab] = useState('customer');
  const [loading, setLoading] = useState(false);
  const [branches, setBranches] = useState([]);
  const [customers, setCustomers] = useState([]);
  const [suppliers, setSuppliers] = useState([]);
  const [customerDebtData, setCustomerDebtData] = useState([]);
  const [supplierDebtData, setSupplierDebtData] = useState([]);
  const [summary, setSummary] = useState({
    totalCustomerDebt: 0,
    totalSupplierDebt: 0,
    customerCount: 0,
    supplierCount: 0,
    highRiskCustomerCount: 0,
    highRiskSupplierCount: 0
  });
  const [form] = Form.useForm();

  useEffect(() => {
    fetchBranches();
    fetchCustomers();
    fetchSuppliers();
    fetchDebtReport();
  }, []);

  const fetchBranches = async () => {
    try {
      const response = await BranchService.getAllBranches();
      setBranches(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách chi nhánh: ' + error.message);
    }
  };

  const fetchCustomers = async () => {
    try {
      const response = await CustomerService.getAllCustomers();
      setCustomers(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách khách hàng: ' + error.message);
    }
  };

  const fetchSuppliers = async () => {
    try {
      const response = await SupplierService.getAllSuppliers();
      setSuppliers(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách nhà cung cấp: ' + error.message);
    }
  };

  const fetchDebtReport = async (values = {}) => {
    setLoading(true);
    try {
      const options = {
        branchId: values.branchId,
        customerId: values.customerId,
        supplierId: values.supplierId,
      };

      if (values.dateRange && values.dateRange.length === 2) {
        options.startDate = values.dateRange[0].format('YYYY-MM-DD');
        options.endDate = values.dateRange[1].format('YYYY-MM-DD');
      }

      // Fetch customer debt data
      const customerResponse = await fetchCustomerDebt(options);
      
      // Fetch supplier debt data
      const supplierResponse = await fetchSupplierDebt(options);
      
      // Calculate summary
      calculateSummary(customerResponse, supplierResponse);
      
      console.log('Fetched debt report data:', { customerResponse, supplierResponse });
    } catch (error) {
      console.error('Error fetching debt report:', error);
      toast.error('Không thể tải báo cáo công nợ: ' + error.message);
      // Đặt dữ liệu mặc định để tránh giao diện bị lỗi
      setCustomerDebtData([]);
      setSupplierDebtData([]);
      setSummary({
        totalCustomerDebt: 0,
        totalSupplierDebt: 0,
        customerCount: 0,
        supplierCount: 0,
        highRiskCustomerCount: 0,
        highRiskSupplierCount: 0
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchCustomerDebt = async (options) => {
    try {
      // Đầu tiên thử gọi API chính
      const response = await ReportService.getCustomerDebt(options);
      
      // Kiểm tra cấu trúc dữ liệu trả về từ API
      if (response?.data?.data?.customers && Array.isArray(response.data.data.customers)) {
        // Chuyển đổi dữ liệu từ API để phù hợp với cấu trúc hiển thị
        const customerDebtData = response.data.data.customers.map(item => {
          return {
            id: item.customer?.id,
            code: item.customer?.customerCode || item.customer?.code || `KH${item.customer?.id || ''}`,
            name: item.customer?.name || 'Khách hàng không xác định',
            phone: item.customer?.phone || '',
            totalDebt: item.totalDebt || 0,
            invoiceCount: item.invoiceCount || 0,
            invoices: Array.isArray(item.invoices) ? item.invoices.map(invoice => ({
              id: invoice.id,
              code: invoice.invoiceCode || invoice.code || `HD${invoice.id || ''}`,
              date: invoice.createdAt,
              totalAmount: invoice.totalAmount || 0,
              paidAmount: invoice.paidAmount || 0,
              debtAmount: invoice.debtAmount || (invoice.totalAmount - invoice.paidAmount) || 0
            })) : []
          };
        });
        
        setCustomerDebtData(customerDebtData);
        return customerDebtData;
      }
      
      // Nếu API trả về dữ liệu không đúng định dạng, sử dụng phương pháp dự phòng
      throw new Error('API trả về dữ liệu không đúng định dạng');
      
    } catch (apiError) {
      console.warn('Sử dụng phương pháp dự phòng để tính công nợ khách hàng:', apiError.message);
      
      try {
        // Phương pháp dự phòng: Tự tính toán công nợ từ hóa đơn và khách hàng
        // Lấy danh sách hóa đơn
        const invoiceResponse = await ReportService.getInvoices(options);
        
        // Lấy danh sách khách hàng để có thông tin chính xác
        const customerResponse = await CustomerService.getAllCustomers();
        const customers = customerResponse?.data || [];
        const customerMap = new Map();
        customers.forEach(customer => {
          customerMap.set(customer.id, customer);
        });
        
        // Xử lý dữ liệu để tính công nợ từ hóa đơn
        const customerDebtMap = new Map();
        
        if (invoiceResponse?.data && Array.isArray(invoiceResponse.data)) {
          invoiceResponse.data.forEach(invoice => {
            const customerId = invoice.customerId;
            if (!customerId) return; // Bỏ qua nếu không có customerId
            
            const totalAmount = Number(invoice.totalAmount || 0);
            const paidAmount = Number(invoice.paidAmount || 0);
            const debtAmount = totalAmount - paidAmount;
            
            if (debtAmount > 0) {
              // Lấy thông tin khách hàng từ danh sách khách hàng
              const customerInfo = customerMap.get(customerId) || {};
              
              if (customerDebtMap.has(customerId)) {
                const customer = customerDebtMap.get(customerId);
                customer.totalDebt += debtAmount;
                customer.invoiceCount += 1;
                customer.invoices.push({
                  id: invoice.id,
                  code: invoice.invoiceCode || invoice.code || `HD${invoice.id || ''}`,
                  date: invoice.createdAt,
                  totalAmount,
                  paidAmount,
                  debtAmount
                });
              } else {
                customerDebtMap.set(customerId, {
                  id: customerId,
                  code: customerInfo.customerCode || customerInfo.code || `KH${customerId}`,
                  name: customerInfo.name || invoice.customerName || 'Khách hàng không xác định',
                  phone: customerInfo.phone || invoice.customerPhone || '',
                  totalDebt: debtAmount,
                  invoiceCount: 1,
                  invoices: [{
                    id: invoice.id,
                    code: invoice.invoiceCode || invoice.code || `HD${invoice.id || ''}`,
                    date: invoice.createdAt,
                    totalAmount,
                    paidAmount,
                    debtAmount
                  }]
                });
              }
            }
          });
        }
        
        const customerDebtData = Array.from(customerDebtMap.values());
        setCustomerDebtData(customerDebtData);
        
        return customerDebtData;
      } catch (fallbackError) {
        console.error('Phương pháp dự phòng cũng thất bại:', fallbackError);
        toast.error('Không thể tính toán công nợ khách hàng: ' + fallbackError.message);
        setCustomerDebtData([]);
        return [];
      }
    }
  };

  const fetchSupplierDebt = async (options) => {
    try {
      // Đầu tiên thử gọi API chính
      const response = await ReportService.getSupplierDebt(options);
      
      // Kiểm tra cấu trúc dữ liệu trả về từ API
      if (response?.data?.data?.suppliers && Array.isArray(response.data.data.suppliers)) {
        // Chuyển đổi dữ liệu từ API để phù hợp với cấu trúc hiển thị
        const supplierDebtData = response.data.data.suppliers.map(item => {
          return {
            id: item.supplier?.id,
            code: item.supplier?.supplierCode || item.supplier?.code || `NCC${item.supplier?.id || ''}`,
            name: item.supplier?.name || 'Nhà cung cấp không xác định',
            phone: item.supplier?.phone || '',
            totalDebt: item.totalDebt || 0,
            importCount: item.importCount || 0,
            imports: Array.isArray(item.imports) ? item.imports.map(importItem => ({
              id: importItem.id,
              code: importItem.importCode || importItem.code || `NK${importItem.id || ''}`,
              date: importItem.createdAt,
              totalAmount: importItem.totalAmount || 0,
              paidAmount: importItem.paidAmount || 0,
              debtAmount: importItem.debtAmount || (importItem.totalAmount - importItem.paidAmount) || 0
            })) : []
          };
        });
        
        setSupplierDebtData(supplierDebtData);
        return supplierDebtData;
      }
      
      // Nếu API trả về dữ liệu không đúng định dạng, sử dụng phương pháp dự phòng
      throw new Error('API trả về dữ liệu không đúng định dạng');
      
    } catch (apiError) {
      console.warn('Sử dụng phương pháp dự phòng để tính công nợ nhà cung cấp:', apiError.message);
      
      try {
        // Phương pháp dự phòng: Tự tính toán công nợ từ nhập hàng và nhà cung cấp
        // Lấy danh sách nhập hàng
        const importResponse = await ReportService.getImports(options);
        
        // Lấy danh sách nhà cung cấp để có thông tin chính xác
        const supplierResponse = await SupplierService.getAllSuppliers();
        const suppliers = supplierResponse?.data || [];
        const supplierMap = new Map();
        suppliers.forEach(supplier => {
          supplierMap.set(supplier.id, supplier);
        });
        
        // Xử lý dữ liệu để tính công nợ từ nhập hàng
        const supplierDebtMap = new Map();
        
        if (importResponse?.data && Array.isArray(importResponse.data)) {
          importResponse.data.forEach(importItem => {
            const supplierId = importItem.supplierId;
            if (!supplierId) return; // Bỏ qua nếu không có supplierId
            
            const totalAmount = Number(importItem.totalAmount || 0);
            const paidAmount = Number(importItem.paidAmount || 0);
            const debtAmount = totalAmount - paidAmount;
            
            if (debtAmount > 0) {
              // Lấy thông tin nhà cung cấp từ danh sách
              const supplierInfo = supplierMap.get(supplierId) || {};
              
              if (supplierDebtMap.has(supplierId)) {
                const supplier = supplierDebtMap.get(supplierId);
                supplier.totalDebt += debtAmount;
                supplier.importCount += 1;
                supplier.imports.push({
                  id: importItem.id,
                  code: importItem.importCode || importItem.code || `NK${importItem.id || ''}`,
                  date: importItem.createdAt,
                  totalAmount,
                  paidAmount,
                  debtAmount
                });
              } else {
                supplierDebtMap.set(supplierId, {
                  id: supplierId,
                  code: supplierInfo.supplierCode || supplierInfo.code || `NCC${supplierId}`,
                  name: supplierInfo.name || importItem.supplierName || 'Nhà cung cấp không xác định',
                  phone: supplierInfo.phone || importItem.supplierPhone || '',
                  totalDebt: debtAmount,
                  importCount: 1,
                  imports: [{
                    id: importItem.id,
                    code: importItem.importCode || importItem.code || `NK${importItem.id || ''}`,
                    date: importItem.createdAt,
                    totalAmount,
                    paidAmount,
                    debtAmount
                  }]
                });
              }
            }
          });
        }
        
        const supplierDebtData = Array.from(supplierDebtMap.values());
        setSupplierDebtData(supplierDebtData);
        
        return supplierDebtData;
      } catch (fallbackError) {
        console.error('Phương pháp dự phòng cũng thất bại:', fallbackError);
        toast.error('Không thể tính toán công nợ nhà cung cấp: ' + fallbackError.message);
        setSupplierDebtData([]);
        return [];
      }
    }
  };

  const calculateSummary = (customerData, supplierData) => {
    // Đảm bảo dữ liệu là mảng hợp lệ
    const validCustomerData = Array.isArray(customerData) ? customerData : [];
    const validSupplierData = Array.isArray(supplierData) ? supplierData : [];
    
    // Tính tổng công nợ khách hàng
    const totalCustomerDebt = validCustomerData.reduce((total, customer) => {
      return total + Number(customer.totalDebt || 0);
    }, 0);

    // Tính tổng công nợ nhà cung cấp
    const totalSupplierDebt = validSupplierData.reduce((total, supplier) => {
      return total + Number(supplier.totalDebt || 0);
    }, 0);

    // Đếm số khách hàng có công nợ cao (ví dụ: > 5 triệu)
    const highRiskThreshold = 5000000; // 5 triệu đồng
    const highRiskCustomerCount = validCustomerData.filter(customer => 
      Number(customer.totalDebt || 0) > highRiskThreshold
    ).length;

    // Đếm số nhà cung cấp có công nợ cao
    const highRiskSupplierCount = validSupplierData.filter(supplier => 
      Number(supplier.totalDebt || 0) > highRiskThreshold
    ).length;

    setSummary({
      totalCustomerDebt,
      totalSupplierDebt,
      customerCount: validCustomerData.length,
      supplierCount: validSupplierData.length,
      highRiskCustomerCount,
      highRiskSupplierCount
    });
  };

  const handleSearch = (values) => {
    fetchDebtReport(values);
  };

  const handleReset = () => {
    form.resetFields();
    fetchDebtReport();
  };

  const handleTabChange = (key) => {
    setActiveTab(key);
  };

  // Định nghĩa cột cho bảng công nợ khách hàng
  const customerColumns = [
    {
      title: <span style={{ fontSize: 16 }}>Mã khách hàng</span>,
      dataIndex: 'code',
      key: 'code',
      width: 120,
    },
    {
      title: <span style={{ fontSize: 16 }}>Tên khách hàng</span>,
      dataIndex: 'name',
      key: 'name',
      width: 200,
    },
    {
      title: 'Số điện thoại',
      dataIndex: 'phone',
      key: 'phone',
      width: 150,
    },
    {
      title: <span style={{ fontSize: 16 }}>Số hóa đơn</span>,
      dataIndex: 'invoiceCount',
      key: 'invoiceCount',
      width: 120,
      align: 'center',
    },
    {
      title: 'Tổng công nợ',
      dataIndex: 'totalDebt',
      key: 'totalDebt',
      width: 180,
      align: 'right',
      render: (value) => formatCurrencyWithSymbol(value),
      sorter: (a, b) => a.totalDebt - b.totalDebt,
    },
    {
      title: 'Mức độ',
      key: 'riskLevel',
      width: 120,
      align: 'center',
      render: (_, record) => {
        let color = 'green';
        let text = 'Thấp';
        
        if (record.totalDebt > 10000000) {
          color = 'red';
          text = 'Cao';
        } else if (record.totalDebt > 5000000) {
          color = 'orange';
          text = 'Trung bình';
        }
        
        return <Tag color={color}>{text}</Tag>;
      },
    },
  ];

  // Định nghĩa cột cho bảng công nợ nhà cung cấp
  const supplierColumns = [
    {
      title: 'Mã nhà cung cấp',
      dataIndex: 'code',
      key: 'code',
      width: 120,
    },
    {
      title: 'Tên nhà cung cấp',
      dataIndex: 'name',
      key: 'name',
      width: 200,
    },
    {
      title: 'Số điện thoại',
      dataIndex: 'phone',
      key: 'phone',
      width: 150,
    },
    {
      title: 'Số phiếu nhập',
      dataIndex: 'importCount',
      key: 'importCount',
      width: 120,
      align: 'center',
    },
    {
      title: 'Tổng công nợ',
      dataIndex: 'totalDebt',
      key: 'totalDebt',
      width: 180,
      align: 'right',
      render: (value) => formatCurrencyWithSymbol(value),
      sorter: (a, b) => a.totalDebt - b.totalDebt,
    },
    {
      title: 'Mức độ',
      key: 'riskLevel',
      width: 120,
      align: 'center',
      render: (_, record) => {
        let color = 'green';
        let text = 'Thấp';
        
        if (record.totalDebt > 10000000) {
          color = 'red';
          text = 'Cao';
        } else if (record.totalDebt > 5000000) {
          color = 'orange';
          text = 'Trung bình';
        }
        
        return <Tag color={color}>{text}</Tag>;
      },
    },
  ];

  // Định nghĩa cột cho bảng chi tiết hóa đơn
  const invoiceDetailColumns = [
    {
      title: 'Mã hóa đơn',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'date',
      key: 'date',
      render: (date) => moment(date).format('DD/MM/YYYY'),
    },
    {
      title: 'Tổng tiền',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      align: 'right',
      render: (value) => formatCurrencyWithSymbol(value),
    },
    {
      title: 'Đã thanh toán',
      dataIndex: 'paidAmount',
      key: 'paidAmount',
      align: 'right',
      render: (value) => formatCurrencyWithSymbol(value),
    },
    {
      title: 'Công nợ',
      dataIndex: 'debtAmount',
      key: 'debtAmount',
      align: 'right',
      render: (value) => formatCurrencyWithSymbol(value),
    },
  ];

  // Định nghĩa cột cho bảng chi tiết phiếu nhập
  const importDetailColumns = [
    {
      title: 'Mã phiếu nhập',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'date',
      key: 'date',
      render: (date) => moment(date).format('DD/MM/YYYY'),
    },
    {
      title: 'Tổng tiền',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      align: 'right',
      render: (value) => formatCurrencyWithSymbol(value),
    },
    {
      title: 'Đã thanh toán',
      dataIndex: 'paidAmount',
      key: 'paidAmount',
      align: 'right',
      render: (value) => formatCurrencyWithSymbol(value),
    },
    {
      title: 'Công nợ',
      dataIndex: 'debtAmount',
      key: 'debtAmount',
      align: 'right',
      render: (value) => formatCurrencyWithSymbol(value),
    },
  ];

  // Định nghĩa expandable row cho bảng khách hàng
  const expandedRowRenderCustomer = (record) => {
    return (
      <Table
        columns={invoiceDetailColumns}
        dataSource={record.invoices}
        pagination={false}
        rowKey="id"
      />
    );
  };

  // Định nghĩa expandable row cho bảng nhà cung cấp
  const expandedRowRenderSupplier = (record) => {
    return (
      <Table
        columns={importDetailColumns}
        dataSource={record.imports}
        pagination={false}
        rowKey="id"
      />
    );
  };

  return (
    <div>
      <Toaster position="top-right" />
      <Card style={{ marginBottom: 20 }}>
        <Form
          form={form}
          layout="inline"
          onFinish={handleSearch}
        >
          <Form.Item name="dateRange" label={<span style={{ fontSize: 16 }}>Khoảng thời gian</span>}>
            <RangePicker format="DD/MM/YYYY" style={{ height: 42, fontSize: 16 }} />
          </Form.Item>
          <Form.Item name="branchId" label={<span style={{ fontSize: 16 }}>Chi nhánh</span>}>
            <Select
              placeholder="Tất cả chi nhánh"
              style={{ width: 200, height: 42, fontSize: 16 }}
              allowClear
            >
              {branches.map((branch) => (
                <Option key={branch.id} value={branch.id}>
                  {branch.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          {activeTab === 'customer' && (
            <Form.Item name="customerId" label={<span style={{ fontSize: 16 }}>Khách hàng</span>}>
              <Select
                placeholder="Tất cả khách hàng"
                style={{ width: 200, height: 42, fontSize: 16 }}
                allowClear
                showSearch
                optionFilterProp="children"
              >
                {customers.map((customer) => (
                  <Option key={customer.id} value={customer.id}>
                    {customer.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          )}
          {activeTab === 'supplier' && (
            <Form.Item name="supplierId" label={<span style={{ fontSize: 16 }}>Nhà cung cấp</span>}>
              <Select
                placeholder="Tất cả nhà cung cấp"
                style={{ width: 200, height: 42, fontSize: 16 }}
                allowClear
                showSearch
                optionFilterProp="children"
              >
                {suppliers.map((supplier) => (
                  <Option key={supplier.id} value={supplier.id}>
                    {supplier.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          )}
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" icon={<SearchOutlined />} style={{ height: 40, fontSize: 16, display: 'flex', alignItems: 'center' }}>
                Tìm kiếm
              </Button>
              <Button onClick={handleReset} icon={<ReloadOutlined />} style={{ height: 40, fontSize: 16, display: 'flex', alignItems: 'center' }}>
                Đặt lại
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      <Card title={<Title level={4} style={{ fontSize: 22, background: 'linear-gradient(90deg, #1890ff, #096dd9)', WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent' }}>Báo cáo công nợ</Title>}>
        {/* Thống kê tổng quan */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={12}>
            <Card>
              <Statistic
                title="Tổng công nợ khách hàng"
                value={formatCurrencyWithSymbol(summary.totalCustomerDebt)}
                prefix={<UserOutlined />}
                loading={loading}
              />
              <div style={{ marginTop: 8 }}>
                <Text style={{ fontSize: 16 }}>Số khách hàng có công nợ: </Text>
                <Text strong style={{ fontSize: 16 }}>{summary.customerCount}</Text>
                {summary.highRiskCustomerCount > 0 && (
                  <Tag color="red" style={{ marginLeft: 8 }}>
                    <ExclamationCircleOutlined /> {summary.highRiskCustomerCount} khách hàng có công nợ cao
                  </Tag>
                )}
              </div>
            </Card>
          </Col>
          <Col span={12}>
            <Card>
              <Statistic
                title="Tổng công nợ nhà cung cấp"
                value={formatCurrencyWithSymbol(summary.totalSupplierDebt)}
                prefix={<ShopOutlined />}
                loading={loading}
              />
              <div style={{ marginTop: 8 }}>
                <Text style={{ fontSize: 16 }}>Số nhà cung cấp có công nợ: </Text>
                <Text strong style={{ fontSize: 16 }}>{summary.supplierCount}</Text>
                {summary.highRiskSupplierCount > 0 && (
                  <Tag color="red" style={{ marginLeft: 8 }}>
                    <ExclamationCircleOutlined /> {summary.highRiskSupplierCount} nhà cung cấp có công nợ cao
                  </Tag>
                )}
              </div>
            </Card>
          </Col>
        </Row>

        {/* Tab công nợ */}
        <Tabs
          activeKey={activeTab}
          onChange={handleTabChange}
          items={[
            {
              key: 'customer',
              label: 'Công nợ khách hàng',
              children: (
                <Table
                  columns={customerColumns}
                  dataSource={customerDebtData}
                  rowKey="id"
                  expandable={{
                    expandedRowRender: expandedRowRenderCustomer,
                  }}
                  loading={loading}
                />
              )
            },
            {
              key: 'supplier',
              label: 'Công nợ nhà cung cấp',
              children: (
                <Table
                  columns={supplierColumns}
                  dataSource={supplierDebtData}
                  rowKey="id"
                  expandable={{
                    expandedRowRender: expandedRowRenderSupplier,
                  }}
                  loading={loading}
                />
              )
            }
          ]}
        />
      </Card>
    </div>
  );
};

export default DebtReport;
