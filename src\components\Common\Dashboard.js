import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Statistic, Typography, Table, Divider, Button, Alert, Tag } from 'antd';
import {
  ShoppingOutlined,
  TeamOutlined,
  FileTextOutlined,
  DollarOutlined,
  BellOutlined,
  ClockCircleOutlined,
  WarningOutlined
} from '@ant-design/icons';
import { Link } from 'react-router-dom';
import ProductService from '../../services/product.service';
import CustomerService from '../../services/customer.service';
import InvoiceService from '../../services/invoice.service';
import SettingService from '../../services/setting.service';
import { formatCurrencyWithSymbol } from '../../utils/format';

const { Title } = Typography;

const Dashboard = () => {
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalProducts: 0,
    totalCustomers: 0,
    totalInvoices: 0,
    totalRevenue: 0
  });
  const [recentInvoices, setRecentInvoices] = useState([]);
  const [lowStockProducts, setLowStockProducts] = useState([]);
  const [outOfStockProducts, setOutOfStockProducts] = useState([]);
  const [agingProducts, setAgingProducts] = useState([]);
  const [settings, setSettings] = useState({
    inventory_aging_months: 6,
    low_stock_threshold: 5
  });

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    setLoading(true);
    try {
      // Lấy cài đặt hệ thống
      const settingsResponse = await SettingService.getAllSettings();
      const settingsData = settingsResponse.data;

      // Chuyển đổi danh sách cài đặt thành đối tượng để dễ truy cập
      const settingsObj = {};
      settingsData.forEach(setting => {
        let value = setting.value;

        // Chuyển đổi giá trị theo kiểu dữ liệu
        if (setting.type === 'number') {
          value = parseFloat(value);
        } else if (setting.type === 'boolean') {
          value = value === 'true';
        } else if (setting.type === 'json') {
          try {
            value = JSON.parse(value);
          } catch (e) {
            console.error('Error parsing JSON setting:', e);
          }
        }

        settingsObj[setting.key] = value;
      });

      setSettings({
        inventory_aging_months: settingsObj.inventory_aging_months || 6,
        low_stock_threshold: settingsObj.low_stock_threshold || 5
      });

      // Lấy danh sách sản phẩm
      const productsResponse = await ProductService.getAllProducts();
      const products = productsResponse.data;

      // Lấy danh sách khách hàng
      const customersResponse = await CustomerService.getAllCustomers();
      const customers = customersResponse.data;

      // Lấy danh sách hóa đơn
      const invoicesResponse = await InvoiceService.getAllInvoices();
      const invoices = invoicesResponse.data;

      // Lấy danh sách sản phẩm tồn kho lâu
      const agingResponse = await ProductService.getAgingInventory(settingsObj.inventory_aging_months);
      // Lọc lại để chỉ lấy sản phẩm có thời gian tồn kho >= số tháng đã cài đặt
      const agingData = agingResponse.data.filter(product =>
        product.monthsInStock >= (settingsObj.inventory_aging_months || 6)
      );

      // Lấy danh sách sản phẩm sắp hết hàng
      const lowStockResponse = await ProductService.getLowStockProducts(settingsObj.low_stock_threshold);
      const lowStockData = lowStockResponse.data;

      // Lấy danh sách sản phẩm đã hết hàng
      const outOfStockResponse = await ProductService.getOutOfStockProducts();
      const outOfStockData = outOfStockResponse.data;

      // Tính tổng doanh thu từ các hóa đơn đã thanh toán
      const revenue = invoices
        .filter(invoice => invoice.status === 'paid')
        .reduce((sum, invoice) => sum + (invoice.totalAmount || 0), 0);

      // Lấy 5 hóa đơn gần đây nhất
      const recent = [...invoices]
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
        .slice(0, 5);

      setStats({
        totalProducts: products.length,
        totalCustomers: customers.length,
        totalInvoices: invoices.length,
        totalRevenue: revenue
      });

      setRecentInvoices(recent);
      setLowStockProducts(lowStockData.slice(0, 5));
      setOutOfStockProducts(outOfStockData.slice(0, 5));
      setAgingProducts(agingData.slice(0, 5));
    } catch (error) {
      console.error('Lỗi khi tải dữ liệu tổng quan:', error);
    } finally {
      setLoading(false);
    }
  };

  const invoiceColumns = [
    {
      title: 'Mã hoá đơn',
      dataIndex: 'invoiceCode',
      key: 'invoiceCode',
      render: (text, record) => {
        const code = record.invoiceCode || record.code;
        return <Link to={`/invoices/view/${record.id}`}>{code}</Link>;
      },
    },
    {
      title: 'Khách hàng',
      key: 'customer',
      render: (_, record) => {
        if (record.Customer) {
          return record.Customer.name || 'N/A';
        }
        if (record.customer) {
          return record.customer.name || 'N/A';
        }
        return 'N/A';
      },
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => new Date(date).toLocaleDateString('vi-VN'),
    },
    {
      title: 'Tổng tiền',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      render: (amount) => formatCurrencyWithSymbol(amount),
    },
  ];

  const productColumns = [
    {
      title: 'Mã SP',
      dataIndex: 'productCode',
      key: 'productCode',
      render: (text, record) => text || record.code,
    },
    {
      title: 'Tên sản phẩm',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => <Link to={`/products/edit/${record.id}`}>{text}</Link>,
    },
    {
      title: 'Tồn kho',
      dataIndex: 'stock',
      key: 'stock',
      render: (stock) => (
        <span style={{ color: stock === 0 ? 'red' : 'orange', fontWeight: 'bold' }}>
          {stock}
        </span>
      ),
    },
  ];

  return (
    <div>
      <Title level={2}>Tổng quan hệ thống quản lý kho</Title>

      <Row gutter={16}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Tổng sản phẩm"
              value={stats.totalProducts}
              prefix={<ShoppingOutlined />}
              loading={loading}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Tổng khách hàng"
              value={stats.totalCustomers}
              prefix={<TeamOutlined />}
              loading={loading}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Tổng hoá đơn"
              value={stats.totalInvoices}
              prefix={<FileTextOutlined />}
              loading={loading}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Doanh thu"
              value={formatCurrencyWithSymbol(stats.totalRevenue)}
              prefix={<DollarOutlined />}
              loading={loading}
            />
          </Card>
        </Col>
      </Row>

      <Divider />

      {agingProducts.length > 0 && (
        <Alert
          message={
            <span>
              <ClockCircleOutlined /> Có {agingProducts.length} sản phẩm tồn kho lâu hơn hoặc bằng {settings.inventory_aging_months} tháng
            </span>
          }
          description={
            <span>
              Hãy kiểm tra và xử lý các sản phẩm tồn kho lâu để tối ưu hóa vốn lưu động.
              <Link to="/inventory-notification" style={{ marginLeft: 8 }}>
                Xem chi tiết
              </Link>
            </span>
          }
          type="warning"
          showIcon={false}
          style={{ marginBottom: 16 }}
        />
      )}

      {outOfStockProducts.length > 0 && (
        <Alert
          message={
            <span>
              <WarningOutlined /> Có {outOfStockProducts.length} sản phẩm đã hết hàng
            </span>
          }
          description={
            <span>
              Vui lòng kiểm tra và nhập thêm hàng cho các sản phẩm đã hết.
              <Link to="/inventory-notification?tab=4" style={{ marginLeft: 8 }}>
                Xem chi tiết
              </Link>
            </span>
          }
          type="error"
          showIcon={false}
          style={{ marginBottom: 16 }}
        />
      )}

      <Row gutter={16}>
        <Col span={12}>
          <Card title="Hoá đơn gần đây" variant="borderless">
            <Table
              columns={invoiceColumns}
              dataSource={recentInvoices}
              rowKey="id"
              pagination={false}
              loading={loading}
              size="small"
            />
          </Card>
        </Col>
        <Col span={12}>
          <Card
            title={
              <span>
                <WarningOutlined style={{ color: '#faad14' }} /> Sản phẩm sắp hết hàng
              </span>
            }
            variant="borderless"
            extra={
              <Link to="/inventory-notification">
                Xem tất cả
              </Link>
            }
          >
            <Table
              columns={productColumns}
              dataSource={lowStockProducts}
              rowKey="id"
              pagination={false}
              loading={loading}
              size="small"
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
