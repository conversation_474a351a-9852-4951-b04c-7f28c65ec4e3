import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Card, Switch, Select, Typography, Divider } from 'antd';
import { useNavigate, useParams } from 'react-router-dom';
import { Toaster, toast } from 'react-hot-toast';
import ReceiptTypeService from '../../services/receiptType.service';

const { Title } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const ReceiptTypeForm = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const navigate = useNavigate();
  const { id } = useParams();

  useEffect(() => {
    if (id) {
      setIsEdit(true);
      fetchReceiptTypeData();
    }
  }, [id]);

  const fetchReceiptTypeData = async () => {
    setLoading(true);
    try {
      const response = await ReceiptTypeService.getReceiptTypeById(id);
      const receiptTypeData = response.data;
      form.setFieldsValue({
        name: receiptTypeData.name,
        type: receiptTypeData.type,
        description: receiptTypeData.description,
        isActive: receiptTypeData.isActive,
      });
    } catch (error) {
      toast.error('Không thể tải thông tin loại thu chi: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const onFinish = async (values) => {
    setLoading(true);
    try {
      if (isEdit) {
        await ReceiptTypeService.updateReceiptType(id, values);
        toast.success('Cập nhật loại thu chi thành công');
      } else {
        await ReceiptTypeService.createReceiptType(values);
        toast.success('Thêm loại thu chi thành công');
      }
      navigate('/receipt-types');
    } catch (error) {
      toast.error('Lỗi: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <Toaster position="top-right" />
      <Title level={2}>{isEdit ? 'Cập nhật loại thu chi' : 'Thêm loại thu chi mới'}</Title>
      <Divider />

      <Card>
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          initialValues={{ type: 'other', isActive: true }}
        >
          <Form.Item
            name="name"
            label="Tên loại thu chi"
            rules={[{ required: true, message: 'Vui lòng nhập tên loại thu chi' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="type"
            label="Loại"
            rules={[{ required: true, message: 'Vui lòng chọn loại' }]}
          >
            <Select>
              <Option value="income">Thu</Option>
              <Option value="expense">Chi</Option>
              <Option value="transfer">Chuyển/Rút</Option>
              <Option value="other">Khác</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="description"
            label="Mô tả"
          >
            <TextArea rows={4} />
          </Form.Item>

          <Form.Item
            name="isActive"
            label="Trạng thái"
            valuePropName="checked"
          >
            <Switch checkedChildren="Hoạt động" unCheckedChildren="Không hoạt động" defaultChecked />
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading}>
              {isEdit ? 'Cập nhật' : 'Thêm mới'}
            </Button>
            <Button style={{ marginLeft: 8 }} onClick={() => navigate('/receipt-types')}>
              Hủy
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default ReceiptTypeForm;
