import React, { useState, useEffect, useRef } from 'react';
import { Form, Input, Button, Card, Select, InputNumber, DatePicker, Typography, Divider, Table, Space, Modal, Switch } from 'antd';
import { Toaster, toast } from 'react-hot-toast';
import { PlusOutlined, MinusCircleOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';
import moment from 'moment';
import ImportService from '../../services/import.service';
import SupplierService from '../../services/supplier.service';
import ProductService from '../../services/product.service';
import BranchService from '../../services/branch.service';
import ProductCategoryService from '../../services/productCategory.service';
import BrandService from '../../services/brand.service';
import { formatCurrencyWithSymbol } from '../../utils/format';
import { useAuth } from '../../context/AuthContext';

const { Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const ImportForm = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [suppliers, setSuppliers] = useState([]);
  const [products, setProducts] = useState([]);
  const [branches, setBranches] = useState([]);

  const [categories, setCategories] = useState({ roots: [], all: [], childrenMap: {} });
  const [brands, setBrands] = useState([]);
  const [selectedItems, setSelectedItems] = useState([]);
  const [totalAmount, setTotalAmount] = useState(0);
  const [productModalVisible, setProductModalVisible] = useState(false);
  const [supplierModalVisible, setSupplierModalVisible] = useState(false);
  const [productForm] = Form.useForm();
  const [supplierForm] = Form.useForm();
  const supplierNameRef = useRef(null);
  const supplierCodeRef = useRef(null);
  const [newSupplierName, setNewSupplierName] = useState('');
  const [newSupplierCode, setNewSupplierCode] = useState('');
  const [addingSupplier, setAddingSupplier] = useState(false);
  const navigate = useNavigate();
  const { id } = useParams();
  const { currentUser } = useAuth();

  useEffect(() => {
    fetchSuppliers();
    fetchProducts();
    fetchBranches();
    fetchCategories();
    fetchBrands();

    if (id) {
      setIsEdit(true);
      fetchImportData();
    }
  }, [id]);

  // Tự động set userId từ currentUser khi component mount hoặc currentUser thay đổi
  useEffect(() => {
    if (currentUser && !isEdit) {
      // Lấy userId từ currentUser, xử lý cả trường hợp nested và không nested
      const userId = currentUser.user ? currentUser.user.id : currentUser.id;
      if (userId) {
        form.setFieldsValue({ userId: userId });
      }
    }
  }, [currentUser, isEdit, form]);

  const fetchCategories = async () => {
    try {
      const response = await ProductCategoryService.getAllCategories();
      const categoriesData = response.data;

      // Chỉ lấy các nhóm cha (không có parentId)
      const rootCategories = categoriesData.filter(cat => !cat.parentId)
        .sort((a, b) => a.name.localeCompare(b.name));

      // Tạo map để tra cứu nhanh các nhóm con
      const childrenMap = {};
      rootCategories.forEach(parent => {
        childrenMap[parent.id] = categoriesData
          .filter(cat => cat.parentId === parent.id)
          .sort((a, b) => a.name.localeCompare(b.name));
      });

      // Lưu toàn bộ dữ liệu để sử dụng sau này
      setCategories({
        roots: rootCategories,
        all: categoriesData,
        childrenMap: childrenMap
      });
    } catch (error) {
      toast.error('Không thể tải danh sách nhóm hàng: ' + error.message);
    }
  };

  const fetchBrands = async () => {
    try {
      const response = await BrandService.getAllBrands();
      setBrands(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách thương hiệu: ' + error.message);
    }
  };

  useEffect(() => {
    calculateTotal();
  }, [selectedItems]);

  const fetchSuppliers = async () => {
    try {
      const response = await SupplierService.getAllSuppliers();
      setSuppliers(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách nhà cung cấp: ' + error.message);
    }
  };

  const handleAddSupplier = async () => {
    if (!newSupplierCode.trim()) {
      toast.error('Vui lòng nhập mã nhà cung cấp');
      return;
    }

    if (!newSupplierName.trim()) {
      toast.error('Vui lòng nhập tên nhà cung cấp');
      return;
    }

    setAddingSupplier(true);
    try {
      const supplierData = {
        supplierCode: newSupplierCode.trim(),
        name: newSupplierName.trim(),
        isActive: true
      };

      const response = await SupplierService.createSupplier(supplierData);
      toast.success('Thêm nhà cung cấp thành công');
      
      // Cập nhật danh sách nhà cung cấp
      await fetchSuppliers();
      
      // Cập nhật giá trị nhà cung cấp trong form
      form.setFieldsValue({ supplierId: response.data.id });
      
      // Reset form
      setNewSupplierName('');
      setNewSupplierCode('');
      setSupplierModalVisible(false);
    } catch (error) {
      toast.error('Không thể thêm nhà cung cấp: ' + error.message);
    } finally {
      setAddingSupplier(false);
    }
  };

  const showAddSupplierModal = () => {
    setSupplierModalVisible(true);
  };

  const handleAddSupplierModalCancel = () => {
    setSupplierModalVisible(false);
    setNewSupplierName('');
    setNewSupplierCode('');
  };

  const fetchProducts = async () => {
    try {
      const response = await ProductService.getAllProducts();
      setProducts(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách sản phẩm: ' + error.message);
    }
  };

  const fetchBranches = async () => {
    try {
      const response = await BranchService.getAllBranches();
      setBranches(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách chi nhánh: ' + error.message);
    }
  };



  const fetchImportData = async () => {
    setLoading(true);
    try {
      const response = await ImportService.getImportById(id);
      const importData = response.data;

      // Kiểm tra xem items có tồn tại không
      let items = [];
      if (importData.items && importData.items.length > 0) {
        items = importData.items;
      } else if (importData.ImportItems && importData.ImportItems.length > 0) {
        // Nếu không có items, kiểm tra xem có ImportItems không
        items = importData.ImportItems.map(item => ({
          id: item.id,
          productId: item.productId,
          quantity: item.quantity,
          price: item.price,
          amount: item.amount,
          // Thêm thông tin sản phẩm nếu có
          product: item.Product || item.product
        }));
      }

      form.setFieldsValue({
        importCode: importData.importCode,
        supplierId: importData.supplierId,
        importDate: importData.importDate ? moment(importData.importDate) : moment(),
        status: importData.status,
        paidAmount: importData.paidAmount || 0,
        branchId: importData.branchId,
        userId: importData.userId,
        note: importData.note,
      });

      setSelectedItems(items);

      // Nếu phiếu nhập đã hoàn thành, kiểm tra tồn kho hiện tại của các sản phẩm
      if (importData.status === 'completed') {

        // Lấy danh sách ID sản phẩm trong phiếu nhập
        const productIds = items
          .filter(item => item.productId)
          .map(item => item.productId);

        if (productIds.length > 0) {
          toast.success('Đang kiểm tra tồn kho ban đầu của sản phẩm trong phiếu nhập...');
          await verifyAllProductsStock(productIds);
        }
      }
    } catch (error) {
      toast.error('Không thể tải thông tin phiếu nhập: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleAddItem = () => {
    setSelectedItems([...selectedItems, { productId: null, quantity: 1, price: 0, amount: 0 }]);
  };

  const showAddProductModal = () => {
    productForm.resetFields();
    setProductModalVisible(true);
  };

  const handleAddProductModalCancel = () => {
    setProductModalVisible(false);
  };

  const handleAddProductModalOk = async () => {
    try {
      const values = await productForm.validateFields();

      // Chuyển đổi tên trường để phù hợp với backend
      const productData = {
        ...values,
        productCode: values.code,
        sellingPrice: values.price,
      };
      delete productData.code;
      delete productData.price;

      setLoading(true);

      // Gọi API để tạo sản phẩm mới
      const response = await ProductService.createProduct(productData);
      const newProduct = response.data;

      toast.success('Thêm sản phẩm mới thành công');

      // Cập nhật danh sách sản phẩm
      setProducts([...products, newProduct]);

      // Thêm sản phẩm mới vào danh sách sản phẩm đã chọn
      const newItem = {
        productId: newProduct.id,
        quantity: 1,
        price: newProduct.costPrice || 0,
        amount: newProduct.costPrice || 0
      };

      setSelectedItems([...selectedItems, newItem]);

      setProductModalVisible(false);
    } catch (error) {
      if (error.errorFields) {
        toast.error('Vui lòng điền đầy đủ thông tin sản phẩm');
      } else {
        toast.error('Lỗi khi thêm sản phẩm: ' + error.message);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveItem = (index) => {
    const newItems = [...selectedItems];
    newItems.splice(index, 1);
    setSelectedItems(newItems);
  };

  const handleProductChange = (value, index) => {
    const product = products.find(p => p.id === value);
    const newItems = [...selectedItems];
    const price = Number(product.costPrice) || 0;
    const quantity = Number(newItems[index].quantity) || 1;
    newItems[index] = {
      ...newItems[index],
      productId: value,
      price: price,
      amount: price * quantity
    };
    setSelectedItems(newItems);
  };

  const handleQuantityChange = (value, index) => {
    const newItems = [...selectedItems];
    const quantity = Number(value) || 0;
    const price = Number(newItems[index].price) || 0;
    newItems[index] = {
      ...newItems[index],
      quantity: quantity,
      amount: price * quantity
    };
    setSelectedItems(newItems);
  };

  const handlePriceChange = (value, index) => {
    const newItems = [...selectedItems];
    const price = Number(value) || 0;
    const quantity = Number(newItems[index].quantity) || 1;
    newItems[index] = {
      ...newItems[index],
      price: price,
      amount: price * quantity
    };
    setSelectedItems(newItems);
  };

  const calculateTotal = () => {
    const total = selectedItems.reduce((sum, item) => sum + (Number(item.amount) || 0), 0);
    setTotalAmount(total);
    form.setFieldsValue({ totalAmount: total });
  };





  // Hàm so sánh danh sách sản phẩm cũ và mới để xác định sự thay đổi
  const compareItems = (oldItems, newItems, oldStatus, newStatus, backendUpdated = false) => {
    // Đảm bảo oldItems và newItems là mảng
    oldItems = Array.isArray(oldItems) ? oldItems : [];
    newItems = Array.isArray(newItems) ? newItems : [];

    // Trường hợp đặc biệt: khi chuyển từ pending sang completed
    if (oldStatus === 'pending' && newStatus === 'completed') {
      // Nếu backend đã cập nhật tồn kho, trả về mảng rỗng để tránh cập nhật lại
      if (backendUpdated) {
        return [];
      }

      // Khi chuyển từ pending sang completed, thêm tất cả sản phẩm vào danh sách cập nhật
      const allItems = newItems.map(item => ({
        productId: item.productId,
        quantity: item.quantity,
        isNew: false,
        isRemoved: false
      }));

      return allItems;
    }

    // Tạo map để lưu thông tin sản phẩm cũ theo productId
    const oldItemsMap = {};
    oldItems.forEach(item => {
      if (item && item.productId) {
        oldItemsMap[item.productId] = item;
      }
    });

    // Tạo map để lưu thông tin sản phẩm mới theo productId
    const newItemsMap = {};
    newItems.forEach(item => {
      if (item && item.productId) {
        newItemsMap[item.productId] = item;
      }
    });

    // Mảng lưu các sản phẩm cần cập nhật tồn kho
    const itemsToUpdate = [];

    // Kiểm tra các sản phẩm mới hoặc số lượng thay đổi
    newItems.forEach(newItem => {
      // Chỉ xử lý các sản phẩm có productId
      if (!newItem || !newItem.productId) return;

      const oldItem = oldItemsMap[newItem.productId];

      // Nếu sản phẩm không tồn tại trong danh sách cũ hoặc số lượng thay đổi
      if (!oldItem) {
        // Sản phẩm mới thêm vào
        itemsToUpdate.push({
          productId: newItem.productId,
          quantity: newItem.quantity,
          isNew: true,
          isRemoved: false
        });
      } else if (oldItem.quantity !== newItem.quantity) {
        // Sản phẩm có số lượng thay đổi
        const diffQuantity = newItem.quantity - oldItem.quantity;
        itemsToUpdate.push({
          productId: newItem.productId,
          quantity: diffQuantity, // Chỉ lấy phần chênh lệch
          isNew: false,
          isRemoved: false
        });
      }
    });

    // Kiểm tra các sản phẩm đã bị xóa khỏi phiếu nhập
    oldItems.forEach(oldItem => {
      // Chỉ xử lý các sản phẩm có productId
      if (!oldItem || !oldItem.productId) return;

      // Nếu sản phẩm không còn trong danh sách mới
      if (!newItemsMap[oldItem.productId]) {
        itemsToUpdate.push({
          productId: oldItem.productId,
          quantity: oldItem.quantity,
          isNew: false,
          isRemoved: true
        });
      }
    });

    return itemsToUpdate;
  };

  // Hàm phát hiện nếu backend đã tự động cập nhật tồn kho
  const detectBackendStockUpdate = async (oldStock, newStock, quantity, operation) => {
    // Tính toán tồn kho dự kiến
    let expectedStock = oldStock;

    if (operation === 'add') {
      // Nếu thêm sản phẩm, tồn kho phải tăng
      expectedStock = oldStock + quantity;
    } else if (operation === 'remove') {
      // Nếu xóa sản phẩm, tồn kho phải giảm
      expectedStock = Math.max(0, oldStock - quantity);
    }

    // Kiểm tra nếu tồn kho thực tế khác với tồn kho dự kiến
    if (newStock !== expectedStock) {
      return {
        backendUpdated: true,
        difference: newStock - expectedStock
      };
    }

    return { backendUpdated: false };
  };

  // Hàm đảm bảo đồng bộ khi kiểm tra tồn kho
  const checkStockSynchronously = async (productId, operation = "") => {
    try {
      // Gọi API để lấy thông tin sản phẩm hiện tại - thêm timestamp để tránh cache
      const response = await ProductService.getProductById(productId, { timestamp: new Date().getTime() });
      const product = response.data;
      const stock = product.stock || 0;

      // Hiển thị thông báo cho người dùng nếu có operation
      if (operation) {
        toast.success(`Tồn kho của "${product.name}" là: ${stock}`);
      }

      return {
        productId,
        name: product.name,
        stock
      };
    } catch (error) {
      return { productId, stock: null, error };
    }
  };

  // Hàm kiểm tra tồn kho của nhiều sản phẩm - tối ưu hóa với Promise.all
  const verifyAllProductsStock = async (productIds) => {
    return Promise.all(
      productIds.map(productId => checkStockSynchronously(productId))
    );
  };

  const onFinish = async (values) => {
    if (selectedItems.length === 0) {
      toast.error('Vui lòng thêm ít nhất một sản phẩm vào phiếu nhập');
      return;
    }

    // Kiểm tra xem các sản phẩm đã được chọn đầy đủ chưa
    const invalidItems = selectedItems.filter(item => !item.productId);
    if (invalidItems.length > 0) {
      toast.error('Vui lòng chọn sản phẩm cho tất cả các dòng');
      return;
    }

    const importData = {
      ...values,
      importDate: values.importDate.format('YYYY-MM-DD HH:mm:ss'),
      items: selectedItems.map(item => ({
        productId: item.productId,
        quantity: Number(item.quantity) || 0,
        price: Number(item.price) || 0,
        amount: Number(item.amount) || 0
      })),
      totalAmount: Number(totalAmount) || 0,
      // Đảm bảo các trường bắt buộc có giá trị
      paidAmount: Number(values.paidAmount) || 0,
      status: values.status || 'pending'
    };



    // Khi tạo mới, không gửi importCode để backend tự động tạo
    if (!isEdit) {
      // Không cần gửi importCode, backend sẽ tự động tạo
      delete importData.importCode;
    }

    setLoading(true);
    try {
      if (isEdit) {
        // Lấy thông tin cũ của phiếu nhập để so sánh
        const oldImportResponse = await ImportService.getImportById(id);
        const oldImportData = oldImportResponse.data;
        const oldStatus = oldImportData.status;
        const newStatus = values.status;

        // Lấy danh sách sản phẩm cũ
        let oldItems = [];
        if (oldImportData.items && oldImportData.items.length > 0) {
          oldItems = oldImportData.items;
        } else if (oldImportData.ImportItems && oldImportData.ImportItems.length > 0) {
          oldItems = oldImportData.ImportItems.map(item => ({
            productId: item.productId,
            quantity: item.quantity,
            price: item.price,
            amount: item.amount
          }));
        }

        // Trường hợp đặc biệt: Chuyển từ completed sang pending/cancelled
        if (oldStatus === 'completed' && newStatus !== 'completed') {


          // Lưu trữ thông tin phiếu nhập cũ trước khi cập nhật
          const oldImportItems = [...oldItems];

          // Cập nhật phiếu nhập trước
          await ImportService.updateImport(id, importData);



          // Xử lý tồn kho cho từng sản phẩm

          for (const item of oldImportItems) {
            try {
              if (!item.productId) continue;

              // Lấy thông tin sản phẩm
              const productId = item.productId;
              const quantity = item.quantity;

              // Kiểm tra tồn kho hiện tại
              const productResponse = await ProductService.getProductById(productId);
              const product = productResponse.data;
              const currentStock = product.stock || 0;

              // Tính toán tồn kho mới - khi hủy phiếu nhập đã hoàn thành, giảm tồn kho
              // Đảm bảo tồn kho không âm
              const newStock = Math.max(0, currentStock - quantity);

              // Cập nhật tồn kho
              await ProductService.updateProduct(productId, {
                ...product,
                stock: newStock
              });

              // Kiểm tra lại tồn kho sau khi cập nhật
              const updatedResponse = await ProductService.getProductById(productId);
              const updatedStock = updatedResponse.data.stock;

              // Kiểm tra tồn kho sau cập nhật nhưng không hiển thị thông báo
              if (updatedStock !== newStock) {
                console.warn(`Tồn kho không khớp sau khi cập nhật sản phẩm ${product.name}`);
              }
            } catch (error) {
              console.error(`Lỗi cập nhật tồn kho sản phẩm: ${error.message}`);
            }
          }

          toast.success('Cập nhật phiếu nhập thành công');
        }
        // Trường hợp đặc biệt: Chuyển từ pending/cancelled sang completed
        else if (oldStatus !== 'completed' && newStatus === 'completed') {
          // Cập nhật phiếu nhập
          await ImportService.updateImport(id, importData);

          // Chỉ hiển thị một thông báo
          toast.success('Cập nhật phiếu nhập thành công');
        }
        // Trường hợp thông thường
        else {
          // Chỉ cập nhật phiếu nhập, không làm gì với tồn kho
          await ImportService.updateImport(id, importData);
        toast.success('Cập nhật phiếu nhập thành công');
        }
      } else {
        // Tạo phiếu nhập mới

        // Kiểm tra tồn kho trước khi tạo phiếu nhập mới
        if (values.status === 'completed') {


          // Không cần kiểm tra tồn kho trước khi tạo phiếu nhập

          // Tạo phiếu nhập mới
          await ImportService.createImport(importData);



          // Chỉ hiển thị một thông báo
          // Không cần kiểm tra tồn kho sau khi tạo phiếu nhập, backend sẽ tự xử lý
        } else {
          // Nếu không phải trạng thái completed, đơn giản là tạo phiếu nhập
          await ImportService.createImport(importData);
        }
        
        toast.success('Tạo phiếu nhập thành công');
      }
      navigate('/imports');
    } catch (error) {
      toast.error('Lỗi: ' + (error.response?.data?.message || error.message));
    } finally {
      setLoading(false);
    }
  };

  const columns = [
    {
      title: 'Sản phẩm',
      dataIndex: 'productId',
      key: 'productId',
      render: (value, _, index) => (
        <Select
          style={{ width: '100%' }}
          placeholder="Chọn sản phẩm"
          value={value}
          onChange={(val) => handleProductChange(val, index)}
        >
          {products.map(product => (
            <Option key={product.id} value={product.id}>{product.name}</Option>
          ))}
        </Select>
      ),
    },
    {
      title: 'Số lượng',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 120,
      render: (value, _, index) => (
        <InputNumber
          min={1}
          value={value}
          onChange={(val) => handleQuantityChange(val, index)}
          style={{ width: '100%' }}
        />
      ),
    },
    {
      title: 'Đơn giá',
      dataIndex: 'price',
      key: 'price',
      width: 150,
      render: (value, _, index) => (
        <InputNumber
          value={value}
          onChange={(val) => handlePriceChange(val, index)}
          style={{ width: '100%' }}
          formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, '.')}
          parser={value => value.replace(/\$\s?|(\.+)/g, '')}
          min={0}
        />
      ),
    },
    {
      title: 'Thành tiền',
      dataIndex: 'amount',
      key: 'amount',
      width: 150,
      render: (amount) => formatCurrencyWithSymbol(amount),
    },
    {
      title: '',
      key: 'action',
      width: 80,
      render: (_, __, index) => (
        <Button
          type="text"
          danger
          icon={<MinusCircleOutlined />}
          onClick={() => handleRemoveItem(index)}
        />
      ),
    },
  ];

  return (
    <div>
      <Toaster position="top-right" />
      <Title level={2}>{isEdit ? 'Cập nhật phiếu nhập' : 'Tạo phiếu nhập mới'}</Title>
      <Divider />

      <Card>
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          initialValues={{
            importDate: moment(),
            status: 'pending',
          }}
        >
          <div style={{ display: 'flex', gap: '16px' }}>
            {isEdit ? (
              <Form.Item
                name="importCode"
                label="Mã phiếu nhập"
                style={{ flex: 1 }}
              >
                <Input disabled />
              </Form.Item>
            ) : (
              <Form.Item
                label="Mã phiếu nhập"
                style={{ flex: 1 }}
              >
                <Input disabled placeholder="Mã phiếu nhập sẽ được tạo tự động (PN + số thứ tự)" />
              </Form.Item>
            )}

            <Form.Item
              name="supplierId"
              label="Nhà cung cấp"
              rules={[{ required: true, message: 'Vui lòng chọn nhà cung cấp' }]}
              style={{ flex: 1 }}
            >
              <Select 
                placeholder="Chọn nhà cung cấp"
                dropdownRender={menu => (
                  <div>
                    {menu}
                    <Divider style={{ margin: '8px 0' }} />
                    <Space style={{ padding: '0 8px 4px' }}>
                      <Button 
                        type="text" 
                        icon={<PlusOutlined />} 
                        onClick={showAddSupplierModal}
                      >
                        Thêm nhà cung cấp mới
                      </Button>
                    </Space>
                  </div>
                )}
              >
                {suppliers.map(supplier => (
                  <Option key={supplier.id} value={supplier.id}>{supplier.name}</Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="importDate"
              label="Ngày nhập"
              rules={[{ required: true, message: 'Vui lòng chọn ngày nhập' }]}
              style={{ flex: 1 }}
            >
              <DatePicker
                showTime
                format="DD/MM/YYYY HH:mm:ss"
                style={{ width: '100%' }}
              />
            </Form.Item>
          </div>

          <div style={{ display: 'flex', gap: '16px' }}>
            <Form.Item
              name="status"
              label="Trạng thái"
              rules={[{ required: true, message: 'Vui lòng chọn trạng thái' }]}
              style={{ flex: 1 }}
            >
              <Select>
                <Option value="pending">Chờ xử lý</Option>
                <Option value="completed">Đã hoàn thành</Option>
                <Option value="cancelled">Đã hủy</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="branchId"
              label="Chi nhánh"
              style={{ flex: 1 }}
            >
              <Select placeholder="Chọn chi nhánh" allowClear>
                {branches.map(branch => (
                  <Option key={branch.id} value={branch.id}>{branch.name}</Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="userId"
              label="Người tạo"
              style={{ flex: 1 }}
            >
              <Input
                disabled
                value={currentUser ? (currentUser.user ? currentUser.user.fullName || currentUser.user.username : currentUser.fullName || currentUser.username) : 'Đang tải...'}
                placeholder="Người tạo"
              />
            </Form.Item>
          </div>

          <Divider orientation="left">Chi tiết nhập hàng</Divider>

          <div style={{ display: 'flex', gap: '8px', marginBottom: 16 }}>
            <Button
              type="dashed"
              onClick={handleAddItem}
              icon={<PlusOutlined />}
            >
              Chọn sản phẩm
            </Button>
            <Button
              type="primary"
              onClick={showAddProductModal}
              icon={<PlusCircleOutlined />}
            >
              Thêm sản phẩm mới
            </Button>
          </div>

          <Table
            columns={columns}
            dataSource={selectedItems}
            rowKey={(record) => record.id || record.key || record._id || Math.random().toString(36).substr(2, 9)}
            pagination={false}
            bordered
          />

          <div style={{ textAlign: 'right', marginTop: 16 }}>
            <Title level={4}>Tổng tiền: {formatCurrencyWithSymbol(totalAmount)}</Title>
          </div>

          <Form.Item name="totalAmount" hidden>
            <InputNumber />
          </Form.Item>

          <Form.Item
            name="paidAmount"
            label="Số tiền đã thanh toán"
            style={{ marginTop: 16 }}
          >
            <InputNumber
              style={{ width: '100%' }}
              formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, '.')}
              parser={value => value.replace(/\$\s?|(\.+)/g, '')}
              min={0}
            />
          </Form.Item>

          <Form.Item
            name="note"
            label="Ghi chú"
          >
            <TextArea rows={4} />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={loading}>
                {isEdit ? 'Cập nhật' : 'Tạo phiếu nhập'}
              </Button>
              <Button onClick={() => navigate('/imports')}>
                Hủy
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      {/* Modal thêm sản phẩm mới */}
      <Modal
        title="Thêm sản phẩm mới"
        open={productModalVisible}
        onOk={handleAddProductModalOk}
        onCancel={handleAddProductModalCancel}
        width={800}
        confirmLoading={loading}
      >
        <Form
          form={productForm}
          layout="vertical"
          initialValues={{ stock: 0, price: 0, isActive: true }}
        >
          <div style={{ display: 'flex', gap: '16px' }}>
            <Form.Item
              name="code"
              label="Mã sản phẩm"
              rules={[{ required: true, message: 'Vui lòng nhập mã sản phẩm' }]}
              style={{ flex: 1 }}
            >
              <Input />
            </Form.Item>

            <Form.Item
              name="barcode"
              label="Mã vạch"
              style={{ flex: 1 }}
            >
              <Input />
            </Form.Item>
          </div>

          <Form.Item
            name="name"
            label="Tên sản phẩm"
            rules={[{ required: true, message: 'Vui lòng nhập tên sản phẩm' }]}
          >
            <Input />
          </Form.Item>

          <div style={{ display: 'flex', gap: '16px' }}>
            <Form.Item
              name="categoryId"
              label="Nhóm hàng"
              rules={[{ required: true, message: 'Vui lòng chọn nhóm hàng' }]}
              style={{ flex: 1 }}
            >
              <Select
                placeholder="Chọn nhóm hàng"
                dropdownRender={menu => {
                  return categories.roots ? menu : <div style={{ padding: 8 }}>Đang tải...</div>;
                }}
              >
                {categories.roots && categories.roots.map(category => (
                  <Select.OptGroup key={category.id} label={<strong>{category.name}</strong>}>
                    {/* Thêm nhóm cha như một option */}
                    <Option key={category.id} value={category.id}>
                      <strong>{category.name}</strong>
                    </Option>

                    {/* Thêm các nhóm con */}
                    {categories.childrenMap[category.id] && categories.childrenMap[category.id].map(child => (
                      <Option key={child.id} value={child.id}>
                        <span style={{ paddingLeft: '20px' }}>↳ {child.name}</span>
                      </Option>
                    ))}
                  </Select.OptGroup>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="brandId"
              label="Thương hiệu"
              rules={[{ required: true, message: 'Vui lòng chọn thương hiệu' }]}
              style={{ flex: 1 }}
            >
              <Select placeholder="Chọn thương hiệu">
                {brands.map(brand => (
                  <Option key={brand.id} value={brand.id}>{brand.name}</Option>
                ))}
              </Select>
            </Form.Item>
          </div>

          <div style={{ display: 'flex', gap: '16px' }}>
            <Form.Item
              name="costPrice"
              label="Giá vốn"
              rules={[{ required: true, message: 'Vui lòng nhập giá vốn' }]}
              style={{ flex: 1 }}
            >
              <InputNumber
                style={{ width: '100%' }}
                formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, '.')}
                parser={value => value.replace(/\$\s?|(\.+)/g, '')}
                min={0}
              />
            </Form.Item>

            <Form.Item
              name="price"
              label="Giá bán"
              rules={[{ required: true, message: 'Vui lòng nhập giá bán' }]}
              style={{ flex: 1 }}
            >
              <InputNumber
                style={{ width: '100%' }}
                formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, '.')}
                parser={value => value.replace(/\$\s?|(\.+)/g, '')}
                min={0}
              />
            </Form.Item>

            <Form.Item
              name="stock"
              label="Tồn kho"
              rules={[{ required: true, message: 'Vui lòng nhập số lượng tồn kho' }]}
              style={{ flex: 1 }}
            >
              <InputNumber style={{ width: '100%' }} min={0} />
            </Form.Item>
          </div>

          <Form.Item
            name="description"
            label="Mô tả"
          >
            <TextArea rows={4} />
          </Form.Item>

          <Form.Item
            name="isActive"
            label="Trạng thái"
            valuePropName="checked"
          >
            <Switch checkedChildren="Hoạt động" unCheckedChildren="Không hoạt động" defaultChecked />
          </Form.Item>
        </Form>
      </Modal>

      {/* Modal thêm nhà cung cấp mới */}
      <Modal
        title="Thêm nhà cung cấp mới"
        open={supplierModalVisible}
        onOk={handleAddSupplier}
        onCancel={handleAddSupplierModalCancel}
        confirmLoading={addingSupplier}
      >
        <Form form={supplierForm} layout="vertical">
          <Form.Item
            name="supplierCode"
            label="Mã nhà cung cấp"
            rules={[{ required: true, message: 'Vui lòng nhập mã nhà cung cấp' }]}
          >
            <Input 
              placeholder="Nhập mã nhà cung cấp" 
              value={newSupplierCode}
              onChange={(e) => setNewSupplierCode(e.target.value)}
            />
          </Form.Item>
          <Form.Item
            name="name"
            label="Tên nhà cung cấp"
            rules={[{ required: true, message: 'Vui lòng nhập tên nhà cung cấp' }]}
          >
            <Input 
              placeholder="Nhập tên nhà cung cấp" 
              value={newSupplierName}
              onChange={(e) => setNewSupplierName(e.target.value)}
            />
          </Form.Item>
          <Form.Item
            name="phone"
            label="Số điện thoại"
          >
            <Input placeholder="Nhập số điện thoại" />
          </Form.Item>
          <Form.Item
            name="address"
            label="Địa chỉ"
          >
            <Input placeholder="Nhập địa chỉ" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ImportForm;

