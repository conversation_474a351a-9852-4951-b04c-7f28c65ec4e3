import React, { useEffect, useState } from 'react';
import { Card, Descriptions, Avatar, Typography, Divider, Spin, Alert, Button } from 'antd';
import { UserOutlined, ReloadOutlined } from '@ant-design/icons';
import { useAuth } from '../../context/AuthContext';
import AuthService from '../../services/auth.service';

const { Title } = Typography;

const Profile = () => {
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const refreshUserData = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await AuthService.getCurrentUser();

      if (response.data) {
        if (response.data.user) {

          window.location.reload();
        } else {

          window.location.reload();
        }
      } else {
        setError('Không nhận được dữ liệu người dùng từ server.');
      }
    } catch (error) {
      console.error('Lỗi khi làm mới dữ liệu người dùng:', error);
      setError('<PERSON>hông thể lấy thông tin người dùng. Vui lòng đăng nhập lại.');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <p style={{ marginTop: '20px' }}>Đang tải thông tin người dùng...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ padding: '20px' }}>
        <Alert
          message="Lỗi"
          description={error}
          type="error"
          showIcon
          action={
            <Button type="primary" onClick={refreshUserData} icon={<ReloadOutlined />}>
              Thử lại
            </Button>
          }
        />
      </div>
    );
  }

  if (!currentUser) {
    return (
      <div style={{ padding: '20px' }}>
        <Alert
          message="Không có thông tin người dùng"
          description="Không thể tải thông tin người dùng. Vui lòng đăng nhập lại hoặc làm mới trang."
          type="warning"
          showIcon
          action={
            <Button type="primary" onClick={refreshUserData} icon={<ReloadOutlined />}>
              Làm mới
            </Button>
          }
        />
      </div>
    );
  }

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Title level={2}>Hồ sơ người dùng</Title>
        <Button
          type="primary"
          icon={<ReloadOutlined />}
          onClick={refreshUserData}
          loading={loading}
        >
          Làm mới thông tin
        </Button>
      </div>
      <Divider />

      <Card style={{ width: '100%', marginTop: 16 }}>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: 20 }}>
          <Avatar size={64} icon={<UserOutlined />} />
          <div style={{ marginLeft: 16 }}>
            <Title level={4}>
              {currentUser.user ?
                (currentUser.user.fullName || currentUser.user.name || currentUser.user.username || 'Người dùng') :
                (currentUser.fullName || currentUser.name || currentUser.username || 'Người dùng')}
            </Title>
            <p>
              {currentUser.user ?
                (currentUser.user.email || 'Không có email') :
                (currentUser.email || 'Không có email')}
            </p>
          </div>
        </div>

        <Descriptions bordered column={1}>
          <Descriptions.Item label="ID">
            {currentUser.user ? currentUser.user.id : currentUser.id || 'N/A'}
          </Descriptions.Item>
          <Descriptions.Item label="Tên đăng nhập">
            {currentUser.user ? currentUser.user.username : currentUser.username || 'N/A'}
          </Descriptions.Item>
          <Descriptions.Item label="Vai trò">
            {currentUser.user ? currentUser.user.role : currentUser.role || 'N/A'}
          </Descriptions.Item>
          <Descriptions.Item label="Ngày tạo">
            {currentUser.user && currentUser.user.createdAt ?
              new Date(currentUser.user.createdAt).toLocaleDateString('vi-VN') :
              (currentUser.createdAt ?
                new Date(currentUser.createdAt).toLocaleDateString('vi-VN') : 'N/A')}
          </Descriptions.Item>
          {(currentUser.user && currentUser.user.phone) || currentUser.phone ? (
            <Descriptions.Item label="Số điện thoại">
              {currentUser.user ? currentUser.user.phone : currentUser.phone}
            </Descriptions.Item>
          ) : null}
          {(currentUser.user && currentUser.user.address) || currentUser.address ? (
            <Descriptions.Item label="Địa chỉ">
              {currentUser.user ? currentUser.user.address : currentUser.address}
            </Descriptions.Item>
          ) : null}
        </Descriptions>
      </Card>
    </div>
  );
};

export default Profile;
