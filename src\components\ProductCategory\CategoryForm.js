import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Card, Select, Switch, Typography, Divider } from 'antd';
import { useNavigate, useParams } from 'react-router-dom';
import { Toaster, toast } from 'react-hot-toast';
import ProductCategoryService from '../../services/productCategory.service';

const { Title } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const CategoryForm = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [categories, setCategories] = useState([]);
  const navigate = useNavigate();
  const { id } = useParams();

  useEffect(() => {
    fetchCategories();

    if (id) {
      setIsEdit(true);
      fetchCategoryData();
    }
  }, [id]);

  const fetchCategories = async () => {
    try {
      const response = await ProductCategoryService.getAllCategories();
      const categoriesData = response.data;

      // Tạo cấu trúc phân cấp cho dropdown
      const rootCategories = categoriesData.filter(cat => !cat.parentId)
        .sort((a, b) => a.name.localeCompare(b.name));

      // Tạo map để tra cứu nhanh các nhóm con
      const childrenMap = {};
      rootCategories.forEach(parent => {
        childrenMap[parent.id] = categoriesData
          .filter(cat => cat.parentId === parent.id)
          .sort((a, b) => a.name.localeCompare(b.name));
      });

      // Lưu toàn bộ dữ liệu để sử dụng sau này
      setCategories({
        roots: rootCategories,
        all: categoriesData,
        childrenMap: childrenMap
      });
    } catch (error) {
      toast.error('Không thể tải danh sách nhóm hàng: ' + error.message);
    }
  };

  const fetchCategoryData = async () => {
    setLoading(true);
    try {
      const response = await ProductCategoryService.getCategoryById(id);
      const categoryData = response.data;
      form.setFieldsValue({
        name: categoryData.name,
        parentId: categoryData.parentId,
        description: categoryData.description,
        isActive: categoryData.isActive,
      });

      // Đảm bảo rằng chúng ta có dữ liệu nhóm hàng trước khi hiển thị form
      if (!categories.roots) {
        fetchCategories();
      }
    } catch (error) {
      toast.error('Không thể tải thông tin nhóm hàng: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const onFinish = async (values) => {
    setLoading(true);
    try {
      if (isEdit) {
        await ProductCategoryService.updateCategory(id, values);
        toast.success('Cập nhật nhóm hàng thành công');
      } else {
        await ProductCategoryService.createCategory(values);
        toast.success('Thêm nhóm hàng thành công');
      }
      navigate('/product-categories');
    } catch (error) {
      toast.error('Lỗi: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <Toaster position="top-right" />
      <Title level={2}>{isEdit ? 'Cập nhật nhóm hàng' : 'Thêm nhóm hàng mới'}</Title>
      <Divider />

      <Card>
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
        >
          <Form.Item
            name="name"
            label="Tên nhóm hàng"
            rules={[{ required: true, message: 'Vui lòng nhập tên nhóm hàng' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="parentId"
            label="Nhóm cha"
          >
            <Select
              placeholder="Chọn nhóm cha"
              allowClear
              dropdownRender={menu => {
                return categories.roots ? menu : <div style={{ padding: 8 }}>Đang tải...</div>;
              }}
            >
              {categories.roots && categories.roots
                .filter(category => {
                  // Loại bỏ category hiện tại và các category con của nó để tránh tạo vòng lặp
                  if (isEdit && (category.id === parseInt(id))) {
                    return false;
                  }
                  return true;
                })
                .map(category => (
                  <Select.OptGroup key={category.id} label={<strong>{category.name}</strong>}>
                    {/* Thêm nhóm cha như một option */}
                    <Option key={category.id} value={category.id}>
                      <strong>{category.name}</strong>
                    </Option>

                    {/* Thêm các nhóm con */}
                    {categories.childrenMap[category.id] && categories.childrenMap[category.id]
                      .filter(child => {
                        // Loại bỏ category hiện tại và các category con của nó
                        if (isEdit && (child.id === parseInt(id) || child.parentId === parseInt(id))) {
                          return false;
                        }
                        return true;
                      })
                      .map(child => (
                        <Option key={child.id} value={child.id}>
                          <span style={{ paddingLeft: '20px' }}>↳ {child.name}</span>
                        </Option>
                      ))
                    }
                  </Select.OptGroup>
                ))
              }
            </Select>
          </Form.Item>

          <Form.Item
            name="description"
            label="Mô tả"
          >
            <TextArea rows={4} />
          </Form.Item>

          <Form.Item
            name="isActive"
            label="Trạng thái"
            valuePropName="checked"
          >
            <Switch checkedChildren="Hoạt động" unCheckedChildren="Không hoạt động" defaultChecked />
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading}>
              {isEdit ? 'Cập nhật' : 'Thêm mới'}
            </Button>
            <Button style={{ marginLeft: 8 }} onClick={() => navigate('/product-categories')}>
              Hủy
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default CategoryForm;
