import React, { useState, useEffect } from 'react';
import { Card, Descriptions, Table, Typography, Button, Tag, Divider, Space } from 'antd';
import { useParams, useNavigate } from 'react-router-dom';
import { Toaster, toast } from 'react-hot-toast';
import { PrinterOutlined, EditOutlined } from '@ant-design/icons';
import ReturnService from '../../services/return.service';
import { formatCurrencyWithSymbol } from '../../utils/format';
import './PrintStyles.css';

const { Title } = Typography;

const ReturnDetail = () => {
  const [returnData, setReturnData] = useState(null);
  const [loading, setLoading] = useState(true);
  const { id } = useParams();
  const navigate = useNavigate();

  useEffect(() => {
    fetchReturnData();
  }, [id]);

  const fetchReturnData = async () => {
    setLoading(true);
    try {
      const response = await ReturnService.getReturnById(id);
      console.log('<PERSON><PERSON> liệu chi tiết phiếu trả hàng:', response.data);

      // Xử lý dữ liệu trước khi set state
      const data = response.data;

      // Kiểm tra và xử lý danh sách sản phẩm
      if (!data.items && data.ReturnItems && data.ReturnItems.length > 0) {
        data.items = data.ReturnItems;
      }

      setReturnData(data);
    } catch (error) {
      toast.error('Không thể tải thông tin phiếu trả hàng: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'green';
      case 'pending':
        return 'orange';
      case 'cancelled':
        return 'red';
      default:
        return 'default';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'completed':
        return 'Đã hoàn thành';
      case 'pending':
        return 'Chờ xử lý';
      case 'cancelled':
        return 'Đã hủy';
      default:
        return status;
    }
  };

  // Chức năng in đơn giản
  const handlePrint = () => {
    window.print();
  };

  // Chức năng in nâng cao - mở cửa sổ in mới với định dạng tốt hơn
  const handlePrintAdvanced = () => {
    // Tạo một trang in hoàn toàn mới
    const printWindow = window.open('', '_blank');

    if (!printWindow) {
      alert('Trình duyệt đã chặn cửa sổ pop-up. Vui lòng cho phép pop-up và thử lại.');
      return;
    }

    // Cập nhật tiêu đề
    printWindow.document.title = `Phiếu trả hàng - ${returnData.returnCode}`;

    // Tạo HTML cho trang mới với layout giống ImportDetail.js
    const printHtml = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Phiếu trả hàng - ${returnData.returnCode}</title>
        <style>
          @page {
            size: landscape !important;
            margin: 1cm !important;
          }

          body {
            font-family: 'Segoe UI', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            box-sizing: border-box;
            font-size: 14px;
          }

          /* Đảm bảo phiếu hiển thị đúng */
          .print-content {
            width: 100%;
            max-width: 100%;
            padding: 0;
            margin: 0;
          }

          /* Tiêu đề phiếu */
          .print-header {
            text-align: center;
            margin-bottom: 20px;
          }

          .print-header h3 {
            font-size: 24px;
            margin: 10px 0;
            font-weight: bold;
            text-transform: uppercase;
          }

          .print-header p {
            margin: 5px 0;
            font-size: 14px;
          }

          /* Bảng thông tin */
          table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
          }

          table, th, td {
            border: 1px solid #d9d9d9;
          }

          th, td {
            padding: 10px;
            text-align: left;
            font-size: 14px;
          }

          th {
            background-color: #f5f5f5;
            font-weight: bold;
          }

          /* Phần chữ ký */
          .signature-section {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
          }

          .signature-block {
            width: 30%;
            text-align: center;
          }

          .signature-block p {
            margin: 5px 0;
            font-size: 14px;
          }

          /* Tổng tiền */
          .total-table td:first-child {
            width: 15%;
            font-weight: bold;
          }

          .divider {
            border-top: 1px solid #d9d9d9;
            margin: 15px 0;
          }

          /* Định dạng tag trạng thái */
          .status-tag {
            display: inline-block;
            padding: 4px 10px;
            border-radius: 4px;
            font-size: 14px;
          }

          .status-green {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
          }

          .status-orange {
            background-color: #fff7e6;
            border: 1px solid #ffd591;
            color: #fa8c16;
          }

          .status-red {
            background-color: #fff1f0;
            border: 1px solid #ffa39e;
            color: #f5222d;
          }
        </style>
      </head>
      <body>
        <div class="print-content">
          <!-- Phần tiêu đề -->
          <div class="print-header">
            <h3>PHIẾU TRẢ HÀNG</h3>
            <p>Mã phiếu trả hàng: ${returnData.returnCode}</p>
            <p>Ngày: ${new Date(returnData.createdAt).toLocaleDateString('vi-VN')}</p>
          </div>

          <!-- Phần thông tin khách hàng -->
          <table>
            <tr>
              <td width="15%"><strong>Khách hàng:</strong></td>
              <td colspan="3">${returnData.Customer?.name || returnData.customer?.name || 'N/A'}</td>
            </tr>
            <tr>
              <td><strong>Địa chỉ:</strong></td>
              <td colspan="3">${returnData.Customer?.address || returnData.customer?.address || 'N/A'}</td>
            </tr>
            <tr>
              <td><strong>Số điện thoại:</strong></td>
              <td colspan="3">${returnData.Customer?.phone || returnData.customer?.phone || 'N/A'}</td>
            </tr>
            <tr>
              <td><strong>Hóa đơn liên quan:</strong></td>
              <td>${returnData.Invoice?.invoiceCode || returnData.invoice?.invoiceCode || 'N/A'}</td>
              <td width="15%"><strong>Trạng thái:</strong></td>
              <td>
                <span class="status-tag status-${getStatusColor(returnData.status)}">
                  ${getStatusText(returnData.status)}
                </span>
              </td>
            </tr>
          </table>

          <div class="divider"></div>

          <!-- Phần bảng sản phẩm -->
          <table>
            <thead>
              <tr>
                <th style="width: 60px;">STT</th>
                <th>Sản phẩm</th>
                <th>Đơn giá</th>
                <th>Số lượng</th>
                <th>Thành tiền</th>
                <th>Lý do trả hàng</th>
              </tr>
            </thead>
            <tbody>
              ${returnData.items.map((item, index) => {
                const productName = item.Product?.name || item.product?.name || 'N/A';
                return `
                  <tr>
                    <td>${index + 1}</td>
                    <td>${productName}</td>
                    <td>${formatCurrencyWithSymbol(item.price)}</td>
                    <td>${item.quantity}</td>
                    <td>${formatCurrencyWithSymbol(item.amount)}</td>
                    <td>${item.reason || ''}</td>
                  </tr>
                `;
              }).join('')}
            </tbody>
          </table>

          <!-- Phần tổng tiền nằm ở table riêng, giống ImportDetail.js -->
          <table class="total-table">
            <tr>
              <td width="15%"><strong>Tổng cộng:</strong></td>
              <td width="85%">${formatCurrencyWithSymbol(returnData.totalAmount)}</td>
            </tr>
          </table>

          ${returnData.note ? `
          <div class="divider"></div>
          <div class="print-note">
            <strong>Ghi chú:</strong>
            <p>${returnData.note}</p>
          </div>
          ` : ''}

          <div class="divider"></div>

          <!-- Phần chữ ký -->
          <div class="signature-section">
            <div class="signature-block">
              <p><strong>Người lập phiếu</strong></p>
              <p>(Ký, họ tên)</p>
            </div>
            <div class="signature-block">
              <p><strong>Khách hàng</strong></p>
              <p>(Ký, họ tên)</p>
            </div>
            <div class="signature-block">
              <p><strong>Thủ kho</strong></p>
              <p>(Ký, họ tên)</p>
            </div>
          </div>
        </div>

        <script>
          // Tự động in khi trang đã tải xong
          window.onload = function() {
            window.print();
            // Sau khi in xong sẽ tự đóng cửa sổ sau 1 giây
            setTimeout(function() {
              window.close();
            }, 1000);
          };
        </script>
      </body>
      </html>
    `;

    // Ghi HTML vào cửa sổ mới
    printWindow.document.open();
    printWindow.document.write(printHtml);
    printWindow.document.close();
  };

  const columns = [
    {
      title: 'STT',
      key: 'index',
      width: 60,
      render: (_, __, index) => index + 1,
    },
    {
      title: 'Sản phẩm',
      key: 'product',
      render: (_, record) => {
        if (record.Product && record.Product.name) {
          return record.Product.name;
        }
        if (record.product && record.product.name) {
          return record.product.name;
        }
        return 'N/A';
      },
    },
    {
      title: 'Đơn giá',
      dataIndex: 'price',
      key: 'price',
      render: (price) => formatCurrencyWithSymbol(price),
    },
    {
      title: 'Số lượng',
      dataIndex: 'quantity',
      key: 'quantity',
    },
    {
      title: 'Thành tiền',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount) => formatCurrencyWithSymbol(amount),
    },
    {
      title: 'Lý do trả hàng',
      dataIndex: 'reason',
      key: 'reason',
    },
  ];

  if (loading) {
    return <div>Đang tải...</div>;
  }

  if (!returnData) {
    toast.error("Không tìm thấy phiếu trả hàng");
    return <div></div>;
  }

  return (
    <div className="return-detail-container">
      <Toaster position="top-right" />
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
        <Title level={2}>Chi tiết phiếu trả hàng</Title>
        <Space>
          <Button type="primary" icon={<EditOutlined />} onClick={() => navigate(`/returns/edit/${id}`)}>
            Sửa
          </Button>
          <Button icon={<PrinterOutlined />} onClick={handlePrintAdvanced}>
            In phiếu trả hàng
          </Button>
        </Space>
      </div>

      <Card className="print-container" bordered={true}>
        <div className="print-header" style={{ textAlign: 'center', marginBottom: 24 }}>
          <Title level={3} className="print-title">PHIẾU TRẢ HÀNG</Title>
          <p className="print-code">Mã phiếu trả hàng: {returnData.returnCode}</p>
          <p className="print-date">Ngày: {new Date(returnData.createdAt).toLocaleDateString('vi-VN')}</p>
        </div>

        <Descriptions bordered column={2} className="print-descriptions">
          <Descriptions.Item label="Khách hàng" span={2}>
            {(returnData.Customer?.name || returnData.Customer?.fullName || returnData.customer?.name || returnData.customer?.fullName || 'N/A')}
          </Descriptions.Item>
          <Descriptions.Item label="Địa chỉ">
            {(returnData.Customer?.address || returnData.customer?.address || 'N/A')}
          </Descriptions.Item>
          <Descriptions.Item label="Số điện thoại">
            {(returnData.Customer?.phone || returnData.customer?.phone || 'N/A')}
          </Descriptions.Item>
          <Descriptions.Item label="Hóa đơn liên quan">
            {(returnData.Invoice?.invoiceCode || returnData.invoice?.invoiceCode || 'N/A')}
          </Descriptions.Item>
          <Descriptions.Item label="Trạng thái">
            <Tag color={getStatusColor(returnData.status)} className={`status-tag status-${returnData.status}`}>
              {getStatusText(returnData.status)}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="Ngày tạo">
            {new Date(returnData.createdAt).toLocaleDateString('vi-VN')}
          </Descriptions.Item>
        </Descriptions>

        <Divider className="print-divider" />

        <Table
          className="print-table"
          columns={columns}
          dataSource={returnData.items}
          rowKey={(_, index) => index}
          pagination={false}
          bordered
          summary={() => (
            <Table.Summary fixed className="print-summary">
              <Table.Summary.Row>
                <Table.Summary.Cell index={0} colSpan={4} style={{ textAlign: 'right' }}>
                  <strong>Tổng cộng:</strong>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={1} colSpan={2}>
                  <strong>{formatCurrencyWithSymbol(returnData.totalAmount)}</strong>
                </Table.Summary.Cell>
              </Table.Summary.Row>
            </Table.Summary>
          )}
        />

        {returnData.note && (
          <>
            <Divider className="print-divider" />
            <div className="print-note">
              <strong>Ghi chú:</strong>
              <p>{returnData.note}</p>
            </div>
          </>
        )}

        <Divider className="print-divider" />

        <div className="print-signatures" style={{ display: 'flex', justifyContent: 'space-between', marginTop: 30 }}>
          <div className="signature-block" style={{ textAlign: 'center', width: '30%' }}>
            <p><strong>Người lập phiếu</strong></p>
            <p>(Ký, họ tên)</p>
          </div>
          <div className="signature-block" style={{ textAlign: 'center', width: '30%' }}>
            <p><strong>Khách hàng</strong></p>
            <p>(Ký, họ tên)</p>
          </div>
          <div className="signature-block" style={{ textAlign: 'center', width: '30%' }}>
            <p><strong>Thủ kho</strong></p>
            <p>(Ký, họ tên)</p>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default ReturnDetail;
