import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Card, Select, DatePicker, InputNumber, Typography, Divider, Modal, Switch, Space } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { Toaster, toast } from 'react-hot-toast';
import { useNavigate, useParams } from 'react-router-dom';
import moment from 'moment';
import ReceiptService from '../../services/receipt.service';
import ReceiptTypeService from '../../services/receiptType.service';
import UserService from '../../services/user.service';
import PayerService from '../../services/payer.service';
import BankAccountService from '../../services/bankAccount.service';
import BranchService from '../../services/branch.service';
import InvoiceService from '../../services/invoice.service';

const { Title } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const ReceiptForm = () => {
  const [form] = Form.useForm();
  const [payerForm] = Form.useForm();
  const [bankAccountForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [receiptTypes, setReceiptTypes] = useState([]);
  const [users, setUsers] = useState([]);
  const [payers, setPayers] = useState([]);
  const [bankAccounts, setBankAccounts] = useState([]);
  const [branches, setBranches] = useState([]);
  const [invoices, setInvoices] = useState([]);
  const [selectedReceiptType, setSelectedReceiptType] = useState(null);
  const [selectedPayerId, setSelectedPayerId] = useState(null);
  const [selectedBankAccountId, setSelectedBankAccountId] = useState(null);
  const [payerModalVisible, setPayerModalVisible] = useState(false);
  const [bankAccountModalVisible, setBankAccountModalVisible] = useState(false);
  const [invoiceDebtInfo, setInvoiceDebtInfo] = useState(null); // Thông tin công nợ của hóa đơn
  const navigate = useNavigate();
  const { id } = useParams();

  useEffect(() => {
    fetchReceiptTypes();
    fetchUsers();
    fetchPayers();
    fetchBankAccounts();
    fetchBranches();
    fetchInvoices();

    if (id) {
      setIsEdit(true);
      fetchReceiptData();
    }
  }, [id]);

  const fetchReceiptTypes = async () => {
    try {
      const response = await ReceiptTypeService.getAllReceiptTypes();
      setReceiptTypes(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách loại thu: ' + error.message);
    }
  };

  const fetchUsers = async () => {
    try {
      const response = await UserService.getAllUsers();
      setUsers(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách người dùng: ' + error.message);
    }
  };

  const fetchPayers = async () => {
    try {
      const response = await PayerService.getAllPayers();
      setPayers(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách người nộp: ' + error.message);
    }
  };

  const showAddPayerModal = () => {
    payerForm.resetFields();
    setPayerModalVisible(true);
  };

  const handleAddPayerModalCancel = () => {
    setPayerModalVisible(false);
  };

  const handleAddPayerModalOk = async () => {
    try {
      const values = await payerForm.validateFields();
      setLoading(true);

      // Gọi API để tạo người nộp mới
      const response = await PayerService.createPayer(values);
      const newPayer = response.data;

      toast.success('Thêm người nộp mới thành công');

      // Cập nhật danh sách người nộp
      setPayers([...payers, newPayer]);

      // Chọn người nộp vừa tạo trong form
      setSelectedPayerId(newPayer.id);
      form.setFieldsValue({ 
        payerId: newPayer.id,
        payerName: newPayer.name
      });

      setPayerModalVisible(false);
    } catch (error) {
      if (error.errorFields) {
        toast.error('Vui lòng điền đầy đủ thông tin người nộp');
      } else {
        toast.error('Lỗi khi thêm người nộp: ' + error.message);
      }
    } finally {
      setLoading(false);
    }
  };

  const showAddBankAccountModal = () => {
    bankAccountForm.resetFields();
    setBankAccountModalVisible(true);
  };

  const handleAddBankAccountModalCancel = () => {
    setBankAccountModalVisible(false);
  };

  const handleAddBankAccountModalOk = async () => {
    try {
      const values = await bankAccountForm.validateFields();
      setLoading(true);

      // Gọi API để tạo tài khoản ngân hàng mới
      const response = await BankAccountService.createBankAccount(values);
      const newBankAccount = response.data;

      toast.success('Thêm tài khoản ngân hàng mới thành công');

      // Cập nhật danh sách tài khoản ngân hàng
      setBankAccounts([...bankAccounts, newBankAccount]);

      // Chọn tài khoản ngân hàng vừa tạo trong form
      setSelectedBankAccountId(newBankAccount.id);
      form.setFieldsValue({ 
        bankAccountId: newBankAccount.id
      });

      setBankAccountModalVisible(false);
    } catch (error) {
      if (error.errorFields) {
        toast.error('Vui lòng điền đầy đủ thông tin tài khoản ngân hàng');
      } else {
        toast.error('Lỗi khi thêm tài khoản ngân hàng: ' + error.message);
      }
    } finally {
      setLoading(false);
    }
  };

  const fetchBankAccounts = async () => {
    try {
      const response = await BankAccountService.getAllBankAccounts();
      setBankAccounts(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách tài khoản ngân hàng: ' + error.message);
    }
  };

  const fetchBranches = async () => {
    try {
      const response = await BranchService.getAllBranches();
      setBranches(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách chi nhánh: ' + error.message);
    }
  };

  const fetchInvoices = async () => {
    try {
      const response = await InvoiceService.getAllInvoices();
      setInvoices(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách hóa đơn: ' + error.message);
    }
  };

  const fetchReceiptData = async () => {
    setLoading(true);
    try {
      const response = await ReceiptService.getReceiptById(id);
      const receiptData = response.data;

      setSelectedReceiptType(receiptData.receiptTypeId);
      setSelectedPayerId(receiptData.payerId);
      setSelectedBankAccountId(receiptData.bankAccountId);

      form.setFieldsValue({
        receiptCode: receiptData.receiptCode,
        receiptDate: receiptData.receiptDate ? moment(receiptData.receiptDate) : moment(),
        receiptTypeId: receiptData.receiptTypeId,
        amount: receiptData.amount,
        userId: receiptData.userId,
        payerId: receiptData.payerId,
        payerName: receiptData.payerName,
        bankAccountId: receiptData.bankAccountId,
        note: receiptData.note,
        branchId: receiptData.branchId,
        status: receiptData.status,
        invoiceId: receiptData.invoiceId,
      });
      
      // Chỉ lấy thông tin công nợ hóa đơn mà không cập nhật số tiền khi đang chỉnh sửa
      if (receiptData.invoiceId) {
        fetchInvoiceDebtInfo(receiptData.invoiceId);
      }
    } catch (error) {
      toast.error('Không thể tải thông tin phiếu thu: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleReceiptTypeChange = (value) => {
    setSelectedReceiptType(value);
    
    // Nếu loại thu không phải là chuyển khoản, xóa giá trị tài khoản ngân hàng
    const receiptType = receiptTypes.find(type => type.id === value);
    if (receiptType && receiptType.type !== 'transfer') {
      form.setFieldsValue({ bankAccountId: undefined });
      setSelectedBankAccountId(null);
    }
  };

  /**
   * Chỉ lấy thông tin công nợ hóa đơn mà không cập nhật số tiền hoặc người nộp
   * Dùng khi đang chỉnh sửa phiếu thu
   */
  const fetchInvoiceDebtInfo = async (invoiceId) => {
    if (!invoiceId) return;
    
    try {
      console.log('Lấy thông tin công nợ hóa đơn ID:', invoiceId);
      const invoiceResponse = await InvoiceService.getInvoiceById(invoiceId);
      const selectedInvoice = invoiceResponse.data;
      
      if (selectedInvoice) {
        // Tính toán công nợ
        const totalAmount = parseFloat(selectedInvoice.totalAmount || 0);
        const paidAmount = parseFloat(selectedInvoice.paidAmount || 0);
        const debtAmount = totalAmount - paidAmount;
        
        // Chỉ lưu thông tin công nợ hóa đơn để hiển thị
        setInvoiceDebtInfo({
          invoiceCode: selectedInvoice.invoiceCode || `HD${selectedInvoice.id}`,
          totalAmount,
          paidAmount,
          debtAmount,
          date: selectedInvoice.createdAt || selectedInvoice.invoiceDate
        });
        
        console.log('Chỉ lấy thông tin công nợ, không cập nhật số tiền');
      }
    } catch (error) {
      console.error('Lỗi khi lấy thông tin công nợ hóa đơn:', error);
      toast.error('Không thể lấy thông tin công nợ hóa đơn: ' + error.message);
    }
  };

  /**
   * Xử lý khi chọn hóa đơn liên quan
   * Lấy thông tin công nợ, tự động điền số tiền và chọn người nộp
   */
  const handleInvoiceChange = async (value) => {
    // Reset thông tin công nợ
    setInvoiceDebtInfo(null);
    
    if (!value) {
      // Nếu không chọn hóa đơn, không làm gì cả
      form.setFieldsValue({ payerId: undefined, payerName: undefined });
      return;
    }
    
    try {
      console.log('Đang lấy thông tin hóa đơn ID:', value);
      // Lấy thông tin chi tiết hóa đơn từ API
      const invoiceResponse = await InvoiceService.getInvoiceById(value);
      const selectedInvoice = invoiceResponse.data;
      console.log('Đã nhận dữ liệu hóa đơn:', selectedInvoice);
      
      if (selectedInvoice) {
        // Tính toán công nợ
        const totalAmount = parseFloat(selectedInvoice.totalAmount || 0);
        const paidAmount = parseFloat(selectedInvoice.paidAmount || 0);
        const debtAmount = totalAmount - paidAmount;
        
        console.log('Thông tin công nợ:', { totalAmount, paidAmount, debtAmount });
        
        // Lưu thông tin công nợ hóa đơn
        setInvoiceDebtInfo({
          invoiceCode: selectedInvoice.invoiceCode || `HD${selectedInvoice.id}`,
          totalAmount,
          paidAmount,
          debtAmount,
          date: selectedInvoice.createdAt || selectedInvoice.invoiceDate
        });
        
        // Điền số tiền còn nợ vào form nếu có công nợ và đang tạo mới phiếu thu
        if (debtAmount > 0 && !isEdit) {
          console.log('Điền số tiền còn nợ:', debtAmount);
          form.setFieldsValue({ amount: debtAmount });
        }
        
        // Tự động chọn khách hàng
        const customerId = selectedInvoice.customerId || selectedInvoice.CustomerId;
        const customerName = selectedInvoice.customer?.name || selectedInvoice.Customer?.name;
        console.log('ID khách hàng từ hóa đơn:', customerId, 'Tên:', customerName);
        
        if (customerId || customerName) {
          // Tìm người nộp tương ứng với khách hàng
          const matchingPayer = payers.find(p => 
            (customerId && p.id === customerId) || 
            (customerName && p.name === customerName)
          );
          
          console.log('Tìm thấy người nộp:', matchingPayer);
          
          if (matchingPayer) {
            // Nếu tìm thấy người nộp tương ứng, cập nhật form
            setSelectedPayerId(matchingPayer.id);
            form.setFieldsValue({ 
              payerId: matchingPayer.id,
              payerName: matchingPayer.name
            });
            toast.success('Đã tự động chọn người nộp từ hóa đơn');
          } else {
            // Không tìm thấy người nộp, chỉ thông báo
            toast.error('Không tìm thấy người nộp phù hợp trong danh sách');
          }
        }
      }
    } catch (error) {
      console.error('Lỗi khi lấy thông tin hóa đơn:', error);
      toast.error('Không thể lấy thông tin hóa đơn: ' + error.message);
    }
  };

  const onFinish = async (values) => {
    setLoading(true);
    try {
      const receiptData = {
        ...values,
        receiptDate: values.receiptDate.format('YYYY-MM-DD HH:mm:ss'),
        // Đảm bảo các trường bắt buộc có giá trị
        amount: values.amount || 0,
        status: values.status || 'completed'
      };

      // Khi tạo mới, không gửi receiptCode để backend tự động tạo
      if (!isEdit) {
        delete receiptData.receiptCode;
      }

      let response;
      if (isEdit) {
        response = await ReceiptService.updateReceipt(id, receiptData);
        toast.success('Cập nhật phiếu thu thành công');
      } else {
        response = await ReceiptService.createReceipt(receiptData);
        toast.success('Tạo phiếu thu thành công');
        
        // Cập nhật công nợ hóa đơn nếu phiếu thu liên quan đến hóa đơn
        if (values.invoiceId) {
          try {
            // Lấy thông tin hóa đơn hiện tại
            const invoiceResponse = await InvoiceService.getInvoiceById(values.invoiceId);
            const invoice = invoiceResponse.data;
            
            // Tính toán số tiền đã thanh toán mới
            const currentPaidAmount = parseFloat(invoice.paidAmount || 0);
            const receiptAmount = parseFloat(values.amount || 0);
            const newPaidAmount = currentPaidAmount + receiptAmount;
            
            console.log('Cập nhật công nợ hóa đơn:', {
              invoiceId: values.invoiceId,
              currentPaidAmount,
              receiptAmount,
              newPaidAmount
            });
            
            // Cập nhật số tiền đã thanh toán của hóa đơn
            await InvoiceService.updateInvoice(values.invoiceId, {
              paidAmount: newPaidAmount
            });
            
            toast.success('Đã cập nhật công nợ hóa đơn');
          } catch (invoiceError) {
            console.error('Lỗi khi cập nhật công nợ hóa đơn:', invoiceError);
            toast.error('Không thể cập nhật công nợ hóa đơn: ' + invoiceError.message);
          }
        }
      }
      navigate('/receipts');
    } catch (error) {
      console.error('Lỗi chi tiết:', error);
      toast.error('Lỗi: ' + error.message);
    } finally {
      setLoading(false);
    }  
  };

  // Kiểm tra xem loại thu có phải là chuyển/rút không
  const isTransferType = () => {
    if (!selectedReceiptType) return false;
    const receiptType = receiptTypes.find(type => type.id === selectedReceiptType);
    return receiptType && receiptType.type === 'transfer';
  };

  return (
    <div>
      <Toaster position="top-right" />
      <Title level={2}>{isEdit ? 'Cập nhật phiếu thu' : 'Tạo phiếu thu mới'}</Title>
      <Divider />

      <Card>
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          initialValues={{
            receiptDate: moment(),
            status: 'completed',
          }}
        >
          <div style={{ display: 'flex', gap: '16px' }}>
            <Form.Item
              name="receiptCode"
              label="Mã phiếu thu"
              tooltip={isEdit ? "" : "Mã phiếu thu sẽ được tạo tự động (TTHD...)"}
              style={{ flex: 1 }}
            >
              <Input disabled={!isEdit} placeholder="Tự động tạo (TTHD...)" />
            </Form.Item>

            <Form.Item
              name="receiptDate"
              label="Ngày thu"
              rules={[{ required: true, message: 'Vui lòng chọn ngày thu' }]}
              style={{ flex: 1 }}
            >
              <DatePicker
                showTime
                format="DD/MM/YYYY HH:mm:ss"
                style={{ width: '100%' }}
              />
            </Form.Item>

            <Form.Item
              name="status"
              label="Trạng thái"
              rules={[{ required: true, message: 'Vui lòng chọn trạng thái' }]}
              style={{ flex: 1 }}
            >
              <Select>
                <Option value="pending">Chờ xử lý</Option>
                <Option value="completed">Đã hoàn thành</Option>
                <Option value="cancelled">Đã hủy</Option>
              </Select>
            </Form.Item>
          </div>

          <div style={{ display: 'flex', gap: '16px' }}>
            <Form.Item
              name="receiptTypeId"
              label="Loại thu"
              rules={[{ required: true, message: 'Vui lòng chọn loại thu' }]}
              style={{ flex: 1 }}
            >
              <Select
                placeholder="Chọn loại thu"
                onChange={handleReceiptTypeChange}
              >
                {receiptTypes.map(type => (
                  <Option key={type.id} value={type.id}>{type.name}</Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="amount"
              label="Số tiền"
              rules={[{ required: true, message: 'Vui lòng nhập số tiền' }]}
              style={{ flex: 1 }}
            >
              <InputNumber
                style={{ width: '100%' }}
                formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={value => value.replace(/\$\s?|(,*)/g, '')}
                min={0}
              />
            </Form.Item>

            <Form.Item
              name="branchId"
              label="Chi nhánh"
              style={{ flex: 1 }}
            >
              <Select placeholder="Chọn chi nhánh" allowClear>
                {branches.map(branch => (
                  <Option key={branch.id} value={branch.id}>{branch.name}</Option>
                ))}
              </Select>
            </Form.Item>
          </div>

          <div style={{ display: 'flex', gap: '16px' }}>
            <Form.Item
              name="payerId"
              label="Người nộp"
              style={{ flex: 1 }}
            >
              <div style={{ display: 'flex', gap: '8px' }}>
                <Select
                  placeholder="Chọn người nộp"
                  allowClear
                  value={selectedPayerId}
                  onChange={(value) => {
                    setSelectedPayerId(value);
                    if (value) {
                      const payer = payers.find(p => p.id === value);
                      if (payer) {
                        form.setFieldsValue({ payerName: payer.name });
                      }
                    } else {
                      form.setFieldsValue({ payerName: '' });
                    }
                  }}
                  style={{ flex: 1 }}
                >
                  {payers.map(payer => (
                    <Option key={payer.id} value={payer.id}>{payer.name}</Option>
                  ))}
                </Select>
                <Button 
                  type="primary" 
                  icon={<PlusOutlined />} 
                  onClick={showAddPayerModal}
                  style={{ flexShrink: 0 }}
                >
                  Thêm mới
                </Button>
              </div>
            </Form.Item>

            <Form.Item
              name="payerName"
              label="Tên người nộp"
              style={{ flex: 1 }}
            >
              <Input />
            </Form.Item>

            <Form.Item
              name="userId"
              label="Nhân viên thu"
              style={{ flex: 1 }}
            >
              <Select placeholder="Chọn nhân viên thu" allowClear>
                {users.map(user => (
                  <Option key={user.id} value={user.id}>{user.fullName}</Option>
                ))}
              </Select>
            </Form.Item>
          </div>

          <div style={{ display: 'flex', gap: '16px' }}>
            {isTransferType() && (
              <Form.Item
                name="bankAccountId"
                label="Tài khoản ngân hàng"
                rules={[{ required: isTransferType(), message: 'Vui lòng chọn tài khoản ngân hàng' }]}
                style={{ flex: 1 }}
              >
                <Space.Compact style={{ width: '100%' }}>
                  <Select 
                    placeholder="Chọn tài khoản ngân hàng" 
                    allowClear
                    value={selectedBankAccountId}
                    onChange={(value) => {
                      setSelectedBankAccountId(value);
                      form.setFieldsValue({ bankAccountId: value });
                    }}
                    style={{ width: 'calc(100% - 40px)' }}
                  >
                    {bankAccounts.map(account => (
                      <Option key={account.id} value={account.id}>
                        {account.accountNumber} - {account.bankName}
                      </Option>
                    ))}
                  </Select>
                  <Button 
                    type="primary" 
                    icon={<PlusOutlined />} 
                    onClick={showAddBankAccountModal}
                    style={{ width: '40px' }}
                  />
                </Space.Compact>
              </Form.Item>
            )}

            <Form.Item
              name="invoiceId"
              label="Hóa đơn liên quan"
              style={{ flex: 1 }}
            >
              <Select 
                placeholder="Chọn hóa đơn" 
                allowClear
                onChange={handleInvoiceChange}
              >
                {invoices.map(invoice => (
                  <Option key={invoice.id} value={invoice.id}>
                    {invoice.invoiceCode || invoice.code} - {invoice.customer?.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
            
            {/* Hiển thị thông tin công nợ của hóa đơn */}
            {invoiceDebtInfo && (
              <div style={{ 
                marginBottom: 24, 
                padding: 16, 
                background: '#f6ffed', 
                border: '1px solid #b7eb8f',
                borderRadius: 4
              }}>
                <div style={{ fontWeight: 'bold', marginBottom: 8, fontSize: 16, color: '#52c41a' }}>
                  Thông tin công nợ hóa đơn: {invoiceDebtInfo.invoiceCode}
                </div>
                <div style={{ display: 'flex', flexDirection: 'column', gap: 8 }}>
                  <div>Tổng tiền: <span style={{ fontWeight: 'bold' }}>{new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(invoiceDebtInfo.totalAmount)}</span></div>
                  <div>Đã thanh toán: <span style={{ fontWeight: 'bold' }}>{new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(invoiceDebtInfo.paidAmount)}</span></div>
                  <div>Còn nợ: <span style={{ fontWeight: 'bold', color: invoiceDebtInfo.debtAmount > 0 ? '#f5222d' : '#52c41a' }}>{new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(invoiceDebtInfo.debtAmount)}</span></div>
                </div>
              </div>
            )}
          </div>

          <Form.Item
            name="note"
            label="Ghi chú"
          >
            <TextArea rows={4} />
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading}>
              {isEdit ? 'Cập nhật' : 'Tạo phiếu thu'}
            </Button>
            <Button style={{ marginLeft: 8 }} onClick={() => navigate('/receipts')}>
              Hủy
            </Button>
          </Form.Item>
        </Form>
      </Card>

      {/* Modal thêm người nộp mới */}
      <Modal
        title="Thêm người nộp mới"
        open={payerModalVisible}
        onOk={handleAddPayerModalOk}
        onCancel={handleAddPayerModalCancel}
        width={600}
        confirmLoading={loading}
      >
        <Form
          form={payerForm}
          layout="vertical"
          initialValues={{ isActive: true }}
        >
          <Form.Item
            name="name"
            label="Tên người nộp"
            rules={[{ required: true, message: 'Vui lòng nhập tên người nộp' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="phone"
            label="Số điện thoại"
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="email"
            label="Email"
            rules={[
              { type: 'email', message: 'Email không hợp lệ' },
            ]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="address"
            label="Địa chỉ"
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="area"
            label="Khu vực"
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="ward"
            label="Phường xã"
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="note"
            label="Ghi chú"
          >
            <TextArea rows={4} />
          </Form.Item>

          <Form.Item
            name="isActive"
            label="Trạng thái"
            valuePropName="checked"
          >
            <Switch checkedChildren="Hoạt động" unCheckedChildren="Không hoạt động" defaultChecked />
          </Form.Item>
        </Form>
      </Modal>

      {/* Modal thêm tài khoản ngân hàng mới */}
      <Modal
        title="Thêm tài khoản ngân hàng mới"
        open={bankAccountModalVisible}
        onOk={handleAddBankAccountModalOk}
        onCancel={handleAddBankAccountModalCancel}
        width={600}
        confirmLoading={loading}
      >
        <Form
          form={bankAccountForm}
          layout="vertical"
          initialValues={{ type: 'company', isActive: true }}
        >
          <Form.Item
            name="accountNumber"
            label="Số tài khoản"
            rules={[{ required: true, message: 'Vui lòng nhập số tài khoản' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="bankName"
            label="Ngân hàng"
            rules={[{ required: true, message: 'Vui lòng nhập tên ngân hàng' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="accountHolder"
            label="Chủ tài khoản"
            rules={[{ required: true, message: 'Vui lòng nhập tên chủ tài khoản' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="branch"
            label="Chi nhánh ngân hàng"
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="type"
            label="Loại tài khoản"
            rules={[{ required: true, message: 'Vui lòng chọn loại tài khoản' }]}
          >
            <Select>
              <Option value="company">Công ty</Option>
              <Option value="personal">Cá nhân</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="note"
            label="Ghi chú"
          >
            <TextArea rows={4} />
          </Form.Item>

          <Form.Item
            name="isActive"
            label="Trạng thái"
            valuePropName="checked"
          >
            <Switch checkedChildren="Hoạt động" unCheckedChildren="Không hoạt động" defaultChecked />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ReceiptForm;
