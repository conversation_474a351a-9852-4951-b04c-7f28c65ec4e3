import api from './api';

const SupplierService = {
  getAllSuppliers: async () => {
    return api.get('/suppliers');
  },

  getSupplierById: async (id) => {
    return api.get(`/suppliers/${id}`);
  },

  createSupplier: async (supplierData) => {
    return api.post('/suppliers', supplierData);
  },

  updateSupplier: async (id, supplierData) => {
    return api.put(`/suppliers/${id}`, supplierData);
  },

  deleteSupplier: async (id) => {
    return api.delete(`/suppliers/${id}`);
  }
};

export default SupplierService;
