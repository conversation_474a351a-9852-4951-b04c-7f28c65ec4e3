import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Card, Select, InputNumber, Typography, Divider, Table, Space, Modal, Switch } from 'antd';
import { Toaster, toast } from 'react-hot-toast';
import { PlusOutlined, MinusCircleOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';
import ReturnService from '../../services/return.service';
import CustomerService from '../../services/customer.service';
import ProductService from '../../services/product.service';
import InvoiceService from '../../services/invoice.service';
import InvoiceReturnService from '../../services/invoiceReturn.service';
import BranchService from '../../services/branch.service';
import UserService from '../../services/user.service';
import ProductCategoryService from '../../services/productCategory.service';
import BrandService from '../../services/brand.service';
import { formatCurrencyWithSymbol } from '../../utils/format';

const { Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const ReturnForm = () => {
  const [form] = Form.useForm();
  const [productForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [customers, setCustomers] = useState([]);
  const [products, setProducts] = useState([]);
  const [invoices, setInvoices] = useState([]);
  const [branches, setBranches] = useState([]);
  const [users, setUsers] = useState([]);
  const [categories, setCategories] = useState({ roots: [], all: [], childrenMap: {} });
  const [brands, setBrands] = useState([]);
  const [selectedItems, setSelectedItems] = useState([]);
  const [totalAmount, setTotalAmount] = useState(0);
  const [processedItems, setProcessedItems] = useState(new Set());
  const [productModalVisible, setProductModalVisible] = useState(false);
  const [selectedInvoiceId, setSelectedInvoiceId] = useState(null);
  const navigate = useNavigate();
  const { id } = useParams();

  useEffect(() => {
    // Tải dữ liệu cơ bản trước
    const loadBasicData = async () => {
      try {
        // Tải dữ liệu sản phẩm trước tiên vì nó quan trọng nhất
        await fetchProducts();

        // Tải các dữ liệu khác
        await Promise.all([
          fetchCustomers(),
          fetchInvoices(),
          fetchBranches(),
          fetchUsers(),
          fetchCategories(),
          fetchBrands()
        ]);

        // Sau khi tải xong dữ liệu cơ bản, mới tải dữ liệu phiếu trả hàng
        if (id) {
          setIsEdit(true);
          await fetchReturnData();
        }
      } catch (error) {
        console.error('Lỗi khi tải dữ liệu:', error);
      }
    };

    loadBasicData();
  }, [id]);

  const fetchCategories = async () => {
    try {
      const response = await ProductCategoryService.getAllCategories();
      const categoriesData = response.data;

      // Chỉ lấy các nhóm cha (không có parentId)
      const rootCategories = categoriesData.filter(cat => !cat.parentId)
        .sort((a, b) => a.name.localeCompare(b.name));

      // Tạo map để tra cứu nhanh các nhóm con
      const childrenMap = {};
      rootCategories.forEach(parent => {
        childrenMap[parent.id] = categoriesData
          .filter(cat => cat.parentId === parent.id)
          .sort((a, b) => a.name.localeCompare(b.name));
      });

      // Lưu toàn bộ dữ liệu để sử dụng sau này
      setCategories({
        roots: rootCategories,
        all: categoriesData,
        childrenMap: childrenMap
      });
    } catch (error) {
      toast.error('Không thể tải danh sách nhóm hàng: ' + error.message);
    }
  };

  const fetchBrands = async () => {
    try {
      const response = await BrandService.getAllBrands();
      setBrands(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách thương hiệu: ' + error.message);
    }
  };

  useEffect(() => {
    calculateTotal();
  }, [selectedItems]);

  const fetchCustomers = async () => {
    try {
      const response = await CustomerService.getAllCustomers();
      setCustomers(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách khách hàng: ' + error.message);
    }
  };

  const fetchProducts = async () => {
    try {
      const response = await ProductService.getAllProducts();
      setProducts(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách sản phẩm: ' + error.message);
    }
  };

  const fetchInvoices = async () => {
    try {
      const response = await InvoiceService.getAllInvoices();
      setInvoices(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách hóa đơn: ' + error.message);
    }
  };

  // Hàm xử lý khi chọn hóa đơn
  const handleInvoiceChange = async (invoiceId) => {
    // Cập nhật state để theo dõi việc chọn hóa đơn
    setSelectedInvoiceId(invoiceId);
    
    if (!invoiceId) {
      // Nếu không chọn hóa đơn, cho phép chọn khách hàng và xóa danh sách sản phẩm
      form.setFieldsValue({ customerId: undefined });
      setSelectedItems([]);
      return;
    }

    try {
      // Lấy chi tiết hóa đơn từ API
      const response = await InvoiceService.getInvoiceById(invoiceId);
      const invoiceDetail = response.data;
      
      // Cập nhật khách hàng tương ứng với hóa đơn
      if (invoiceDetail.customerId || invoiceDetail.Customer?.id) {
        form.setFieldsValue({ customerId: invoiceDetail.customerId || invoiceDetail.Customer?.id });
      }
      
      // Kiểm tra xem hóa đơn có chi tiết sản phẩm không
      const invoiceItems = invoiceDetail.InvoiceItems || [];
      
      if (invoiceItems.length > 0) {
        // Tạo danh sách sản phẩm từ hóa đơn
        const items = invoiceItems.map(item => {
          // Lấy thông tin sản phẩm từ item hoặc tìm trong danh sách sản phẩm
          const product = item.Product || products.find(p => p.id === item.productId);
          
          if (!product) {
            console.error('Không tìm thấy thông tin sản phẩm:', item.productId);
            return null;
          }
          
          return {
            productId: item.productId,
            quantity: 1, // Mặc định số lượng trả là 1
            maxQuantity: item.quantity, // Lưu số lượng tối đa có thể trả
            price: item.price || product.sellingPrice || product.price || 0,
            amount: item.price || product.sellingPrice || product.price || 0,
            reason: '',
            product: product,
            invoiceItemId: item.id // Lưu ID của chi tiết hóa đơn để tham chiếu
          };
        }).filter(item => item !== null); // Loại bỏ các item null
        
        // Cập nhật danh sách sản phẩm đã chọn
        setSelectedItems(items);
        toast.success(`Đã tải ${items.length} sản phẩm từ hóa đơn`);
      } else {
        toast.error('Hóa đơn này không có sản phẩm nào');
      }
    } catch (error) {
      console.error('Lỗi khi lấy chi tiết hóa đơn:', error);
      toast.error('Không thể lấy thông tin chi tiết hóa đơn');
    }
  };

  const fetchBranches = async () => {
    try {
      const response = await BranchService.getAllBranches();
      setBranches(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách chi nhánh: ' + error.message);
    }
  };

  const fetchUsers = async () => {
    try {
      const response = await UserService.getAllUsers();
      setUsers(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách người dùng: ' + error.message);
    }
  };

  const fetchReturnData = async () => {
    setLoading(true);
    try {
      const response = await ReturnService.getReturnById(id);
      const returnData = response.data;

      form.setFieldsValue({
        returnCode: returnData.returnCode,
        customerId: returnData.customerId,
        invoiceId: returnData.invoiceId,
        status: returnData.status,
        branchId: returnData.branchId,
        userId: returnData.userId,
        note: returnData.note,
      });

      // Xử lý danh sách sản phẩm
      let itemsData = [];

      // Kiểm tra nếu có ReturnItems
      if (returnData.ReturnItems && returnData.ReturnItems.length > 0) {
        itemsData = returnData.ReturnItems.map(item => ({
          id: item.id,
          productId: item.productId,
          quantity: item.quantity,
          price: item.price,
          amount: item.amount,
          reason: item.reason || '',
          product: item.Product // Lưu thông tin sản phẩm đầy đủ
        }));
      }
      // Nếu không có ReturnItems, thử items
      else if (returnData.items && returnData.items.length > 0) {
        itemsData = returnData.items;
      }

      setSelectedItems(itemsData);
    } catch (error) {
      console.error('Lỗi khi tải dữ liệu:', error);
      toast.error('Không thể tải thông tin phiếu trả hàng: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleAddItem = () => {
    setSelectedItems([...selectedItems, { productId: null, quantity: 1, price: 0, amount: 0, reason: '' }]);
  };

  const showAddProductModal = () => {
    productForm.resetFields();
    setProductModalVisible(true);
  };

  const handleAddProductModalCancel = () => {
    setProductModalVisible(false);
  };

  const handleAddProductModalOk = async () => {
    try {
      const values = await productForm.validateFields();

      // Chuyển đổi tên trường để phù hợp với backend
      const productData = {
        ...values,
        productCode: values.code,
        sellingPrice: values.price,
      };
      delete productData.code;
      delete productData.price;

      setLoading(true);

      // Gọi API để tạo sản phẩm mới
      const response = await ProductService.createProduct(productData);
      const newProduct = response.data;

      toast.success('Thêm sản phẩm mới thành công');

      // Cập nhật danh sách sản phẩm
      setProducts([...products, newProduct]);

      // Thêm sản phẩm mới vào danh sách sản phẩm đã chọn
      const newItem = {
        productId: newProduct.id,
        quantity: 1,
        price: newProduct.sellingPrice || newProduct.price || 0,
        amount: newProduct.sellingPrice || newProduct.price || 0,
        reason: '',
        product: newProduct
      };

      setSelectedItems([...selectedItems, newItem]);

      setProductModalVisible(false);
    } catch (error) {
      if (error.errorFields) {
        toast.error('Vui lòng điền đầy đủ thông tin sản phẩm');
      } else {
        toast.error('Lỗi khi thêm sản phẩm: ' + error.message);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveItem = (index) => {
    const itemToRemove = selectedItems[index];
    const newItems = [...selectedItems];
    newItems.splice(index, 1);

    // Chỉ ghi log, không cập nhật tồn kho khi xóa sản phẩm
    if (isEdit && form.getFieldValue('status') === 'completed' && itemToRemove.productId) {
      console.log('Đã xóa sản phẩm khỏi phiếu trả hàng đã hoàn thành, sẽ cập nhật tồn kho khi lưu phiếu:', itemToRemove);
    }

    setSelectedItems(newItems);
  };

  const handleProductChange = (value, index) => {
    const product = products.find(p => p.id === value);
    if (!product) {
      console.error('Không tìm thấy sản phẩm với ID:', value);
      return;
    }

    // Lấy giá từ sản phẩm, kiểm tra tất cả các trường có thể chứa giá
    let productPrice = 0;

    // Kiểm tra tất cả các trường có thể chứa giá
    if (product.sellingPrice !== undefined && product.sellingPrice !== null) {
      productPrice = product.sellingPrice;
    } else if (product.price !== undefined && product.price !== null) {
      productPrice = product.price;
    } else if (product.retailPrice !== undefined && product.retailPrice !== null) {
      productPrice = product.retailPrice;
    } else if (product.salePrice !== undefined && product.salePrice !== null) {
      productPrice = product.salePrice;
    }

    const newItems = [...selectedItems];
    newItems[index] = {
      ...newItems[index],
      productId: value,
      price: productPrice,
      amount: productPrice * (newItems[index].quantity || 1),
      product: product // Lưu thông tin sản phẩm đầy đủ
    };
    setSelectedItems(newItems);
  };

  const handleQuantityChange = (value, index) => {
    const newItems = [...selectedItems];
    const currentItem = newItems[index];
    
    // Kiểm tra nếu sản phẩm có giới hạn số lượng trả (từ hóa đơn)
    if (currentItem.maxQuantity !== undefined) {
      // Đảm bảo số lượng trả không vượt quá số lượng đã bán
      if (value > currentItem.maxQuantity) {
        toast.error(`Số lượng trả không được vượt quá số lượng đã bán (${currentItem.maxQuantity})`);
        value = currentItem.maxQuantity;
      }
    }
    
    // Cập nhật số lượng và thành tiền
    newItems[index] = {
      ...currentItem,
      quantity: value,
      amount: (currentItem.price || 0) * value
    };
    
    setSelectedItems(newItems);
  };

  const handlePriceChange = (value, index) => {
    const newItems = [...selectedItems];
    newItems[index] = {
      ...newItems[index],
      price: value,
      amount: value * (newItems[index].quantity || 1)
    };
    setSelectedItems(newItems);
  };

  const handleReasonChange = (value, index) => {
    const newItems = [...selectedItems];
    newItems[index] = {
      ...newItems[index],
      reason: value
    };
    setSelectedItems(newItems);
  };

  const calculateTotal = () => {
    const total = selectedItems.reduce((sum, item) => sum + (item.amount || 0), 0);
    setTotalAmount(total);
    form.setFieldsValue({ totalAmount: total });
  };

  // Hàm cập nhật tồn kho sản phẩm khi trả hàng
  const updateProductStock = async (productId, quantity, isCompleted, isRemoved = false) => {
    // Tạo key để theo dõi sản phẩm đã xử lý
    const processedItemKey = `${productId}-${isRemoved ? 'removed' : 'updated'}`;

    // Kiểm tra nếu sản phẩm đã được xử lý, bỏ qua
    if (processedItems.has(processedItemKey)) {
      return;
    }

    try {
      // Lấy thông tin sản phẩm hiện tại từ backend
      const response = await ProductService.getProductById(productId);
      const product = response.data;

      // Lấy số lượng tồn kho hiện tại
      const currentStock = product.stock || 0;

      // Tính toán số lượng tồn kho mới
      let newStock;

      if (isRemoved && isCompleted) {
        // Khi xóa sản phẩm đã trả hàng hoàn thành, giảm tồn kho lại
        // quantity đã là số dương (đã lấy abs ở hàm gọi)
        newStock = currentStock - quantity;
      } else if (isCompleted) {
        // Khi trả hàng hoàn thành, tăng tồn kho
        newStock = currentStock + quantity;
      } else {
        // Khi hủy trả hàng đã hoàn thành, giảm tồn kho
        // Đảm bảo tồn kho không âm
        newStock = Math.max(0, currentStock - quantity);
      }

      // Cập nhật tồn kho sản phẩm
      await ProductService.updateProduct(productId, {
        ...product,
        stock: newStock
      });

      // Đánh dấu sản phẩm đã được xử lý
      const newProcessedItems = new Set(processedItems);
      newProcessedItems.add(processedItemKey);
      setProcessedItems(newProcessedItems);

    } catch (error) {
      console.error(`Lỗi cập nhật tồn kho sản phẩm ${productId}:`, error);
      // Không hiển thị lỗi cho người dùng để tránh gián đoạn quy trình chính
    }
  };

  // Hàm so sánh danh sách sản phẩm cũ và mới để xác định sự thay đổi
  const compareItems = (oldItems, newItems, oldStatus, newStatus) => {
    // Đảm bảo oldItems và newItems là mảng
    oldItems = Array.isArray(oldItems) ? oldItems : [];
    newItems = Array.isArray(newItems) ? newItems : [];


    // Trường hợp đặc biệt: khi chuyển từ pending sang completed
    if (oldStatus === 'pending' && newStatus === 'completed') {

      // Khi chuyển từ pending sang completed, thêm tất cả sản phẩm vào danh sách cập nhật
      const allItems = newItems.map(item => ({
        productId: item.productId,
        quantity: item.quantity,
        isNew: false,
        isRemoved: false
      }));

      return allItems;
    }

    // Tạo map để lưu thông tin sản phẩm cũ theo productId
    const oldItemsMap = {};
    oldItems.forEach(item => {
      if (item && item.productId) {
        oldItemsMap[item.productId] = item;
      }
    });

    // Tạo map để lưu thông tin sản phẩm mới theo productId
    const newItemsMap = {};
    newItems.forEach(item => {
      if (item && item.productId) {
        newItemsMap[item.productId] = item;
      }
    });

    // Mảng lưu các sản phẩm cần cập nhật tồn kho
    const itemsToUpdate = [];

    // Kiểm tra các sản phẩm mới hoặc số lượng thay đổi
    newItems.forEach(newItem => {
      // Chỉ xử lý các sản phẩm có productId
      if (!newItem || !newItem.productId) return;

      const oldItem = oldItemsMap[newItem.productId];

      // Nếu sản phẩm không tồn tại trong danh sách cũ hoặc số lượng thay đổi
      if (!oldItem || oldItem.quantity !== newItem.quantity) {
        itemsToUpdate.push({
          productId: newItem.productId,
          // Nếu sản phẩm đã tồn tại, chỉ cập nhật phần chênh lệch số lượng
          quantity: oldItem ? newItem.quantity - oldItem.quantity : newItem.quantity,
          isNew: !oldItem,
          isRemoved: false
        });
      }
    });

    // Kiểm tra các sản phẩm đã bị xóa khỏi phiếu trả hàng
    oldItems.forEach(oldItem => {
      // Chỉ xử lý các sản phẩm có productId
      if (!oldItem || !oldItem.productId) return;

      // Nếu sản phẩm không còn trong danh sách mới
      if (!newItemsMap[oldItem.productId]) {
        itemsToUpdate.push({
          productId: oldItem.productId,
          // Khi xóa sản phẩm, cần giảm số lượng từ kho (số lượng âm để cập nhật ngược lại)
          quantity: -oldItem.quantity,
          isNew: false,
          isRemoved: true
        });
      }
    });

    return itemsToUpdate;
  };

  // Hàm cập nhật tồn kho cho tất cả sản phẩm trong phiếu trả hàng
  const updateAllProductsStock = async (items, oldStatus, newStatus) => {
    // Chỉ cập nhật khi trạng thái thay đổi từ pending sang completed hoặc ngược lại
    const isCompletedNow = newStatus === 'completed';
    const wasCompletedBefore = oldStatus === 'completed';

    // Nếu trạng thái đã hoàn thành từ trước và vẫn hoàn thành
    if (isCompletedNow && wasCompletedBefore) {
      for (const item of items) {
        // Xử lý các sản phẩm mới thêm vào
        if (item.isNew && !item.isRemoved) {
          // Khi thêm sản phẩm mới vào phiếu đã hoàn thành, tăng tồn kho
          await updateProductStock(item.productId, item.quantity, true, false);
        }
        // Xử lý các sản phẩm bị xóa
        else if (item.isRemoved) {

          await updateProductStock(item.productId, Math.abs(item.quantity), true, true);
        }
        // Xử lý các sản phẩm thay đổi số lượng
        else if (item.quantity !== 0) {
          // Khi thay đổi số lượng, cập nhật theo chênh lệch
          await updateProductStock(item.productId, item.quantity, true, false);
        }
      }
      return;
    }

    // Trường hợp đặc biệt: chuyển từ pending sang completed
    if (!wasCompletedBefore && isCompletedNow) {
      for (const item of items) {
        // Tăng tồn kho cho tất cả sản phẩm khi chuyển sang hoàn thành
        await updateProductStock(item.productId, item.quantity, true, false);
      }
      return;
    }

    // Trường hợp đặc biệt: chuyển từ completed sang pending/cancelled
    if (wasCompletedBefore && !isCompletedNow) {

      try {
        for (const item of items) {

          // Lấy thông tin sản phẩm hiện tại từ backend
          const response = await ProductService.getProductById(item.productId);
          const product = response.data;

          // Lấy số lượng tồn kho hiện tại
          const currentStock = product.stock || 0;

          // Khi hủy phiếu trả hàng đã hoàn thành, phải giảm tồn kho vì sản phẩm không còn được trả lại
          // Lưu ý: với trường hợp này, không sử dụng hàm updateProductStock mà xử lý trực tiếp
          // để đảm bảo tồn kho được giảm đúng số lượng
          const actualQuantity = Math.abs(item.quantity);
          const newStock = Math.max(0, currentStock - actualQuantity);

          // Cập nhật tồn kho sản phẩm
          await ProductService.updateProduct(item.productId, {
            ...product,
            stock: newStock
          });
        }
      } catch (error) {
        console.error('Lỗi khi cập nhật tồn kho:', error);
      }

      return;
    }

  };

  // Hàm cập nhật danh sách sản phẩm và xử lý các sản phẩm đã xóa trong phiếu trả
  const processRemovedItems = (oldItems, newItems) => {
    // Tìm các sản phẩm đã bị xóa khỏi danh sách
    const oldItemsMap = {};
    oldItems.forEach(item => {
      if (item && item.productId) {
        oldItemsMap[item.productId] = item;
      }
    });

    const newItemsMap = {};
    newItems.forEach(item => {
      if (item && item.productId) {
        newItemsMap[item.productId] = item;
      }
    });

    // Tìm các sản phẩm bị xóa (có trong oldItems nhưng không có trong newItems)
    const removedItems = [];
    Object.keys(oldItemsMap).forEach(productId => {
      if (!newItemsMap[productId]) {
        removedItems.push({
          productId: productId,
          quantity: oldItemsMap[productId].quantity,
          isRemoved: true
        });
      }
    });

    return removedItems;
  };

  const onFinish = async (values) => {
    if (selectedItems.length === 0) {
      toast.error('Vui lòng thêm ít nhất một sản phẩm vào phiếu trả hàng');
      return;
    }

    // Kiểm tra xem các sản phẩm đã được chọn đầy đủ chưa
    const invalidItems = selectedItems.filter(item => !item.productId);
    if (invalidItems.length > 0) {
      toast.error('Vui lòng chọn sản phẩm cho tất cả các dòng');
      return;
    }

    const returnData = {
      ...values,
      items: selectedItems.map(item => ({
        productId: item.productId,
        quantity: Number(item.quantity) || 0,
        price: Number(item.price) || 0,
        amount: Number(item.amount) || 0,
        reason: item.reason || ''
      })),
      totalAmount: Number(totalAmount) || 0,
      // Đảm bảo các trường bắt buộc có giá trị
      status: values.status || 'pending'
    };

    // Khi tạo mới, không gửi returnCode để backend tự động tạo
    if (!isEdit) {
      delete returnData.returnCode;
    }

    setLoading(true);
    try {
      // Reset danh sách sản phẩm đã xử lý khi bắt đầu lưu phiếu
      setProcessedItems(new Set());

      if (isEdit) {
        // Lấy trạng thái cũ của phiếu trả hàng để so sánh
        const oldReturnResponse = await ReturnService.getReturnById(id);
        const oldStatus = oldReturnResponse.data.status;
        const newStatus = values.status;

        // Lấy danh sách sản phẩm cũ an toàn
        let oldItems = [];
        if (oldReturnResponse.data.ReturnItems && oldReturnResponse.data.ReturnItems.length > 0) {
          oldItems = oldReturnResponse.data.ReturnItems;
        } else if (oldReturnResponse.data.items && oldReturnResponse.data.items.length > 0) {
          oldItems = oldReturnResponse.data.items;
        }

        // Trường hợp đặc biệt: Chuyển từ completed sang pending/cancelled
        if (oldStatus === 'completed' && newStatus !== 'completed') {

          // Cập nhật phiếu trả hàng trước
          await ReturnService.updateReturn(id, returnData);

          // Sau khi cập nhật, xử lý tồn kho cho từng sản phẩm
          for (const item of oldItems) {
            try {
              // Lấy thông tin sản phẩm
              const productId = item.productId;
              const quantity = item.quantity;

              // Lấy thông tin sản phẩm hiện tại
              const productResponse = await ProductService.getProductById(productId);
              const product = productResponse.data;

              // Tính toán tồn kho mới
              const currentStock = product.stock || 0;
              const newStock = Math.max(0, currentStock - quantity);


              // Cập nhật tồn kho
              await ProductService.updateProduct(productId, {
                ...product,
                stock: newStock
              });

            } catch (error) {
              console.error(`Lỗi cập nhật tồn kho sản phẩm:`, error);
            }
          }

          toast.success('Cập nhật phiếu trả hàng và tồn kho thành công');
        }
        // Trường hợp đặc biệt: Chuyển từ pending/cancelled sang completed
        else if (oldStatus !== 'completed' && newStatus === 'completed') {
          console.log('Data being sent to update return:', returnData);
          await ReturnService.updateReturn(id, returnData);

          // Sau khi cập nhật, xử lý tồn kho cho từng sản phẩm
          for (const item of returnData.items) {
            try {
              // Lấy thông tin sản phẩm
              const productId = item.productId;
              const quantity = item.quantity;

              // Lấy thông tin sản phẩm hiện tại
              const productResponse = await ProductService.getProductById(productId);
              const product = productResponse.data;

              // Tính toán tồn kho mới
              const currentStock = product.stock || 0;
              const newStock = currentStock + quantity;


              // Cập nhật tồn kho
              await ProductService.updateProduct(productId, {
                ...product,
                stock: newStock
              });

            } catch (error) {
              console.error(`Lỗi cập nhật tồn kho sản phẩm:`, error);
            }
          }

          // Cập nhật trạng thái trả hàng của hóa đơn
          if (returnData.invoiceId) {
            try {
              await InvoiceReturnService.updateInvoiceReturnStatus(returnData.invoiceId);
              console.log('Đã cập nhật trạng thái trả hàng của hóa đơn:', returnData.invoiceId);
            } catch (error) {
              console.error('Lỗi khi cập nhật trạng thái trả hàng của hóa đơn:', error);
            }
          }

          toast.success('Cập nhật phiếu trả hàng và tồn kho thành công');
        }
        // Trường hợp đặc biệt: Vẫn giữ trạng thái completed nhưng thay đổi sản phẩm
        else if (oldStatus === 'completed' && newStatus === 'completed') {

          // Tìm các sản phẩm đã bị xóa
          const removedItems = processRemovedItems(oldItems, returnData.items);

          // Cập nhật phiếu trả hàng
          await ReturnService.updateReturn(id, returnData);

          // Cập nhật tồn kho cho các sản phẩm đã xóa
          for (const item of removedItems) {
            try {
              // Lấy thông tin sản phẩm hiện tại từ backend
              const productId = item.productId;
              const quantity = item.quantity;

              const productResponse = await ProductService.getProductById(productId);
              const product = productResponse.data;

              // Lấy số lượng tồn kho hiện tại
              const currentStock = product.stock || 0;

              // Giảm tồn kho
              const newStock = Math.max(0, currentStock - quantity);


              // Cập nhật tồn kho sản phẩm
              await ProductService.updateProduct(productId, {
                ...product,
                stock: newStock
              });
            } catch (error) {
              console.error(`Lỗi cập nhật tồn kho sản phẩm ${item.productId}:`, error);
            }
          }

          // So sánh để tìm các sản phẩm mới hoặc thay đổi số lượng
          const itemsToUpdate = compareItems(oldItems, returnData.items, oldStatus, newStatus);

          // Cập nhật tồn kho cho các sản phẩm mới hoặc thay đổi số lượng
          for (const item of itemsToUpdate) {
            if (!item.isRemoved) {
              try {
                // Lấy thông tin sản phẩm
                const productId = item.productId;
                const quantity = item.quantity;

                // Lấy thông tin sản phẩm hiện tại
                const productResponse = await ProductService.getProductById(productId);
                const product = productResponse.data;

                // Tính toán tồn kho mới (tăng thêm số lượng chênh lệch)
                const currentStock = product.stock || 0;
                const newStock = currentStock + quantity;


                // Cập nhật tồn kho
                await ProductService.updateProduct(productId, {
                  ...product,
                  stock: newStock
                });
              } catch (error) {
                console.error(`Lỗi cập nhật tồn kho sản phẩm:`, error);
              }
            }
          }

          // Cập nhật trạng thái trả hàng của hóa đơn
          if (returnData.invoiceId) {
            try {
              await InvoiceReturnService.updateInvoiceReturnStatus(returnData.invoiceId);
              console.log('Đã cập nhật trạng thái trả hàng của hóa đơn:', returnData.invoiceId);
            } catch (error) {
              console.error('Lỗi khi cập nhật trạng thái trả hàng của hóa đơn:', error);
            }
          }

          toast.success('Cập nhật phiếu trả hàng và tồn kho thành công');
        }
        // Trường hợp thông thường
        else {
          // Chỉ cập nhật phiếu trả hàng, không làm gì với tồn kho
          await ReturnService.updateReturn(id, returnData);

          // Cập nhật trạng thái trả hàng của hóa đơn
          if (returnData.invoiceId) {
            try {
              await InvoiceReturnService.updateInvoiceReturnStatus(returnData.invoiceId);
              console.log('Đã cập nhật trạng thái trả hàng của hóa đơn:', returnData.invoiceId);
            } catch (error) {
              console.error('Lỗi khi cập nhật trạng thái trả hàng của hóa đơn:', error);
            }
          }

          toast.success('Cập nhật phiếu trả hàng thành công');
        }
      } else {
        // Tạo phiếu trả hàng mới
        const response = await ReturnService.createReturn(returnData);

        // Nếu phiếu trả hàng mới có trạng thái là completed, cập nhật tồn kho
        if (values.status === 'completed') {
          // Đảm bảo có đủ thông tin để cập nhật tồn kho

          for (const item of returnData.items) {
            try {
              // Lấy thông tin sản phẩm
              const productId = item.productId;
              const quantity = item.quantity;

              // Lấy thông tin sản phẩm hiện tại
              const productResponse = await ProductService.getProductById(productId);
              const product = productResponse.data;

              // Tính toán tồn kho mới
              const currentStock = product.stock || 0;
              const newStock = currentStock + quantity;


              // Cập nhật tồn kho
              await ProductService.updateProduct(productId, {
                ...product,
                stock: newStock
              });
            } catch (error) {
              console.error(`Lỗi cập nhật tồn kho sản phẩm:`, error);
            }
          }

          // Cập nhật trạng thái trả hàng của hóa đơn
          if (returnData.invoiceId) {
            try {
              await InvoiceReturnService.updateInvoiceReturnStatus(returnData.invoiceId);
              console.log('Đã cập nhật trạng thái trả hàng của hóa đơn:', returnData.invoiceId);
            } catch (error) {
              console.error('Lỗi khi cập nhật trạng thái trả hàng của hóa đơn:', error);
            }
          }
        }

        toast.success('Tạo phiếu trả hàng thành công');
      }
      navigate('/returns');
    } catch (error) {
      console.error('Lỗi chi tiết:', error);
      console.error('Error response:', error.response?.data);
      toast.error('Lỗi: ' + (error.response?.data?.message || error.message));
    } finally {
      setLoading(false);
    }
  };

  const columns = [
    {
      title: 'Sản phẩm',
      dataIndex: 'productId',
      key: 'productId',
      render: (value, record, index) => {
        // Kiểm tra xem có thông tin sản phẩm không
        const productName = record.product?.name ||
          record.Product?.name ||
          (value && products.find(p => p.id === value)?.name);


        return (
          <Select
            style={{ width: '100%' }}
            placeholder="Chọn sản phẩm"
            value={value}
            onChange={(val) => handleProductChange(val, index)}
          >
            {products.map(product => (
              <Option key={product.id} value={product.id}>{product.name}</Option>
            ))}
          </Select>
        );
      },
    },
    {
      title: 'Số lượng',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 120,
      render: (value, _, index) => (
        <InputNumber
          min={1}
          value={value}
          onChange={(val) => handleQuantityChange(val, index)}
          style={{ width: '100%' }}
        />
      ),
    },
    {
      title: 'Đơn giá',
      dataIndex: 'price',
      key: 'price',
      width: 150,
      render: (value, _, index) => (
        <InputNumber
          value={value}
          onChange={(val) => handlePriceChange(val, index)}
          style={{ width: '100%' }}
          formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
          parser={value => value.replace(/\$\s?|(,*)/g, '')}
          min={0}
        />
      ),
    },
    {
      title: 'Thành tiền',
      dataIndex: 'amount',
      key: 'amount',
      width: 150,
      render: (amount) => formatCurrencyWithSymbol(amount),
    },
    {
      title: 'Lý do trả hàng',
      dataIndex: 'reason',
      key: 'reason',
      render: (value, _, index) => (
        <Input
          value={value}
          onChange={(e) => handleReasonChange(e.target.value, index)}
          placeholder="Nhập lý do trả hàng"
        />
      ),
    },
    {
      title: '',
      key: 'action',
      width: 80,
      render: (_, __, index) => (
        <Button
          type="text"
          danger
          icon={<MinusCircleOutlined />}
          onClick={() => handleRemoveItem(index)}
        />
      ),
    },
  ];

  return (
    <div>
      <Toaster position="top-right" />
      <Title level={2}>{isEdit ? 'Cập nhật phiếu trả hàng' : 'Tạo phiếu trả hàng mới'}</Title>
      <Divider />

      <Card>
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          initialValues={{ status: 'pending' }}
        >
          <div style={{ display: 'flex', gap: '16px' }}>
            <Form.Item
              name="returnCode"
              label="Mã phiếu trả hàng"
              tooltip={isEdit ? "" : "Mã phiếu trả hàng sẽ được tạo tự động (TH...)"}
              style={{ flex: 1 }}
            >
              <Input disabled={!isEdit} placeholder="Tự động tạo (TH...)" />
            </Form.Item>

            <Form.Item
              name="customerId"
              label="Khách hàng"
              rules={[{ required: true, message: 'Vui lòng chọn khách hàng' }]}
              style={{ flex: 1 }}
              tooltip="Khách hàng sẽ được tự động chọn khi bạn chọn hóa đơn liên quan"
            >
              <Select 
                placeholder="Chọn khách hàng"
                disabled={!!selectedInvoiceId}
              >
                {customers.map(customer => (
                  <Option key={customer.id} value={customer.id}>{customer.name}</Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="status"
              label="Trạng thái"
              rules={[{ required: true, message: 'Vui lòng chọn trạng thái' }]}
              style={{ flex: 1 }}
            >
              <Select>
                <Option value="pending">Chờ xử lý</Option>
                <Option value="completed">Đã hoàn thành</Option>
                <Option value="cancelled">Đã hủy</Option>
              </Select>
            </Form.Item>
          </div>

          <div style={{ display: 'flex', gap: '16px' }}>
            <Form.Item
              name="invoiceId"
              label="Hóa đơn liên quan"
              style={{ flex: 1 }}
            >
              <Select 
                placeholder="Chọn hóa đơn" 
                allowClear
                onChange={handleInvoiceChange}
              >
                {invoices.map(invoice => (
                  <Option key={invoice.id} value={invoice.id}>
                    {invoice.invoiceCode || invoice.code} - {invoice.customer?.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="branchId"
              label="Chi nhánh"
              style={{ flex: 1 }}
            >
              <Select placeholder="Chọn chi nhánh" allowClear>
                {branches.map(branch => (
                  <Option key={branch.id} value={branch.id}>{branch.name}</Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="userId"
              label="Người tạo"
              style={{ flex: 1 }}
            >
              <Select placeholder="Chọn người tạo" allowClear>
                {users.map(user => (
                  <Option key={user.id} value={user.id}>{user.fullName}</Option>
                ))}
              </Select>
            </Form.Item>
          </div>

          <Divider orientation="left">Chi tiết trả hàng</Divider>

          <div style={{ display: 'flex', gap: '8px', marginBottom: 16 }}>
            <Button
              type="dashed"
              onClick={handleAddItem}
              icon={<PlusOutlined />}
            >
              Chọn sản phẩm
            </Button>
            <Button
              type="primary"
              onClick={showAddProductModal}
              icon={<PlusCircleOutlined />}
            >
              Thêm sản phẩm mới
            </Button>
          </div>

          <Table
            columns={columns}
            dataSource={selectedItems}
            rowKey={(_, index) => index}
            pagination={false}
            bordered
          />

          <div style={{ textAlign: 'right', marginTop: 16 }}>
            <Title level={4}>Tổng tiền: {formatCurrencyWithSymbol(totalAmount)}</Title>
          </div>

          <Form.Item name="totalAmount" hidden>
            <InputNumber />
          </Form.Item>

          <Form.Item
            name="note"
            label="Ghi chú"
            style={{ marginTop: 16 }}
          >
            <TextArea rows={4} />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={loading}>
                {isEdit ? 'Cập nhật' : 'Tạo phiếu trả hàng'}
              </Button>
              <Button onClick={() => navigate('/returns')}>
                Hủy
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      {/* Modal thêm sản phẩm mới */}
      <Modal
        title="Thêm sản phẩm mới"
        open={productModalVisible}
        onOk={handleAddProductModalOk}
        onCancel={handleAddProductModalCancel}
        width={800}
        confirmLoading={loading}
      >
        <Form
          form={productForm}
          layout="vertical"
          initialValues={{ stock: 0, price: 0, isActive: true }}
        >
          <div style={{ display: 'flex', gap: '16px' }}>
            <Form.Item
              name="code"
              label="Mã sản phẩm"
              rules={[{ required: true, message: 'Vui lòng nhập mã sản phẩm' }]}
              style={{ flex: 1 }}
            >
              <Input />
            </Form.Item>

            <Form.Item
              name="barcode"
              label="Mã vạch"
              style={{ flex: 1 }}
            >
              <Input />
            </Form.Item>
          </div>

          <Form.Item
            name="name"
            label="Tên sản phẩm"
            rules={[{ required: true, message: 'Vui lòng nhập tên sản phẩm' }]}
          >
            <Input />
          </Form.Item>

          <div style={{ display: 'flex', gap: '16px' }}>
            <Form.Item
              name="categoryId"
              label="Nhóm hàng"
              rules={[{ required: true, message: 'Vui lòng chọn nhóm hàng' }]}
              style={{ flex: 1 }}
            >
              <Select
                placeholder="Chọn nhóm hàng"
                dropdownRender={menu => {
                  return categories.roots ? menu : <div style={{ padding: 8 }}>Đang tải...</div>;
                }}
              >
                {categories.roots && categories.roots.map(category => (
                  <Select.OptGroup key={category.id} label={<strong>{category.name}</strong>}>
                    {/* Thêm nhóm cha như một option */}
                    <Option key={category.id} value={category.id}>
                      <strong>{category.name}</strong>
                    </Option>

                    {/* Thêm các nhóm con */}
                    {categories.childrenMap[category.id] && categories.childrenMap[category.id].map(child => (
                      <Option key={child.id} value={child.id}>
                        <span style={{ paddingLeft: '20px' }}>↳ {child.name}</span>
                      </Option>
                    ))}
                  </Select.OptGroup>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="brandId"
              label="Thương hiệu"
              rules={[{ required: true, message: 'Vui lòng chọn thương hiệu' }]}
              style={{ flex: 1 }}
            >
              <Select placeholder="Chọn thương hiệu">
                {brands.map(brand => (
                  <Option key={brand.id} value={brand.id}>{brand.name}</Option>
                ))}
              </Select>
            </Form.Item>
          </div>

          <div style={{ display: 'flex', gap: '16px' }}>
            <Form.Item
              name="costPrice"
              label="Giá vốn"
              rules={[{ required: true, message: 'Vui lòng nhập giá vốn' }]}
              style={{ flex: 1 }}
            >
              <InputNumber
                style={{ width: '100%' }}
                formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, '.')}
                parser={value => value.replace(/\$\s?|(\.+)/g, '')}
                min={0}
              />
            </Form.Item>

            <Form.Item
              name="price"
              label="Giá bán"
              rules={[{ required: true, message: 'Vui lòng nhập giá bán' }]}
              style={{ flex: 1 }}
            >
              <InputNumber
                style={{ width: '100%' }}
                formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, '.')}
                parser={value => value.replace(/\$\s?|(\.+)/g, '')}
                min={0}
              />
            </Form.Item>

            <Form.Item
              name="stock"
              label="Tồn kho"
              rules={[{ required: true, message: 'Vui lòng nhập số lượng tồn kho' }]}
              style={{ flex: 1 }}
            >
              <InputNumber style={{ width: '100%' }} min={0} />
            </Form.Item>
          </div>

          <Form.Item
            name="description"
            label="Mô tả"
          >
            <TextArea rows={4} />
          </Form.Item>

          <Form.Item
            name="isActive"
            label="Trạng thái"
            valuePropName="checked"
          >
            <Switch checkedChildren="Hoạt động" unCheckedChildren="Không hoạt động" defaultChecked />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ReturnForm;
