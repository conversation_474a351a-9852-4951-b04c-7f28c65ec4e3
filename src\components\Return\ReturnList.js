import React, { useState, useEffect } from 'react';
import { Table, Button, Space, Popconfirm, Typography, Input, Card, Tag } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, SearchOutlined, EyeOutlined } from '@ant-design/icons';
import { Toaster, toast } from 'react-hot-toast';
import { Link } from 'react-router-dom';
import ReturnService from '../../services/return.service';
import ProductService from '../../services/product.service';
import { formatCurrencyWithSymbol } from '../../utils/format';

const { Title } = Typography;

const ReturnList = () => {
  const [returns, setReturns] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');

  useEffect(() => {
    fetchReturns();
  }, []);

  const fetchReturns = async () => {
    setLoading(true);
    try {
      const response = await ReturnService.getAllReturns();
      setReturns(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách trả hàng: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    try {
      // Trước khi xóa, lấy thông tin phiếu trả hàng để kiểm tra trạng thái và cập nhật tồn kho
      const returnResponse = await ReturnService.getReturnById(id);
      const returnData = returnResponse.data;
      
      // Nếu phiếu trả hàng đã hoàn thành, cần cập nhật tồn kho
      if (returnData.status === 'completed') {
        console.log('Phiếu trả hàng đã hoàn thành, cập nhật tồn kho trước khi xóa');
        
        // Lấy danh sách sản phẩm từ phiếu trả hàng
        const items = returnData.ReturnItems || returnData.items || [];
        
        if (items.length > 0) {
          // Cập nhật tồn kho cho từng sản phẩm
          for (const item of items) {
            try {
              // Lấy thông tin sản phẩm hiện tại từ backend
              const productId = item.productId || (item.Product && item.Product.id);
              if (!productId) continue;
              
              const productResponse = await ProductService.getProductById(productId);
              const product = productResponse.data;
              
              // Lấy số lượng tồn kho hiện tại
              const currentStock = product.stock || 0;
              const quantity = item.quantity || 0;
              
              // Khi xóa phiếu trả hàng đã hoàn thành, giảm tồn kho vì sản phẩm không còn được trả lại
              const newStock = Math.max(0, currentStock - quantity);
              
              console.log(`Xóa phiếu trả hàng đã hoàn thành, giảm tồn kho sản phẩm ${productId}: ${currentStock} -> ${newStock}`);
              
              // Cập nhật tồn kho sản phẩm
              await ProductService.updateProduct(productId, {
                ...product,
                stock: newStock
              });
            } catch (error) {
              console.error(`Lỗi cập nhật tồn kho sản phẩm:`, error);
            }
          }
          
          toast.success('Đã cập nhật tồn kho cho các sản phẩm');
        }
      }
      
      // Sau khi cập nhật tồn kho, tiến hành xóa phiếu trả hàng
      await ReturnService.deleteReturn(id);
      toast.success('Xóa phiếu trả hàng thành công');
      fetchReturns();
    } catch (error) {
      console.error('Lỗi chi tiết:', error);
      toast.error('Không thể xóa phiếu trả hàng: ' + (error.response?.data?.message || error.message));
    }
  };

  const filteredReturns = returns.filter(
    (returnItem) => {
      const searchLower = searchText.toLowerCase();
      return (
        returnItem.returnCode?.toLowerCase().includes(searchLower) ||
        // Tìm kiếm theo khách hàng (kiểm tra cả Customer và customer)
        (returnItem.Customer?.name?.toLowerCase().includes(searchLower) ||
          returnItem.Customer?.fullName?.toLowerCase().includes(searchLower) ||
          returnItem.customer?.name?.toLowerCase().includes(searchLower) ||
          returnItem.customer?.fullName?.toLowerCase().includes(searchLower)) ||
        // Tìm kiếm theo hóa đơn (kiểm tra cả Invoice và invoice)
        (returnItem.Invoice?.invoiceCode?.toLowerCase().includes(searchLower) ||
          returnItem.invoice?.invoiceCode?.toLowerCase().includes(searchLower))
      );
    }
  );

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'green';
      case 'pending':
        return 'orange';
      case 'cancelled':
        return 'red';
      default:
        return 'default';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'completed':
        return 'Đã hoàn thành';
      case 'pending':
        return 'Chờ xử lý';
      case 'cancelled':
        return 'Đã hủy';
      default:
        return status;
    }
  };

  const columns = [
    {
      title: <span style={{ fontSize: 16 }}>Mã trả hàng</span>,
      dataIndex: 'returnCode',
      key: 'returnCode',
    },
    {
      title: <span style={{ fontSize: 16 }}>Khách hàng</span>,
      dataIndex: ['Customer', 'name'],
      key: 'customer',
      render: (_, record) => {
        if (record.Customer) {
          return record.Customer.name || record.Customer.fullName || 'N/A';
        }
        if (record.customer) {
          return record.customer.name || record.customer.fullName || 'N/A';
        }
        return 'N/A';
      },
    },
    {
      title: <span style={{ fontSize: 16 }}>Hóa đơn</span>,
      dataIndex: ['Invoice', 'invoiceCode'],
      key: 'invoice',
      render: (_, record) => {
        if (record.Invoice) {
          return record.Invoice.invoiceCode || 'N/A';
        }
        if (record.invoice) {
          return record.invoice.invoiceCode || 'N/A';
        }
        return 'N/A';
      },
    },
    {
      title: <span style={{ fontSize: 16 }}>Ngày tạo</span>,
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => new Date(date).toLocaleDateString('vi-VN'),
      sorter: (a, b) => new Date(a.createdAt) - new Date(b.createdAt),
    },
    {
      title: <span style={{ fontSize: 16 }}>Tổng tiền</span>,
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      render: (amount) => formatCurrencyWithSymbol(amount),
      sorter: (a, b) => a.totalAmount - b.totalAmount,
    },
    {
      title: <span style={{ fontSize: 16 }}>Trạng thái</span>,
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: <span style={{ fontSize: 16 }}>Thao tác</span>,
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Link to={`/returns/view/${record.id}`}>
            <Button type="default" icon={<EyeOutlined />} size="small" style={{ height: 40, fontSize: 14, display: 'flex', alignItems: 'center', padding: '0 15px' }}>
              Xem
            </Button>
          </Link>
          <Link to={`/returns/edit/${record.id}`}>
            <Button type="primary" icon={<EditOutlined />} size="small" style={{ height: 40, fontSize: 14, display: 'flex', alignItems: 'center', padding: '0 15px' }}>
              Sửa
            </Button>
          </Link>
          <Popconfirm
            title="Bạn có chắc chắn muốn xóa phiếu trả hàng này?"
            onConfirm={() => handleDelete(record.id)}
            okText="Có"
            cancelText="Không"
          >
            <Button type="primary" danger icon={<DeleteOutlined />} size="small" style={{ height: 40, fontSize: 14, display: 'flex', alignItems: 'center', padding: '0 15px' }}>
              Xóa
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Toaster position="top-right" />
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 20 }}>
        <Title level={2} style={{ fontSize: 28, background: 'linear-gradient(90deg, #1890ff, #096dd9)', WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent' }}>Quản lý trả hàng</Title>
        <Link to="/returns/add">
          <Button type="primary" icon={<PlusOutlined />} style={{ height: 40, fontSize: 16, display: 'flex', alignItems: 'center' }}>
            Tạo phiếu trả hàng
          </Button>
        </Link>
      </div>

      <Card style={{ marginBottom: 16 }}>
        <Input
          placeholder="Tìm kiếm phiếu trả hàng..."
          prefix={<SearchOutlined />}
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          style={{ width: 300, height: 42, fontSize: 16 }}
        />
      </Card>

      <Table
        columns={columns}
        dataSource={filteredReturns}
        rowKey="id"
        loading={loading}
        pagination={{ pageSize: 10, style: { fontSize: 16 } }}
        style={{ fontSize: 16 }}
      />
    </div>
  );
};

export default ReturnList;
