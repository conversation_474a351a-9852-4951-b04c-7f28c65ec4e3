import api from './api';

const PaymentService = {
  getAllPayments: async () => {
    return api.get('/payments');
  },

  getPaymentById: async (id) => {
    return api.get(`/payments/${id}`);
  },

  createPayment: async (paymentData) => {
    return api.post('/payments', paymentData);
  },

  updatePayment: async (id, paymentData) => {
    return api.put(`/payments/${id}`, paymentData);
  },

  deletePayment: async (id) => {
    return api.delete(`/payments/${id}`);
  }
};

export default PaymentService;
