import api from './api';

const ReportService = {
  /**
   * L<PERSON>y top sản phẩm có doanh thu cao nhất
   * @param {Object} options - <PERSON><PERSON><PERSON> tùy chọn báo cáo
   * @param {Date} options.startDate - <PERSON><PERSON><PERSON> bắt đầu
   * @param {Date} options.endDate - <PERSON><PERSON><PERSON> kết thúc
   * @param {Number} options.limit - Số lượng sản phẩm hiển thị
   * @param {Number} options.branchId - ID chi nhánh (nếu có)
   * @returns {Promise<Object>} Kết quả báo cáo
   */
  getTopSellingProducts: async (options = {}) => {
    try {
      // Xây dựng query params
      const params = new URLSearchParams();
      if (options.startDate) params.append('startDate', options.startDate);
      if (options.endDate) params.append('endDate', options.endDate);
      if (options.limit) params.append('limit', options.limit);
      if (options.branchId) params.append('branchId', options.branchId);

      return await api.get(`/reports/top-selling-products?${params.toString()}`);
    } catch (error) {
      console.error('Error fetching top selling products report:', error);
      throw error;
    }
  },

  /**
   * Lấy doanh thu cuối ngày
   * @param {Object} options - Các tùy chọn báo cáo
   * @param {Date} options.date - Ngày cần báo cáo
   * @param {Number} options.branchId - ID chi nhánh (nếu có)
   * @returns {Promise<Object>} Kết quả báo cáo
   */
  getDailyRevenue: async (options = {}) => {
    try {
      // Xây dựng query params
      const params = new URLSearchParams();
      if (options.date) params.append('date', options.date);
      if (options.branchId) params.append('branchId', options.branchId);

      return await api.get(`/reports/daily-revenue?${params.toString()}`);
    } catch (error) {
      console.error('Error fetching daily revenue report:', error);
      throw error;
    }
  },

  /**
   * Lấy doanh thu thuần
   * @param {Object} options - Các tùy chọn báo cáo
   * @param {Date} options.startDate - Ngày bắt đầu
   * @param {Date} options.endDate - Ngày kết thúc
   * @param {Number} options.branchId - ID chi nhánh (nếu có)
   * @returns {Promise<Object>} Kết quả báo cáo
   */
  getNetRevenue: async (options = {}) => {
    try {
      // Xây dựng query params
      const params = new URLSearchParams();
      if (options.startDate) params.append('startDate', options.startDate);
      if (options.endDate) params.append('endDate', options.endDate);
      if (options.branchId) params.append('branchId', options.branchId);

      return await api.get(`/reports/net-revenue?${params.toString()}`);
    } catch (error) {
      console.error('Error fetching net revenue report:', error);
      throw error;
    }
  },

  /**
   * Lấy doanh thu theo tháng
   * @param {Object} options - Các tùy chọn báo cáo
   * @param {Number} options.year - Năm cần báo cáo
   * @param {Number} options.branchId - ID chi nhánh (nếu có)
   * @returns {Promise<Object>} Kết quả báo cáo
   */
  getMonthlyRevenue: async (options = {}) => {
    try {
      // Xây dựng query params
      const params = new URLSearchParams();
      if (options.year) params.append('year', options.year);
      if (options.branchId) params.append('branchId', options.branchId);

      return await api.get(`/reports/monthly-revenue?${params.toString()}`);
    } catch (error) {
      console.error('Error fetching monthly revenue report:', error);
      throw error;
    }
  },
  /**
   * Lấy danh sách hóa đơn
   * @param {Object} options - Các tùy chọn báo cáo
   * @param {Date} options.startDate - Ngày bắt đầu
   * @param {Date} options.endDate - Ngày kết thúc
   * @param {Number} options.branchId - ID chi nhánh (nếu có)
   * @param {Number} options.customerId - ID khách hàng (nếu có)
   * @returns {Promise<Object>} Kết quả báo cáo
   */
  getInvoices: async (options = {}) => {
    try {
      // Xây dựng query params
      const params = new URLSearchParams();
      if (options.startDate) params.append('startDate', options.startDate);
      if (options.endDate) params.append('endDate', options.endDate);
      if (options.branchId) params.append('branchId', options.branchId);
      if (options.customerId) params.append('customerId', options.customerId);

      return await api.get(`/invoices?${params.toString()}`);
    } catch (error) {
      console.error('Error fetching invoices:', error);
      throw error;
    }
  },

  /**
   * Lấy danh sách phiếu nhập
   * @param {Object} options - Các tùy chọn báo cáo
   * @param {Date} options.startDate - Ngày bắt đầu
   * @param {Date} options.endDate - Ngày kết thúc
   * @param {Number} options.branchId - ID chi nhánh (nếu có)
   * @param {Number} options.supplierId - ID nhà cung cấp (nếu có)
   * @returns {Promise<Object>} Kết quả báo cáo
   */
  getImports: async (options = {}) => {
    try {
      // Xây dựng query params
      const params = new URLSearchParams();
      if (options.startDate) params.append('startDate', options.startDate);
      if (options.endDate) params.append('endDate', options.endDate);
      if (options.branchId) params.append('branchId', options.branchId);
      if (options.supplierId) params.append('supplierId', options.supplierId);

      return await api.get(`/imports?${params.toString()}`);
    } catch (error) {
      console.error('Error fetching imports:', error);
      throw error;
    }
  },

  /**
   * Lấy báo cáo công nợ khách hàng
   * @param {Object} options - Các tùy chọn báo cáo
   * @param {Date} options.startDate - Ngày bắt đầu
   * @param {Date} options.endDate - Ngày kết thúc
   * @param {Number} options.branchId - ID chi nhánh (nếu có)
   * @param {Number} options.customerId - ID khách hàng (nếu có)
   * @returns {Promise<Object>} Kết quả báo cáo
   */
  getCustomerDebt: async (options = {}) => {
    try {
      // Xây dựng query params
      const params = new URLSearchParams();
      if (options.startDate) params.append('startDate', options.startDate);
      if (options.endDate) params.append('endDate', options.endDate);
      if (options.branchId) params.append('branchId', options.branchId);
      if (options.customerId) params.append('customerId', options.customerId);

      return await api.get(`/reports/customer-debt?${params.toString()}`);
    } catch (error) {
      console.error('Error fetching customer debt report:', error);
      throw error;
    }
  },

  /**
   * Lấy báo cáo công nợ nhà cung cấp
   * @param {Object} options - Các tùy chọn báo cáo
   * @param {Date} options.startDate - Ngày bắt đầu
   * @param {Date} options.endDate - Ngày kết thúc
   * @param {Number} options.branchId - ID chi nhánh (nếu có)
   * @param {Number} options.supplierId - ID nhà cung cấp (nếu có)
   * @returns {Promise<Object>} Kết quả báo cáo
   */
  getSupplierDebt: async (options = {}) => {
    try {
      // Xây dựng query params
      const params = new URLSearchParams();
      if (options.startDate) params.append('startDate', options.startDate);
      if (options.endDate) params.append('endDate', options.endDate);
      if (options.branchId) params.append('branchId', options.branchId);
      if (options.supplierId) params.append('supplierId', options.supplierId);

      return await api.get(`/reports/supplier-debt?${params.toString()}`);
    } catch (error) {
      console.error('Error fetching supplier debt report:', error);
      throw error;
    }
  }
};

export default ReportService;
