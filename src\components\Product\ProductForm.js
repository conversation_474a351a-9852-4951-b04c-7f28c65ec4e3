import React, { useState, useEffect, useRef } from 'react';
import { Form, Input, Button, Card, Select, InputNumber, Switch, Typography, Divider, Space, Modal } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';
import { Toaster, toast } from 'react-hot-toast';
import ProductService from '../../services/product.service';
import ProductCategoryService from '../../services/productCategory.service';
import BrandService from '../../services/brand.service';

const { Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const ProductForm = () => {
  const [form] = Form.useForm();
  const [categoryForm] = Form.useForm();
  const [brandForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [categories, setCategories] = useState([]);
  const [brands, setBrands] = useState([]);
  const [newCategoryName, setNewCategoryName] = useState('');
  const [newCategoryParentId, setNewCategoryParentId] = useState(null);
  const [newBrandName, setNewBrandName] = useState('');
  const [addingCategory, setAddingCategory] = useState(false);
  const [addingBrand, setAddingBrand] = useState(false);
  const [categoryModalVisible, setCategoryModalVisible] = useState(false);
  const [brandModalVisible, setBrandModalVisible] = useState(false);
  const navigate = useNavigate();
  const { id } = useParams();
  const inputRef = useRef(null);

  useEffect(() => {
    fetchCategories();
    fetchBrands();

    if (id) {
      setIsEdit(true);
      fetchProductData();
    }
  }, [id]);

  const fetchCategories = async () => {
    try {
      const response = await ProductCategoryService.getAllCategories();
      const categoriesData = response.data;

      // Chỉ lấy các nhóm cha (không có parentId)
      const rootCategories = categoriesData.filter(cat => !cat.parentId)
        .sort((a, b) => a.name.localeCompare(b.name));

      // Tạo map để tra cứu nhanh các nhóm con
      const childrenMap = {};
      rootCategories.forEach(parent => {
        childrenMap[parent.id] = categoriesData
          .filter(cat => cat.parentId === parent.id)
          .sort((a, b) => a.name.localeCompare(b.name));
      });

      // Lưu toàn bộ dữ liệu để sử dụng sau này
      setCategories({
        roots: rootCategories,
        all: categoriesData,
        childrenMap: childrenMap
      });
    } catch (error) {
      toast.error('Không thể tải danh sách nhóm hàng: ' + error.message);
    }
  };

  const handleAddCategory = async () => {
    if (!newCategoryName.trim()) {
      toast.error('Vui lòng nhập tên nhóm hàng');
      return;
    }

    setAddingCategory(true);
    try {
      const categoryData = {
        name: newCategoryName.trim(),
        parentId: newCategoryParentId
      };

      const response = await ProductCategoryService.createCategory(categoryData);
      toast.success('Thêm nhóm hàng thành công');
      
      // Cập nhật danh sách nhóm hàng
      await fetchCategories();
      
      // Cập nhật giá trị nhóm hàng trong form
      form.setFieldsValue({ categoryId: response.data.id });
      
      // Reset form
      setNewCategoryName('');
      setNewCategoryParentId(null);
      setCategoryModalVisible(false);
    } catch (error) {
      toast.error('Không thể thêm nhóm hàng: ' + error.message);
    } finally {
      setAddingCategory(false);
    }
  };

  const fetchBrands = async () => {
    try {
      const response = await BrandService.getAllBrands();
      setBrands(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách thương hiệu: ' + error.message);
    }
  };

  const handleAddBrand = async () => {
    if (!newBrandName.trim()) {
      toast.error('Vui lòng nhập tên thương hiệu');
      return;
    }

    setAddingBrand(true);
    try {
      const brandData = {
        name: newBrandName.trim()
      };

      const response = await BrandService.createBrand(brandData);
      toast.success('Thêm thương hiệu thành công');
      
      // Cập nhật danh sách thương hiệu
      await fetchBrands();
      
      // Cập nhật giá trị thương hiệu trong form
      form.setFieldsValue({ brandId: response.data.id });
      
      // Reset form
      setNewBrandName('');
      setBrandModalVisible(false);
    } catch (error) {
      toast.error('Không thể thêm thương hiệu: ' + error.message);
    } finally {
      setAddingBrand(false);
    }
  };

  const fetchProductData = async () => {
    setLoading(true);
    try {
      const response = await ProductService.getProductById(id);
      const productData = response.data;
      form.setFieldsValue({
        code: productData.productCode || productData.code,
        barcode: productData.barcode,
        name: productData.name,
        description: productData.description,
        costPrice: productData.costPrice || 0,
        price: productData.sellingPrice || productData.price,
        stock: productData.stock,
        categoryId: productData.categoryId,
        brandId: productData.brandId,
        isActive: productData.isActive,
      });

      // Đảm bảo rằng chúng ta có dữ liệu nhóm hàng trước khi hiển thị form
      if (!categories.roots) {
        fetchCategories();
      }
    } catch (error) {
      toast.error('Không thể tải thông tin sản phẩm: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const onFinish = async (values) => {
    // Đảm bảo chuyển đổi giá trị thành số trước khi so sánh
    const costPrice = parseFloat(values.costPrice || 0);
    const price = parseFloat(values.price || 0);
    
    // Kiểm tra giá bán không được thấp hơn giá vốn
    if (price && costPrice && price < costPrice) {
      toast.error('Không thể lưu sản phẩm: Giá bán không được thấp hơn giá vốn!');
      return; // Dừng việc lưu sản phẩm
    }
    
    setLoading(true);
    try {
      // Convert field names to match backend
      const productData = {
        ...values,
        productCode: values.code,
        sellingPrice: values.price,
      };
      delete productData.code;
      delete productData.price;

      if (isEdit) {
        await ProductService.updateProduct(id, productData);
        toast.success('Cập nhật sản phẩm thành công');
      } else {
        await ProductService.createProduct(productData);
        toast.success('Thêm sản phẩm thành công');
      }
      navigate('/products');
    } catch (error) {
      toast.error('Lỗi: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <Toaster position="top-right" />
      <Title level={2} style={{ 
        background: 'linear-gradient(to right, #1890ff, #52c41a)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        fontSize: '24px',
        fontWeight: 'bold'
      }}>{isEdit ? 'Cập nhật sản phẩm' : 'Thêm sản phẩm mới'}</Title>
      <Card>
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          initialValues={{ isActive: true }}
          style={{ fontSize: '16px' }}
        >
          <Form.Item
            name="code"
            label="Mã sản phẩm"
            rules={[{ required: true, message: 'Vui lòng nhập mã sản phẩm' }]}
          >
            <Input style={{ height: '40px', fontSize: '16px' }} />
          </Form.Item>

          <Form.Item
            name="barcode"
            label="Mã vạch"
            rules={[
              {
                pattern: /^[a-zA-Z0-9]+$/,
                message: 'Mã vạch chỉ được chứa chữ cái và số, không chứa ký tự đặc biệt'
              }
            ]}
          >
            <Input style={{ height: '40px', fontSize: '16px' }} placeholder="Nhập mã vạch (chỉ chữ cái và số)" />
          </Form.Item>

          <Form.Item
            name="name"
            label="Tên sản phẩm"
            rules={[{ required: true, message: 'Vui lòng nhập tên sản phẩm' }]}
          >
            <Input style={{ height: '40px', fontSize: '16px' }} />
          </Form.Item>

          <Form.Item
            name="description"
            label="Mô tả"
          >
            <TextArea style={{ fontSize: '16px' }} rows={4} />
          </Form.Item>

          <Form.Item
            name="categoryId"
            label="Nhóm hàng"
            rules={[{ required: true, message: 'Vui lòng chọn nhóm hàng' }]}
          >
            <Select style={{ height: '40px', fontSize: '16px' }}
              placeholder="Chọn nhóm hàng"
              dropdownRender={menu => (
                <div>
                  {categories.roots ? menu : <div style={{ padding: 8 }}>Đang tải...</div>}
                  <Divider style={{ margin: '8px 0' }} />
                  <Space style={{ padding: '0 8px 4px' }}>
                    <Button style={{ height: '40px', fontSize: '16px' }} 
                      type="text" 
                      icon={<PlusOutlined />} 
                      onClick={() => setCategoryModalVisible(true)}
                    >
                      Thêm nhóm hàng mới
                    </Button>
                  </Space>
                </div>
              )}
            >
              {categories.roots && categories.roots.map(category => (
                <Select.OptGroup key={category.id} label={<strong>{category.name}</strong>}>
                  {/* Thêm nhóm cha như một option */}
                  <Option key={category.id} value={category.id}>
                    <strong>{category.name}</strong>
                  </Option>

                  {/* Thêm các nhóm con */}
                  {categories.childrenMap[category.id] && categories.childrenMap[category.id].map(child => (
                    <Option key={child.id} value={child.id}>
                      <span style={{ paddingLeft: '20px' }}>↳ {child.name}</span>
                    </Option>
                  ))}
                </Select.OptGroup>
              ))}
            </Select>
          </Form.Item>

          <Modal
            title="Thêm nhóm hàng mới"
            open={categoryModalVisible}
            onOk={handleAddCategory}
            onCancel={() => setCategoryModalVisible(false)}
            confirmLoading={addingCategory}
            okButtonProps={{ style: { height: '40px', fontSize: '16px' } }}
            cancelButtonProps={{ style: { height: '40px', fontSize: '16px' } }}
          >
            <Form form={categoryForm} layout="vertical">
              <Form.Item
                name="name"
                label="Tên nhóm hàng"
                rules={[{ required: true, message: 'Vui lòng nhập tên nhóm hàng' }]}
              >
                <Input style={{ height: '40px', fontSize: '16px' }} 
                  placeholder="Nhập tên nhóm hàng" 
                  value={newCategoryName}
                  onChange={(e) => setNewCategoryName(e.target.value)}
                />
              </Form.Item>
              <Form.Item
                name="parentId"
                label="Nhóm cha (tùy chọn)"
              >
                <Select style={{ height: '40px', fontSize: '16px' }} 
                  placeholder="Chọn nhóm cha (nếu có)"
                  allowClear
                  value={newCategoryParentId}
                  onChange={(value) => setNewCategoryParentId(value)}
                >
                  {categories.roots && categories.roots.map(category => (
                    <Option key={category.id} value={category.id}>{category.name}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Form>
          </Modal>

          <Form.Item
            name="brandId"
            label="Thương hiệu"
            rules={[{ required: true, message: 'Vui lòng chọn thương hiệu' }]}
          >
            <Select style={{ height: '40px', fontSize: '16px' }} 
              placeholder="Chọn thương hiệu"
              dropdownRender={menu => (
                <div>
                  {menu}
                  <Divider style={{ margin: '8px 0' }} />
                  <Space style={{ padding: '0 8px 4px' }}>
                    <Button style={{ height: '40px', fontSize: '16px' }} 
                      type="text" 
                      icon={<PlusOutlined />} 
                      onClick={() => setBrandModalVisible(true)}
                    >
                      Thêm thương hiệu mới
                    </Button>
                  </Space>
                </div>
              )}
            >
              {brands.map(brand => (
                <Option key={brand.id} value={brand.id}>{brand.name}</Option>
              ))}
            </Select>
          </Form.Item>

          <Modal
            title="Thêm thương hiệu mới"
            open={brandModalVisible}
            onOk={handleAddBrand}
            onCancel={() => setBrandModalVisible(false)}
            confirmLoading={addingBrand}
            okButtonProps={{ style: { height: '40px', fontSize: '16px' } }}
            cancelButtonProps={{ style: { height: '40px', fontSize: '16px' } }}
          >
            <Form form={brandForm} layout="vertical">
              <Form.Item
                name="name"
                label="Tên thương hiệu"
                rules={[{ required: true, message: 'Vui lòng nhập tên thương hiệu' }]}
              >
                <Input style={{ height: '40px', fontSize: '16px' }} 
                  placeholder="Nhập tên thương hiệu" 
                  value={newBrandName}
                  onChange={(e) => setNewBrandName(e.target.value)}
                />
              </Form.Item>
            </Form>
          </Modal>

          <Form.Item
            name="costPrice"
            label="Giá vốn"
            rules={[{ required: true, message: 'Vui lòng nhập giá vốn' }]}
          >
            <InputNumber 
              style={{ height: '40px', fontSize: '16px', width: '100%' }}
              formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, '.')}
              parser={value => value.replace(/\$\s?|(\.+)/g, '')}
              min={0}
            />
          </Form.Item>

          <Form.Item
            name="price"
            label="Giá bán"
            rules={[
              { required: true, message: 'Vui lòng nhập giá bán' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  // Đảm bảo chuyển đổi giá trị thành số trước khi so sánh
                  const costPrice = parseFloat(getFieldValue('costPrice') || 0);
                  const priceValue = parseFloat(value || 0);
                  
                  // Chỉ kiểm tra nếu cả hai giá trị đều hợp lệ và khác 0
                  if (priceValue && costPrice && priceValue < costPrice) {
                    return Promise.reject(new Error('Giá bán không được thấp hơn giá vốn!'));
                  }
                  return Promise.resolve();
                },
              }),
            ]}
            dependencies={['costPrice']}
          >
            <InputNumber 
              style={{ height: '40px', fontSize: '16px', width: '100%' }}
              formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, '.')}
              parser={value => value.replace(/\$\s?|(\.+)/g, '')}
              min={0}
              onBlur={(e) => {
                const value = parseFloat(e.target.value.replace(/\./g, ''));
                const costPrice = form.getFieldValue('costPrice');
                if (value && costPrice && value < costPrice) {
                  toast.error('Giá bán không được thấp hơn giá vốn!');
                }
              }}
            />
          </Form.Item>

          <Form.Item
            name="stock"
            label="Tồn kho"
            rules={[{ required: true, message: 'Vui lòng nhập số lượng tồn kho' }]}
          >
            <InputNumber style={{ height: '40px', fontSize: '16px', width: '100%' }} min={0} />
          </Form.Item>

          <Form.Item
            name="isActive"
            label="Trạng thái"
            valuePropName="checked"
          >
            <Switch checkedChildren="Hoạt động" unCheckedChildren="Không hoạt động" defaultChecked />
          </Form.Item>

          <Form.Item>
            <Button 
              type="primary" 
              htmlType="submit" 
              loading={loading}
              style={{ height: '40px', fontSize: '16px' }}
            >
              {isEdit ? 'Cập nhật' : 'Thêm mới'}
            </Button>
            <Button 
              style={{ marginLeft: 8, height: '40px', fontSize: '16px' }} 
              onClick={() => navigate('/products')}
            >
              Hủy
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default ProductForm;
