import api from './api';

const BranchService = {
  getAllBranches: async () => {
    return api.get('/branches');
  },

  getBranchById: async (id) => {
    return api.get(`/branches/${id}`);
  },

  createBranch: async (branchData) => {
    return api.post('/branches', branchData);
  },

  updateBranch: async (id, branchData) => {
    return api.put(`/branches/${id}`, branchData);
  },

  deleteBranch: async (id) => {
    return api.delete(`/branches/${id}`);
  }
};

export default BranchService;
