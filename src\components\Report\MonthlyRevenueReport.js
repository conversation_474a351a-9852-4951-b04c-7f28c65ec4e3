import React, { useState, useEffect } from 'react';
import { Card, DatePicker, Button, Select, Form, Space, Typography, Table, Row, Col } from 'antd';
import { Toaster, toast } from 'react-hot-toast';
import { SearchOutlined, ReloadOutlined, Bar<PERSON>hartOutlined, RollbackOutlined, LineChartOutlined } from '@ant-design/icons';
import ReportService from '../../services/report.service';
import BranchService from '../../services/branch.service';
import { formatCurrencyWithSymbol } from '../../utils/format';

const { Title } = Typography;
const { Option } = Select;

const MonthlyRevenueReport = () => {
  const [loading, setLoading] = useState(false);
  const [reportData, setReportData] = useState({
    year: new Date().getFullYear(),
    monthlyData: []
  });
  const [branches, setBranches] = useState([]);
  const [form] = Form.useForm();

  useEffect(() => {
    fetchBranches();
    fetchReport();
  }, []);

  const fetchBranches = async () => {
    try {
      const response = await BranchService.getAllBranches();
      setBranches(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách chi nhánh: ' + error.message);
    }
  };

  const fetchReport = async (values = {}) => {
    setLoading(true);
    try {
      const options = {
        year: values.year || new Date().getFullYear(),
        branchId: values.branchId,
      };

      const response = await ReportService.getMonthlyRevenue(options);
      setReportData(response.data.data);
    } catch (error) {
      toast.error('Không thể tải báo cáo: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (values) => {
    fetchReport(values);
  };

  const handleReset = () => {
    form.resetFields();
    fetchReport();
  };

  // Tạo mảng năm cho dropdown
  const getYearOptions = () => {
    const currentYear = new Date().getFullYear();
    const years = [];
    for (let i = currentYear - 5; i <= currentYear; i++) {
      years.push(i);
    }
    return years;
  };

  // Tên tháng tiếng Việt
  const getMonthName = (month) => {
    const monthNames = [
      'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
      'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'
    ];
    return monthNames[month - 1];
  };

  const columns = [
    {
      title: <span style={{ fontSize: 16 }}>Tháng</span>,
      dataIndex: 'month',
      key: 'month',
      render: (month) => getMonthName(month),
    },
    {
      title: <span style={{ fontSize: 16 }}>Tổng doanh thu</span>,
      dataIndex: 'revenue',
      key: 'revenue',
      render: (revenue) => formatCurrencyWithSymbol(revenue),
      sorter: (a, b) => a.revenue - b.revenue,
    },
    {
      title: <span style={{ fontSize: 16 }}>Giá trị hàng trả lại</span>,
      dataIndex: 'returnAmount',
      key: 'returnAmount',
      render: (returnAmount) => formatCurrencyWithSymbol(returnAmount || 0),
    },
    {
      title: <span style={{ fontSize: 16 }}>Doanh thu thuần</span>,
      dataIndex: 'netRevenue',
      key: 'netRevenue',
      render: (netRevenue, record) => formatCurrencyWithSymbol(netRevenue || (record.revenue - (record.returnAmount || 0))),
      sorter: (a, b) => (a.netRevenue || (a.revenue - (a.returnAmount || 0))) - (b.netRevenue || (b.revenue - (b.returnAmount || 0))),
    },
    {
      title: <span style={{ fontSize: 16 }}>Giá vốn</span>,
      dataIndex: 'cost',
      key: 'cost',
      render: (cost) => formatCurrencyWithSymbol(cost),
    },
    {
      title: <span style={{ fontSize: 16 }}>Lợi nhuận</span>,
      dataIndex: 'profit',
      key: 'profit',
      render: (profit, record) => {
        const netRevenue = record.netRevenue || (record.revenue - (record.returnAmount || 0));
        const actualProfit = netRevenue - record.cost;
        return formatCurrencyWithSymbol(actualProfit);
      },
      sorter: (a, b) => {
        const netRevenueA = a.netRevenue || (a.revenue - (a.returnAmount || 0));
        const netRevenueB = b.netRevenue || (b.revenue - (b.returnAmount || 0));
        return (netRevenueA - a.cost) - (netRevenueB - b.cost);
      },
    },
    {
      title: <span style={{ fontSize: 16 }}>Tỷ suất LN</span>,
      key: 'profitMargin',
      render: (_, record) => {
        const netRevenue = record.netRevenue || (record.revenue - (record.returnAmount || 0));
        const actualProfit = netRevenue - record.cost;
        const profitMargin = netRevenue > 0 ? (actualProfit / netRevenue) * 100 : 0;
        return `${profitMargin.toFixed(2)}%`;
      },
    },
    {
      title: <span style={{ fontSize: 16 }}>Số hóa đơn</span>,
      dataIndex: 'invoiceCount',
      key: 'invoiceCount',
      sorter: (a, b) => a.invoiceCount - b.invoiceCount,
    },
  ];

  // Tính tổng doanh thu, chi phí, hàng trả lại và lợi nhuận
  const calculateTotals = () => {
    const totalRevenue = reportData.monthlyData.reduce((sum, item) => sum + item.revenue, 0);
    const totalCost = reportData.monthlyData.reduce((sum, item) => sum + item.cost, 0);
    const totalReturns = reportData.monthlyData.reduce((sum, item) => sum + (item.returnAmount || 0), 0);
    const totalNetRevenue = totalRevenue - totalReturns;
    const totalProfit = totalNetRevenue - totalCost;
    const totalInvoices = reportData.monthlyData.reduce((sum, item) => sum + item.invoiceCount, 0);
    
    return {
      totalRevenue,
      totalCost,
      totalReturns,
      totalNetRevenue,
      totalProfit,
      totalInvoices,
      profitMargin: totalNetRevenue > 0 ? (totalProfit / totalNetRevenue) * 100 : 0
    };
  };

  const totals = calculateTotals();

  return (
    <div>
      <Toaster position="top-right" />
      <Card style={{ marginBottom: 20 }}>
        <Form
          form={form}
          layout="inline"
          onFinish={handleSearch}
          initialValues={{
            year: new Date().getFullYear(),
          }}
        >
          <Form.Item name="year" label={<span style={{ fontSize: 16 }}>Năm</span>}>
            <Select style={{ width: 120, height: 42, fontSize: 16 }}>
              {getYearOptions().map(year => (
                <Option key={year} value={year}>{year}</Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item name="branchId" label={<span style={{ fontSize: 16 }}>Chi nhánh</span>}>
            <Select
              placeholder="Tất cả chi nhánh"
              style={{ width: 200, height: 42, fontSize: 16 }}
              allowClear
            >
              {branches.map((branch) => (
                <Option key={branch.id} value={branch.id}>
                  {branch.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" icon={<SearchOutlined />} style={{ height: 40, fontSize: 16, display: 'flex', alignItems: 'center' }}>
                Tìm kiếm
              </Button>
              <Button onClick={handleReset} icon={<ReloadOutlined />} style={{ height: 40, fontSize: 16, display: 'flex', alignItems: 'center' }}>
                Đặt lại
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      <Card title={
        <div>
          <Title level={4} style={{ fontSize: 22, background: 'linear-gradient(90deg, #1890ff, #096dd9)', WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent' }}>
            <BarChartOutlined /> Báo cáo doanh thu theo tháng năm {reportData.year}
          </Title>
        </div>
      }>
        <Table
          columns={columns}
          dataSource={reportData.monthlyData}
          rowKey="month"
          loading={loading}
          pagination={false}
          style={{ fontSize: 16 }}
          summary={() => (
            <Table.Summary fixed>
              <Table.Summary.Row>
                <Table.Summary.Cell index={0}>
                  <strong style={{ fontSize: 16 }}>Tổng cộng</strong>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={1}>
                  <strong style={{ fontSize: 16 }}>{formatCurrencyWithSymbol(totals.totalRevenue)}</strong>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={2}>
                  <strong style={{ fontSize: 16 }}>{formatCurrencyWithSymbol(totals.totalReturns)}</strong>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={3}>
                  <strong style={{ fontSize: 16 }}>{formatCurrencyWithSymbol(totals.totalNetRevenue)}</strong>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={4}>
                  <strong style={{ fontSize: 16 }}>{formatCurrencyWithSymbol(totals.totalCost)}</strong>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={5}>
                  <strong style={{ fontSize: 16 }}>{formatCurrencyWithSymbol(totals.totalProfit)}</strong>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={6}>
                  <strong style={{ fontSize: 16 }}>{totals.profitMargin.toFixed(2)}%</strong>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={7}>
                  <strong style={{ fontSize: 16 }}>{totals.totalInvoices}</strong>
                </Table.Summary.Cell>
              </Table.Summary.Row>
            </Table.Summary>
          )}
        />
      </Card>
    </div>
  );
};

export default MonthlyRevenueReport;
