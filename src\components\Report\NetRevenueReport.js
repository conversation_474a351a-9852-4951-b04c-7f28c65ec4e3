import React, { useState, useEffect } from 'react';
import { Card, DatePicker, Button, Select, Form, Space, Typography, Statistic, Row, Col, Divider, Progress } from 'antd';
import { Toaster, toast } from 'react-hot-toast';
import { SearchOutlined, ReloadOutlined, DollarOutlined, ShoppingOutlined, RollbackOutlined, LineChartOutlined } from '@ant-design/icons';
import moment from 'moment';
import ReportService from '../../services/report.service';
import BranchService from '../../services/branch.service';
import { formatCurrencyWithSymbol } from '../../utils/format';

const { Title } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

const NetRevenueReport = () => {
  const [loading, setLoading] = useState(false);
  const [reportData, setReportData] = useState({
    period: {
      startDate: null,
      endDate: null
    },
    totalRevenue: 0,
    totalCost: 0,
    totalReturns: 0,
    netRevenue: 0,
    profitMargin: 0
  });
  const [branches, setBranches] = useState([]);
  const [form] = Form.useForm();

  useEffect(() => {
    fetchBranches();
    fetchReport();
  }, []);

  const fetchBranches = async () => {
    try {
      const response = await BranchService.getAllBranches();
      setBranches(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách chi nhánh: ' + error.message);
    }
  };

  const fetchReport = async (values = {}) => {
    setLoading(true);
    try {
      const options = {
        branchId: values.branchId,
      };

      if (values.dateRange && values.dateRange.length === 2) {
        options.startDate = values.dateRange[0].format('YYYY-MM-DD');
        options.endDate = values.dateRange[1].format('YYYY-MM-DD');
      }

      const response = await ReportService.getNetRevenue(options);
      setReportData(response.data.data);
    } catch (error) {
      toast.error('Không thể tải báo cáo: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (values) => {
    fetchReport(values);
  };

  const handleReset = () => {
    form.resetFields();
    fetchReport();
  };

  // Tính tỷ lệ phần trăm của từng thành phần
  const calculatePercentage = (value) => {
    if (!reportData.totalRevenue) return 0;
    return (value / reportData.totalRevenue) * 100;
  };

  // Hàm hiển thị ngày tháng an toàn
  const formatDateSafely = (dateValue, defaultText) => {
    // Kiểm tra nếu giá trị là null, undefined hoặc không phải chuỗi hợp lệ
    if (!dateValue || dateValue === 'All time' || dateValue === 'Tất cả' || dateValue === 'Hiện tại') {
      return defaultText;
    }
    
    // Kiểm tra nếu là đối tượng moment hợp lệ
    const momentDate = moment(dateValue);
    if (momentDate.isValid()) {
      return momentDate.format('DD/MM/YYYY');
    }
    
    return defaultText;
  };

  return (
    <div>
      <Toaster position="top-right" />
      <Card style={{ marginBottom: 20 }}>
        <Form
          form={form}
          layout="inline"
          onFinish={handleSearch}
        >
          <Form.Item name="dateRange" label={<span style={{ fontSize: 16 }}>Khoảng thời gian</span>}>
            <RangePicker format="DD/MM/YYYY" style={{ height: 42, fontSize: 16 }} />
          </Form.Item>
          <Form.Item name="branchId" label={<span style={{ fontSize: 16 }}>Chi nhánh</span>}>
            <Select
              placeholder="Tất cả chi nhánh"
              style={{ width: 200, height: 42, fontSize: 16 }}
              allowClear
            >
              {branches.map((branch) => (
                <Option key={branch.id} value={branch.id}>
                  {branch.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" icon={<SearchOutlined />} style={{ height: 40, fontSize: 16, display: 'flex', alignItems: 'center' }}>
                Tìm kiếm
              </Button>
              <Button onClick={handleReset} icon={<ReloadOutlined />} style={{ height: 40, fontSize: 16, display: 'flex', alignItems: 'center' }}>
                Đặt lại
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      <Card title={
        <div>
          <Title level={4} style={{ fontSize: 22, background: 'linear-gradient(90deg, #1890ff, #096dd9)', WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent' }}>Báo cáo doanh thu thuần</Title>
          <Typography.Text style={{ fontSize: 16 }}>
            Thời gian: {formatDateSafely(reportData.period.startDate, 'Tất cả')} - {formatDateSafely(reportData.period.endDate, 'Hiện tại')}
          </Typography.Text>
        </div>
      }>
        <Row gutter={16}>
          <Col span={12}>
            <Card>
              <Statistic
                title={<span style={{ fontSize: 16 }}>Tổng doanh thu</span>}
                value={formatCurrencyWithSymbol(reportData.totalRevenue)}
                prefix={<DollarOutlined />}
                loading={loading}
              />
              <Progress 
                percent={100} 
                status="active" 
                strokeColor="#1890ff"
                showInfo={false}
              />
            </Card>
          </Col>
          <Col span={12}>
            <Card>
              <Statistic
                title={<span style={{ fontSize: 16 }}>Doanh thu thuần</span>}
                value={formatCurrencyWithSymbol(reportData.netRevenue)}
                prefix={<LineChartOutlined />}
                loading={loading}
                valueStyle={{ color: reportData.netRevenue >= 0 ? '#3f8600' : '#cf1322' }}
              />
              <Progress 
                percent={calculatePercentage(reportData.netRevenue)} 
                status={reportData.netRevenue >= 0 ? "success" : "exception"}
                showInfo={false}
              />
            </Card>
          </Col>
        </Row>

        <Divider />

        <Row gutter={16}>
          <Col span={8}>
            <Card>
              <Statistic
                title={<span style={{ fontSize: 16 }}>Giá vốn hàng bán</span>}
                value={formatCurrencyWithSymbol(reportData.totalCost)}
                prefix={<ShoppingOutlined />}
                loading={loading}
              />
              <Progress 
                percent={calculatePercentage(reportData.totalCost)} 
                strokeColor="#faad14"
                showInfo={false}
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card>
              <Statistic
                title={<span style={{ fontSize: 16 }}>Giá trị hàng trả lại</span>}
                value={formatCurrencyWithSymbol(reportData.totalReturns)}
                prefix={<RollbackOutlined />}
                loading={loading}
              />
              <Progress 
                percent={calculatePercentage(reportData.totalReturns)} 
                strokeColor="#ff4d4f"
                showInfo={false}
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card>
              <Statistic
                title={<span style={{ fontSize: 16 }}>Tỷ suất lợi nhuận</span>}
                value={`${reportData.profitMargin.toFixed(2)}%`}
                prefix={<LineChartOutlined />}
                loading={loading}
                valueStyle={{ color: reportData.profitMargin >= 0 ? '#3f8600' : '#cf1322' }}
              />
              <Progress 
                percent={reportData.profitMargin} 
                status={reportData.profitMargin >= 0 ? "success" : "exception"}
                strokeColor={reportData.profitMargin >= 0 ? "#3f8600" : "#cf1322"}
              />
            </Card>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default NetRevenueReport;
