import api from './api';

const SettingService = {
  /**
   * <PERSON><PERSON><PERSON> tất cả cài đặt
   * @returns {Promise<Object>} Danh sách cài đặt
   */
  getAllSettings: async () => {
    try {
      return await api.get('/settings');
    } catch (error) {
      console.error('Error fetching settings:', error);
      throw error;
    }
  },

  /**
   * Lấy cài đặt theo key
   * @param {String} key - Khóa cài đặt
   * @returns {Promise<Object>} Thông tin cài đặt
   */
  getSettingByKey: async (key) => {
    try {
      return await api.get(`/settings/${key}`);
    } catch (error) {
      console.error(`Error fetching setting with key ${key}:`, error);
      throw error;
    }
  },

  /**
   * Tạo hoặc cập nhật cài đặt
   * @param {Object} settingData - Dữ liệu cài đặt
   * @param {String} settingData.key - Khóa cài đặt
   * @param {String|Number|Boolean|Object} settingData.value - G<PERSON><PERSON> trị cài đặt
   * @param {String} settingData.description - <PERSON><PERSON> tả cài đặt
   * @param {String} settingData.type - <PERSON><PERSON><PERSON> dữ liệu (string, number, boolean, json)
   * @returns {Promise<Object>} Thông tin cài đặt đã tạo/cập nhật
   */
  upsertSetting: async (settingData) => {
    try {
      return await api.post('/settings', settingData);
    } catch (error) {
      console.error('Error creating/updating setting:', error);
      throw error;
    }
  },

  /**
   * Xóa cài đặt
   * @param {String} key - Khóa cài đặt
   * @returns {Promise<Object>} Kết quả xóa
   */
  deleteSetting: async (key) => {
    try {
      return await api.delete(`/settings/${key}`);
    } catch (error) {
      console.error(`Error deleting setting with key ${key}:`, error);
      throw error;
    }
  },

  /**
   * Khởi tạo cài đặt mặc định
   * @returns {Promise<Object>} Kết quả khởi tạo
   */
  initDefaultSettings: async () => {
    try {
      return await api.post('/settings/init-default');
    } catch (error) {
      console.error('Error initializing default settings:', error);
      throw error;
    }
  }
};

export default SettingService;
