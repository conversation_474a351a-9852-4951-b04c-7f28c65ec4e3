import api from './api';

const ReceiptTypeService = {
  getAllReceiptTypes: async () => {
    return api.get('/receipt-types');
  },

  getReceiptTypeById: async (id) => {
    return api.get(`/receipt-types/${id}`);
  },

  createReceiptType: async (receiptTypeData) => {
    return api.post('/receipt-types', receiptTypeData);
  },

  updateReceiptType: async (id, receiptTypeData) => {
    return api.put(`/receipt-types/${id}`, receiptTypeData);
  },

  deleteReceiptType: async (id) => {
    return api.delete(`/receipt-types/${id}`);
  }
};

export default ReceiptTypeService;
