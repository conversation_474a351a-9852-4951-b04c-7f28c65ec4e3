import React, { createContext, useState, useEffect, useContext } from 'react';
import AuthService from '../services/auth.service';
import { message } from 'antd';

const AuthContext = createContext();

export const useAuth = () => useContext(AuthContext);

export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchUser = async () => {
      try {
        if (AuthService.isAuthenticated()) {
          const response = await AuthService.getCurrentUser();

          if (response.data) {
            // Kiểm tra xem dữ liệu có thuộc tính user không
            if (response.data.user) {
              setCurrentUser(response.data.user);
            } else {
              setCurrentUser(response.data);
            }
          } else {
            console.error('<PERSON>hông có dữ liệu người dùng từ API');
            AuthService.logout();
          }
        }
      } catch (error) {
        console.error('Lỗi khi lấy thông tin người dùng:', error);
        if (error.response) {
          console.error('Lỗi response:', error.response.data);
          console.error('Lỗi status:', error.response.status);
        }
        AuthService.logout();
      } finally {
        setLoading(false);
      }
    };

    fetchUser();
  }, []);

  const login = async (credentials) => {
    try {
      const data = await AuthService.login(credentials);

      if (data && data.user) {
        setCurrentUser(data.user);
        message.success('Đăng nhập thành công!');
      } else if (data && data.token) {
        // Nếu chỉ có token mà không có thông tin user, lấy thông tin user từ API
        try {
          const userResponse = await AuthService.getCurrentUser();

          if (userResponse.data) {
            // Kiểm tra xem dữ liệu có thuộc tính user không
            if (userResponse.data.user) {
              setCurrentUser(userResponse.data.user);
            } else {
              setCurrentUser(userResponse.data);
            }
          } else {
            console.warn('Đăng nhập thành công nhưng không lấy được thông tin người dùng');
          }
        } catch (userError) {
          console.error('Lỗi khi lấy thông tin người dùng sau đăng nhập:', userError);
        }

        message.success('Đăng nhập thành công!');
      } else {
        message.warning('Đăng nhập thành công nhưng không nhận được thông tin người dùng');
      }

      return data;
    } catch (error) {
      console.error('Lỗi đăng nhập:', error);
      if (error.response) {
        console.error('Lỗi response:', error.response.data);
      }
      message.error('Đăng nhập thất bại: ' + (error.response?.data?.message || 'Lỗi không xác định'));
      throw error;
    }
  };

  const logout = () => {
    AuthService.logout();
    setCurrentUser(null);
    message.success('Đã đăng xuất');
  };

  const value = {
    currentUser,
    loading,
    login,
    logout,
    isAuthenticated: AuthService.isAuthenticated
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export default AuthContext;
