import React, { useState } from 'react';
import { Tabs, Typography, Card } from 'antd';
import TopSellingProductsReport from './TopSellingProductsReport';
import DailyRevenueReport from './DailyRevenueReport';
import NetRevenueReport from './NetRevenueReport';
import MonthlyRevenueReport from './MonthlyRevenueReport';
import DebtReport from './DebtReport';

const { Title } = Typography;

const ReportDashboard = () => {
  const [activeTab, setActiveTab] = useState('1');

  const handleTabChange = (key) => {
    setActiveTab(key);
  };

  return (
    <div>
      <Title level={2} style={{ fontSize: 28, background: 'linear-gradient(90deg, #1890ff, #096dd9)', WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent' }}><PERSON><PERSON>o cáo thống kê</Title>
      
      <Card>
        <Tabs 
          activeKey={activeTab} 
          onChange={handleTabChange} 
          type="card"
          style={{ fontSize: 16 }}
          items={[
            {
              key: '1',
              label: 'Top sản phẩm bán chạy',
              children: <TopSellingProductsReport />
            },
            {
              key: '2',
              label: 'Doanh thu cuối ngày',
              children: <DailyRevenueReport />
            },
            {
              key: '3',
              label: 'Doanh thu thuần',
              children: <NetRevenueReport />
            },
            {
              key: '4',
              label: 'Doanh thu theo tháng',
              children: <MonthlyRevenueReport />
            },
            {
              key: '5',
              label: 'Báo cáo công nợ',
              children: <DebtReport />
            }
          ]}
        />
      </Card>
    </div>
  );
};

export default ReportDashboard;
