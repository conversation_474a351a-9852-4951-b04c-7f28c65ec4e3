import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Card, Switch, Typography, Divider } from 'antd';
import { useNavigate, useParams } from 'react-router-dom';
import { Toaster, toast } from 'react-hot-toast';
import BranchService from '../../services/branch.service';

const { Title } = Typography;
const { TextArea } = Input;

const BranchForm = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const navigate = useNavigate();
  const { id } = useParams();

  useEffect(() => {
    if (id) {
      setIsEdit(true);
      fetchBranchData();
    }
  }, [id]);

  const fetchBranchData = async () => {
    setLoading(true);
    try {
      const response = await BranchService.getBranchById(id);
      const branchData = response.data;
      form.setFieldsValue({
        name: branchData.name,
        address: branchData.address,
        phone: branchData.phone,
        email: branchData.email,
        description: branchData.description,
        isActive: branchData.isActive,
      });
    } catch (error) {
      toast.error('Không thể tải thông tin chi nhánh: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const onFinish = async (values) => {
    setLoading(true);
    try {
      if (isEdit) {
        await BranchService.updateBranch(id, values);
        toast.success('Cập nhật chi nhánh thành công');
      } else {
        await BranchService.createBranch(values);
        toast.success('Thêm chi nhánh thành công');
      }
      navigate('/branches');
    } catch (error) {
      toast.error('Lỗi: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ fontSize: '16px' }}> {/* Tăng kích thước phông chữ cơ bản thêm 2px */}
      <Toaster position="top-right" />
      <Title level={2} style={{ fontSize: '28px' }}>{isEdit ? 'Cập nhật chi nhánh' : 'Thêm chi nhánh mới'}</Title>
      <Divider />

      <Card>
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          style={{ fontSize: '16px' }}
        >
          <Form.Item
            name="name"
            label={<span style={{ fontSize: '16px' }}>Tên chi nhánh</span>}
            rules={[{ required: true, message: 'Vui lòng nhập tên chi nhánh' }]}
          >
            <Input style={{ height: '40px', fontSize: '16px' }} />
          </Form.Item>

          <Form.Item
            name="address"
            label={<span style={{ fontSize: '16px' }}>Địa chỉ</span>}
          >
            <Input style={{ height: '40px', fontSize: '16px' }} />
          </Form.Item>

          <Form.Item
            name="phone"
            label={<span style={{ fontSize: '16px' }}>Số điện thoại</span>}
          >
            <Input style={{ height: '40px', fontSize: '16px' }} />
          </Form.Item>

          <Form.Item
            name="email"
            label={<span style={{ fontSize: '16px' }}>Email</span>}
            rules={[
              { type: 'email', message: 'Email không hợp lệ' },
            ]}
          >
            <Input style={{ height: '40px', fontSize: '16px' }} />
          </Form.Item>

          <Form.Item
            name="description"
            label={<span style={{ fontSize: '16px' }}>Mô tả</span>}
          >
            <TextArea rows={4} style={{ fontSize: '16px' }} />
          </Form.Item>

          <Form.Item
            name="isActive"
            label={<span style={{ fontSize: '16px' }}>Trạng thái</span>}
            valuePropName="checked"
          >
            <Switch checkedChildren="Hoạt động" unCheckedChildren="Không hoạt động" defaultChecked style={{ fontSize: '14px' }} />
          </Form.Item>

          <Form.Item>
            <Button 
              type="primary" 
              htmlType="submit" 
              loading={loading}
              size="large"
              style={{ height: '40px', fontSize: '16px', fontWeight: 'bold', padding: '0 25px' }}
            >
              {isEdit ? 'Cập nhật' : 'Thêm mới'}
            </Button>
            <Button 
              style={{ marginLeft: 16, height: '40px', fontSize: '16px', padding: '0 25px' }} 
              onClick={() => navigate('/branches')}
              size="large"
            >
              Hủy
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default BranchForm;
