import React, { useState, useEffect } from 'react';
import { Table, Button, Space, Popconfirm, Typography, Input, Card, Tag } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, SearchOutlined } from '@ant-design/icons';
import { Toaster, toast } from 'react-hot-toast';
import { Link } from 'react-router-dom';
import ReceiptTypeService from '../../services/receiptType.service';

const { Title } = Typography;

const ReceiptTypeList = () => {
  const [receiptTypes, setReceiptTypes] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');

  useEffect(() => {
    fetchReceiptTypes();
  }, []);

  const fetchReceiptTypes = async () => {
    setLoading(true);
    try {
      const response = await ReceiptTypeService.getAllReceiptTypes();
      setReceiptTypes(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách loại thu chi: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    try {
      await ReceiptTypeService.deleteReceiptType(id);
      toast.success('Xóa loại thu chi thành công');
      fetchReceiptTypes();
    } catch (error) {
      toast.error('Không thể xóa loại thu chi: ' + error.message);
    }
  };

  const filteredReceiptTypes = receiptTypes.filter(
    (receiptType) =>
      receiptType.name?.toLowerCase().includes(searchText.toLowerCase()) ||
      receiptType.description?.toLowerCase().includes(searchText.toLowerCase())
  );

  const columns = [
    {
      title: <span style={{ fontSize: 16 }}>Tên loại thu chi</span>,
      dataIndex: 'name',
      key: 'name',
      sorter: (a, b) => a.name.localeCompare(b.name),
    },
    {
      title: <span style={{ fontSize: 16 }}>Loại</span>,
      dataIndex: 'type',
      key: 'type',
      render: (type) => {
        let text = 'Khác';
        let color = 'blue';

        if (type === 'transfer') {
          text = 'Chuyển/Rút';
          color = 'purple';
        } else if (type === 'income') {
          text = 'Thu';
          color = 'green';
        } else if (type === 'expense') {
          text = 'Chi';
          color = 'red';
        }

        return <Tag color={color}>{text}</Tag>;
      },
      filters: [
        { text: 'Thu', value: 'income' },
        { text: 'Chi', value: 'expense' },
        { text: 'Chuyển/Rút', value: 'transfer' },
        { text: 'Khác', value: 'other' },
      ],
      onFilter: (value, record) => record.type === value,
    },
    {
      title: <span style={{ fontSize: 16 }}>Mô tả</span>,
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: <span style={{ fontSize: 16 }}>Trạng thái</span>,
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? 'Hoạt động' : 'Không hoạt động'}
        </Tag>
      ),
    },
    {
      title: <span style={{ fontSize: 16 }}>Thao tác</span>,
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Link to={`/receipt-types/edit/${record.id}`}>
            <Button type="primary" icon={<EditOutlined />} size="small" style={{ height: 40, fontSize: 14, display: 'flex', alignItems: 'center', padding: '0 15px' }}>
              Sửa
            </Button>
          </Link>
          <Popconfirm
            title="Bạn có chắc chắn muốn xóa loại thu chi này?"
            onConfirm={() => handleDelete(record.id)}
            okText="Có"
            cancelText="Không"
          >
            <Button type="primary" danger icon={<DeleteOutlined />} size="small" style={{ height: 40, fontSize: 14, display: 'flex', alignItems: 'center', padding: '0 15px' }}>
              Xóa
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Toaster position="top-right" />
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 20 }}>
        <Title level={2} style={{ fontSize: 28, background: 'linear-gradient(90deg, #1890ff, #096dd9)', WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent' }}>Quản lý loại thu chi</Title>
        <Link to="/receipt-types/add">
          <Button type="primary" icon={<PlusOutlined />} style={{ height: 40, fontSize: 16, display: 'flex', alignItems: 'center' }}>
            Thêm loại thu chi
          </Button>
        </Link>
      </div>

      <Card style={{ marginBottom: 16 }}>
        <Input
          placeholder="Tìm kiếm loại thu chi..."
          prefix={<SearchOutlined />}
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          style={{ width: 300, height: 42, fontSize: 16 }}
        />
      </Card>

      <Table
        columns={columns}
        dataSource={filteredReceiptTypes}
        rowKey="id"
        loading={loading}
        pagination={{ pageSize: 10, style: { fontSize: 16 } }}
        style={{ fontSize: 16 }}
      />
    </div>
  );
};

export default ReceiptTypeList;
