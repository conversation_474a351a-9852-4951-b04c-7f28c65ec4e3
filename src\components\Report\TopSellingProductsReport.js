import React, { useState, useEffect } from 'react';
import { Table, Card, DatePicker, Button, Select, Form, Space, Typography } from 'antd';
import { Toaster, toast } from 'react-hot-toast';
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons';
import moment from 'moment';
import ReportService from '../../services/report.service';
import BranchService from '../../services/branch.service';
import { formatCurrencyWithSymbol } from '../../utils/format';

const { Title } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

const TopSellingProductsReport = () => {
  const [loading, setLoading] = useState(false);
  const [products, setProducts] = useState([]);
  const [branches, setBranches] = useState([]);
  const [form] = Form.useForm();

  useEffect(() => {
    fetchBranches();
    fetchReport();
  }, []);

  const fetchBranches = async () => {
    try {
      const response = await BranchService.getAllBranches();
      setBranches(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách chi nhánh: ' + error.message);
    }
  };

  const fetchReport = async (values = {}) => {
    setLoading(true);
    try {
      const options = {
        limit: values.limit || 10,
        branchId: values.branchId,
      };

      if (values.dateRange && values.dateRange.length === 2) {
        options.startDate = values.dateRange[0].format('YYYY-MM-DD');
        options.endDate = values.dateRange[1].format('YYYY-MM-DD');
      }

      const response = await ReportService.getTopSellingProducts(options);
      setProducts(response.data.data);
    } catch (error) {
      toast.error('Không thể tải báo cáo: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (values) => {
    fetchReport(values);
  };

  const handleReset = () => {
    form.resetFields();
    fetchReport();
  };

  const columns = [
    {
      title: <span style={{ fontSize: '16px', fontWeight: 'bold' }}>STT</span>,
      key: 'index',
      width: 60,
      render: (_, __, index) => index + 1,
    },
    {
      title: <span style={{ fontSize: '16px', fontWeight: 'bold' }}>Mã sản phẩm</span>,
      dataIndex: ['Product', 'productCode'],
      key: 'productCode',
    },
    {
      title: <span style={{ fontSize: '16px', fontWeight: 'bold' }}>Tên sản phẩm</span>,
      dataIndex: ['Product', 'name'],
      key: 'name',
    },
    {
      title: <span style={{ fontSize: '16px', fontWeight: 'bold' }}>Số lượng bán</span>,
      dataIndex: 'totalQuantity',
      key: 'totalQuantity',
      sorter: (a, b) => a.totalQuantity - b.totalQuantity,
    },
    {
      title: <span style={{ fontSize: '16px', fontWeight: 'bold' }}>Giá vốn</span>,
      dataIndex: ['Product', 'costPrice'],
      key: 'costPrice',
      render: (costPrice) => formatCurrencyWithSymbol(costPrice),
    },
    {
      title: <span style={{ fontSize: '16px', fontWeight: 'bold' }}>Giá bán</span>,
      dataIndex: ['Product', 'sellingPrice'],
      key: 'sellingPrice',
      render: (sellingPrice) => formatCurrencyWithSymbol(sellingPrice),
    },
    {
      title: <span style={{ fontSize: '16px', fontWeight: 'bold' }}>Doanh thu</span>,
      dataIndex: 'totalRevenue',
      key: 'totalRevenue',
      render: (totalRevenue) => formatCurrencyWithSymbol(totalRevenue),
      sorter: (a, b) => a.totalRevenue - b.totalRevenue,
      defaultSortOrder: 'descend',
    },
    {
      title: <span style={{ fontSize: '16px', fontWeight: 'bold' }}>Tổng giá vốn</span>,
      dataIndex: 'totalCost',
      key: 'totalCost',
      render: (totalCost) => formatCurrencyWithSymbol(totalCost),
    },
    {
      title: <span style={{ fontSize: '16px', fontWeight: 'bold' }}>Lợi nhuận</span>,
      dataIndex: 'profit',
      key: 'profit',
      render: (profit) => formatCurrencyWithSymbol(profit),
      sorter: (a, b) => a.profit - b.profit,
    },
    {
      title: <span style={{ fontSize: '16px', fontWeight: 'bold' }}>Tỷ suất LN</span>,
      dataIndex: 'profitMargin',
      key: 'profitMargin',
      render: (profitMargin) => `${profitMargin.toFixed(2)}%`,
      sorter: (a, b) => a.profitMargin - b.profitMargin,
    },
  ];

  return (
    <div>
      <Toaster position="top-right" />
      <Title level={2} style={{ 
        background: 'linear-gradient(to right, #1890ff, #52c41a)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        fontSize: '24px',
        fontWeight: 'bold',
        marginBottom: '20px'
      }}>Báo cáo sản phẩm bán chạy</Title>
      <Card style={{ marginBottom: 20 }}>
        <Form
          form={form}
          layout="inline"
          onFinish={handleSearch}
          initialValues={{
            limit: 10,
          }}
          style={{ fontSize: '16px' }}
        >
          <Form.Item name="dateRange" label={<span style={{ fontSize: '16px', fontWeight: 'bold' }}>Khoảng thời gian</span>}>
            <RangePicker format="DD/MM/YYYY" style={{ height: '40px', fontSize: '16px' }} />
          </Form.Item>
          <Form.Item name="branchId" label={<span style={{ fontSize: '16px', fontWeight: 'bold' }}>Chi nhánh</span>}>
            <Select
              placeholder="Tất cả chi nhánh"
              style={{ width: 200, height: '40px', fontSize: '16px' }}
              allowClear
            >
              {branches.map((branch) => (
                <Option key={branch.id} value={branch.id}>
                  {branch.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item name="limit" label={<span style={{ fontSize: '16px', fontWeight: 'bold' }}>Số lượng hiển thị</span>}>
            <Select style={{ width: 120, height: '40px', fontSize: '16px' }}>
              <Option value={5}>5</Option>
              <Option value={10}>10</Option>
              <Option value={20}>20</Option>
              <Option value={50}>50</Option>
            </Select>
          </Form.Item>
          <Form.Item>
            <Space>
              <Button 
                type="primary" 
                htmlType="submit" 
                icon={<SearchOutlined />} 
                style={{ height: '40px', fontSize: '16px', display: 'flex', alignItems: 'center' }}
              >
                Tìm kiếm
              </Button>
              <Button 
                onClick={handleReset} 
                icon={<ReloadOutlined />} 
                style={{ height: '40px', fontSize: '16px', display: 'flex', alignItems: 'center' }}
              >
                Đặt lại
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      <Table
        columns={columns}
        dataSource={products}
        rowKey={(record) => record.Product.id}
        loading={loading}
        pagination={{ pageSize: 10, style: { fontSize: '16px' } }}
        style={{ fontSize: '16px' }}
        scroll={{ x: 1200 }}
      />
    </div>
  );
};

export default TopSellingProductsReport;
