import React, { useState, useEffect } from 'react';
import { Card, Descriptions, Typography, Button, Tag, Divider, Space } from 'antd';
import { useParams, useNavigate } from 'react-router-dom';
import { Toaster, toast } from 'react-hot-toast';
import { PrinterOutlined, EditOutlined } from '@ant-design/icons';
import PaymentService from '../../services/payment.service';
import ReceiptTypeService from '../../services/receiptType.service';
import UserService from '../../services/user.service';
import BranchService from '../../services/branch.service';
import ImportService from '../../services/import.service';
import BankAccountService from '../../services/bankAccount.service';
import { formatCurrencyWithSymbol } from '../../utils/format';
import './PrintStyles.css';

const { Title } = Typography;

const PaymentDetail = () => {
  const [payment, setPayment] = useState(null);
  const [receiptTypes, setReceiptTypes] = useState([]);
  const [loading, setLoading] = useState(true);
  const { id } = useParams();
  const navigate = useNavigate();

  useEffect(() => {
    fetchPaymentData();
  }, [id]);

  const fetchPaymentData = async () => {
    setLoading(true);
    try {
      // Lấy dữ liệu từ tất cả các API cần thiết
      const [
        paymentResponse,
        receiptTypesResponse,
        usersResponse,
        branchesResponse,
        importsResponse,
        bankAccountsResponse
      ] = await Promise.all([
        PaymentService.getPaymentById(id),
        ReceiptTypeService.getAllReceiptTypes(),
        UserService.getAllUsers(),
        BranchService.getAllBranches(),
        ImportService.getAllImports(),
        BankAccountService.getAllBankAccounts()
      ]);

      // Lưu danh sách loại thu/chi và các danh sách khác
      setReceiptTypes(receiptTypesResponse.data);

      // Lấy dữ liệu phiếu chi
      const paymentData = paymentResponse.data;

      // Bổ sung thông tin loại chi
      if (paymentData.bankAccountId) {
        const transferType = receiptTypesResponse.data.find(type => Number(type.id) === 2);
        if (transferType) {
          paymentData.paymentType = transferType;
          paymentData.receiptType = transferType;
        } else {
          // Tạo loại chi Tài khoản tạm thời
          const tempTransferType = {
            id: 2,
            name: 'Tài khoản',
            type: 'transfer'
          };
          paymentData.paymentType = tempTransferType;
          paymentData.receiptType = tempTransferType;
        }
      } else {
        // Kiểm tra cả paymentTypeId và receiptTypeId
        const typeId = paymentData.paymentTypeId || paymentData.receiptTypeId;

        if (typeId) {
          // Chuyển đổi ID sang số để so sánh chính xác
          const numericTypeId = Number(typeId);

          // Tìm trong danh sách loại thu/chi
          const matchingType = receiptTypesResponse.data.find(type => Number(type.id) === numericTypeId);

          if (matchingType) {
            paymentData.paymentType = matchingType;
            paymentData.receiptType = matchingType;
          } else {
            // Tạo loại chi tạm thời dựa trên ID
            const tempType = {
              id: numericTypeId,
              name: numericTypeId === 1 ? 'Tiền mặt' : `Loại chi #${numericTypeId}`,
              type: numericTypeId === 2 ? 'transfer' : 'other'
            };
            paymentData.paymentType = tempType;
            paymentData.receiptType = tempType;
          }
        } else {
          // Tìm loại chi Tiền mặt (ID: 1)
          const cashType = receiptTypesResponse.data.find(type => Number(type.id) === 1);

          if (cashType) {
            paymentData.paymentType = cashType;
            paymentData.receiptType = cashType;
          } else {
            // Tạo loại chi Tiền mặt tạm thời
            const tempCashType = {
              id: 1,
              name: 'Tiền mặt',
              type: 'other'
            };
            paymentData.paymentType = tempCashType;
            paymentData.receiptType = tempCashType;
          }
        }
      }

      // Bổ sung thông tin người dùng (nhân viên chi)
      if (paymentData.userId) {
        const matchingUser = usersResponse.data.find(user => Number(user.id) === Number(paymentData.userId));
        if (matchingUser) {
          paymentData.user = matchingUser;
        } else {
          // Tạo thông tin nhân viên tạm thời
          paymentData.user = {
            id: paymentData.userId,
            fullName: `Nhân viên #${paymentData.userId}`
          };
        }
      }

      // Bổ sung thông tin chi nhánh
      if (paymentData.branchId) {
        const matchingBranch = branchesResponse.data.find(branch => Number(branch.id) === Number(paymentData.branchId));
        if (matchingBranch) {
          paymentData.branch = matchingBranch;
        } else {
          // Tạo thông tin chi nhánh tạm thời
          paymentData.branch = {
            id: paymentData.branchId,
            name: `Chi nhánh ${paymentData.branchId}`
          };
        }
      }

      // Bổ sung thông tin phiếu nhập liên quan
      if (paymentData.importId) {
        const matchingImport = importsResponse.data.find(importItem => Number(importItem.id) === Number(paymentData.importId));
        if (matchingImport) {
          paymentData.import = matchingImport;
        } else {
          // Tạo thông tin phiếu nhập tạm thời
          paymentData.import = {
            id: paymentData.importId,
            importCode: `PN${paymentData.importId < 10 ? '00' : '0'}${paymentData.importId}`
          };
        }
      }

      // Bổ sung thông tin tài khoản ngân hàng
      if (paymentData.bankAccountId) {
        try {
          // Lấy thông tin tài khoản ngân hàng trực tiếp từ API
          const bankAccountResponse = await BankAccountService.getBankAccountById(paymentData.bankAccountId);
          paymentData.bankAccount = bankAccountResponse.data;
        } catch (bankAccountError) {
          console.error('Lỗi khi lấy thông tin tài khoản ngân hàng:', bankAccountError);

          // Tìm trong danh sách tài khoản ngân hàng
          const matchingAccount = bankAccountsResponse.data.find(account => Number(account.id) === Number(paymentData.bankAccountId));
          if (matchingAccount) {
            paymentData.bankAccount = matchingAccount;
          } else {
            // Tạo thông tin tài khoản tạm thời
            paymentData.bankAccount = {
              id: paymentData.bankAccountId,
              accountNumber: `TK-${paymentData.bankAccountId}`,
              bankName: 'Ngân hàng'
            };
          }
        }
      }

      // Lưu dữ liệu phiếu chi đã được bổ sung thông tin
      setPayment(paymentData);
    } catch (error) {
      console.error('Lỗi khi tải thông tin phiếu chi:', error);
      toast.error('Không thể tải thông tin phiếu chi: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'green';
      case 'pending':
        return 'orange';
      case 'cancelled':
        return 'red';
      default:
        return 'default';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'completed':
        return 'Đã hoàn thành';
      case 'pending':
        return 'Chờ xử lý';
      case 'cancelled':
        return 'Đã hủy';
      default:
        return status;
    }
  };

  const handlePrint = () => {
    // Tạo cửa sổ mới để in
    const printWindow = window.open('', '_blank', 'width=800,height=600');

    // Tạo nội dung HTML cho trang in
    const printHtml = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>In phiếu chi - ${payment.paymentCode}</title>
        <style>
          @page {
            size: A4 landscape;
            margin: 0.5cm;
          }
          body {
            font-family: 'Segoe UI', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            font-size: 14px;
          }
          .print-header {
            text-align: center;
            margin-bottom: 20px;
          }
          .print-header h1 {
            font-size: 24px;
            text-transform: uppercase;
            margin: 10px 0;
          }
          .print-header p {
            margin: 5px 0;
          }
          .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
          }
          .info-table th, .info-table td {
            border: 1px solid #ddd;
            padding: 8px 12px;
            text-align: left;
          }
          .info-table th {
            background-color: #f5f5f5;
            width: 30%;
          }
          .amount-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
          }
          .amount-table th, .amount-table td {
            border: 1px solid #ddd;
            padding: 8px 12px;
            text-align: left;
          }
          .amount-table th {
            background-color: #f5f5f5;
          }
          .amount-value {
            font-size: 18px;
            font-weight: bold;
          }
          .note {
            margin-bottom: 20px;
          }
          .signature-section {
            display: flex;
            justify-content: space-between;
            margin-top: 40px;
          }
          .signature-block {
            text-align: center;
            width: 30%;
          }
          .signature-block p {
            margin: 5px 0;
          }
        </style>
      </head>
      <body>
        <div class="print-header">
          <h1>PHIẾU CHI</h1>
          <p>Mã phiếu chi: ${payment.paymentCode}</p>
          <p>Ngày: ${new Date(payment.paymentDate).toLocaleDateString('vi-VN')}</p>
        </div>

        <table class="info-table">
          <tr>
            <th>Loại chi</th>
            <td>${payment.paymentType?.name || payment.receiptType?.name || 'N/A'}</td>
          </tr>
          <tr>
            <th>Người nhận</th>
            <td>${payment.receiverName || 'N/A'}</td>
          </tr>
          <tr>
            <th>Nhân viên chi</th>
            <td>${payment.user?.fullName || 'N/A'}</td>
          </tr>
          <tr>
            <th>Chi nhánh</th>
            <td>${payment.branch?.name || 'N/A'}</td>
          </tr>
          <tr>
            <th>Trạng thái</th>
            <td>${getStatusText(payment.status)}</td>
          </tr>
          ${payment.receiptType?.type === 'transfer' ? `
          <tr>
            <th>Tài khoản ngân hàng</th>
            <td>${payment.bankAccount ? `${payment.bankAccount.accountNumber} - ${payment.bankAccount.bankName}` : 'N/A'}</td>
          </tr>
          ` : ''}
          ${payment.importId ? `
          <tr>
            <th>Phiếu nhập liên quan</th>
            <td>${payment.import ? payment.import.importCode : 'N/A'}</td>
          </tr>
          ` : ''}
        </table>

        <table class="amount-table">
          <tr>
            <th>Số tiền</th>
            <td class="amount-value">${formatCurrencyWithSymbol(payment.amount)}</td>
          </tr>
        </table>

        ${payment.note ? `
        <div class="note">
          <strong>Ghi chú:</strong>
          <p>${payment.note}</p>
        </div>
        ` : ''}

        <div class="signature-section">
          <div class="signature-block">
            <p><strong>Người lập phiếu</strong></p>
            <p>(Ký, họ tên)</p>
          </div>
          <div class="signature-block">
            <p><strong>Người nhận tiền</strong></p>
            <p>(Ký, họ tên)</p>
          </div>
          <div class="signature-block">
            <p><strong>Thủ quỹ</strong></p>
            <p>(Ký, họ tên)</p>
          </div>
        </div>

        <script>
          // Tự động in khi trang đã tải xong
          window.onload = function() {
            window.print();
            // Sau khi in xong sẽ tự đóng cửa sổ sau 1 giây
            setTimeout(function() {
              window.close();
            }, 1000);
          };
        </script>
      </body>
      </html>
    `;

    // Ghi HTML vào cửa sổ mới
    printWindow.document.open();
    printWindow.document.write(printHtml);
    printWindow.document.close();
  };

  if (loading) {
    return <div>Đang tải...</div>;
  }

  if (!payment) {
    return <div>Không tìm thấy phiếu chi</div>;
  }

  return (
    <div>
      <Toaster position="top-right" />
      <div className="no-print" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
        <Title level={2}>Chi tiết phiếu chi</Title>
        <Space>
          <Button type="primary" icon={<EditOutlined />} onClick={() => navigate(`/payments/edit/${id}`)}>
            Sửa
          </Button>
          <Button icon={<PrinterOutlined />} onClick={handlePrint}>
            In phiếu chi
          </Button>
        </Space>
      </div>

      <Card className="print-container">
        <div className="print-header" style={{ textAlign: 'center', marginBottom: 24 }}>
          <Title level={3} className="print-title">PHIẾU CHI</Title>
          <p className="print-code">Mã phiếu chi: {payment.paymentCode}</p>
          <p className="print-date">Ngày: {new Date(payment.paymentDate).toLocaleDateString('vi-VN')}</p>
        </div>

        <Descriptions bordered column={2}>
          <Descriptions.Item label="Loại chi" span={2}>
            {payment.paymentType?.name || payment.receiptType?.name || 'N/A'}
          </Descriptions.Item>
          <Descriptions.Item label="Người nhận">
            {payment.receiverName || 'N/A'}
          </Descriptions.Item>
          <Descriptions.Item label="Nhân viên chi">
            {payment.user?.fullName || 'N/A'}
          </Descriptions.Item>
          <Descriptions.Item label="Chi nhánh">
            {payment.branch?.name || 'N/A'}
          </Descriptions.Item>
          <Descriptions.Item label="Trạng thái">
            <Tag color={getStatusColor(payment.status)}>
              {getStatusText(payment.status)}
            </Tag>
          </Descriptions.Item>
          {payment.receiptType?.type === 'transfer' && (
            <Descriptions.Item label="Tài khoản ngân hàng" span={2}>
              {payment.bankAccount ? `${payment.bankAccount.accountNumber} - ${payment.bankAccount.bankName}` : 'N/A'}
            </Descriptions.Item>
          )}
          {payment.importId && (
            <Descriptions.Item label="Phiếu nhập liên quan" span={2}>
              {payment.import ? payment.import.importCode : 'N/A'}
            </Descriptions.Item>
          )}
        </Descriptions>

        <Divider />

        <Descriptions bordered>
          <Descriptions.Item label="Số tiền" span={3}>
            <strong style={{ fontSize: '18px' }}>{formatCurrencyWithSymbol(payment.amount)}</strong>
          </Descriptions.Item>
        </Descriptions>

        {payment.note && (
          <>
            <Divider />
            <div>
              <strong>Ghi chú:</strong>
              <p>{payment.note}</p>
            </div>
          </>
        )}

        <Divider />

        <div className="print-signatures" style={{ display: 'flex', justifyContent: 'space-between', marginTop: 30 }}>
          <div className="signature-block" style={{ textAlign: 'center', width: '30%' }}>
            <p><strong>Người lập phiếu</strong></p>
            <p>(Ký, họ tên)</p>
          </div>
          <div className="signature-block" style={{ textAlign: 'center', width: '30%' }}>
            <p><strong>Người nhận tiền</strong></p>
            <p>(Ký, họ tên)</p>
          </div>
          <div className="signature-block" style={{ textAlign: 'center', width: '30%' }}>
            <p><strong>Thủ quỹ</strong></p>
            <p>(Ký, họ tên)</p>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default PaymentDetail;
