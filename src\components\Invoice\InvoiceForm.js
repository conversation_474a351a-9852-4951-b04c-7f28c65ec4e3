import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Card, Select, InputNumber, Typography, Divider, Table, Space, Modal, Switch } from 'antd';
import { Toaster, toast } from 'react-hot-toast';
import { PlusOutlined, MinusCircleOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';
import InvoiceService from '../../services/invoice.service';
import CustomerService from '../../services/customer.service';
import ProductService from '../../services/product.service';
import BranchService from '../../services/branch.service';
import UserService from '../../services/user.service';
import ProductCategoryService from '../../services/productCategory.service';
import BrandService from '../../services/brand.service';
import { formatCurrency, formatCurrencyWithSymbol } from '../../utils/format';

const { Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const InvoiceForm = () => {
  const [form] = Form.useForm();
  const [productForm] = Form.useForm();
  const [customerForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [customers, setCustomers] = useState([]);
  const [products, setProducts] = useState([]);
  const [branches, setBranches] = useState([]);
  const [users, setUsers] = useState([]);
  const [categories, setCategories] = useState({ roots: [], all: [], childrenMap: {} });
  const [brands, setBrands] = useState([]);
  const [selectedItems, setSelectedItems] = useState([]);
  const [totalAmount, setTotalAmount] = useState(0);
  const [productModalVisible, setProductModalVisible] = useState(false);
  const [customerModalVisible, setCustomerModalVisible] = useState(false);
  const [addingCustomer, setAddingCustomer] = useState(false);
  const [newCustomerCode, setNewCustomerCode] = useState('');
  const [newCustomerName, setNewCustomerName] = useState('');
  const [newCustomerPhone, setNewCustomerPhone] = useState('');
  const [newCustomerAddress, setNewCustomerAddress] = useState('');
  const navigate = useNavigate();
  const { id } = useParams();

  useEffect(() => {
    fetchCustomers();
    fetchProducts();
    fetchBranches();
    fetchUsers();
    fetchCategories();
    fetchBrands();

    if (id) {
      setIsEdit(true);
      fetchInvoiceData();
    }
  }, [id]);

  const fetchCategories = async () => {
    try {
      const response = await ProductCategoryService.getAllCategories();
      const categoriesData = response.data;

      // Chỉ lấy các nhóm cha (không có parentId)
      const rootCategories = categoriesData.filter(cat => !cat.parentId)
        .sort((a, b) => a.name.localeCompare(b.name));

      // Tạo map để tra cứu nhanh các nhóm con
      const childrenMap = {};
      rootCategories.forEach(parent => {
        childrenMap[parent.id] = categoriesData
          .filter(cat => cat.parentId === parent.id)
          .sort((a, b) => a.name.localeCompare(b.name));
      });

      // Lưu toàn bộ dữ liệu để sử dụng sau này
      setCategories({
        roots: rootCategories,
        all: categoriesData,
        childrenMap: childrenMap
      });
    } catch (error) {
      toast.error('Không thể tải danh sách nhóm hàng: ' + error.message);
    }
  };

  const fetchBrands = async () => {
    try {
      const response = await BrandService.getAllBrands();
      setBrands(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách thương hiệu: ' + error.message);
    }
  };

  useEffect(() => {
    calculateTotal();
  }, [selectedItems]);

  const fetchCustomers = async () => {
    try {
      const response = await CustomerService.getAllCustomers();
      setCustomers(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách khách hàng: ' + error.message);
    }
  };

  // Hiển thị modal thêm khách hàng mới
  const showAddCustomerModal = () => {
    setCustomerModalVisible(true);
    setNewCustomerCode('');
    setNewCustomerName('');
    setNewCustomerPhone('');
    setNewCustomerAddress('');
  };

  // Hàm xử lý thêm khách hàng mới
  const handleAddCustomer = async () => {
    setAddingCustomer(true);
    
    // Kiểm tra dữ liệu đầu vào
    if (!newCustomerCode.trim()) {
      toast.error('Vui lòng nhập mã khách hàng');
      setAddingCustomer(false);
      return;
    }
    
    if (!newCustomerName.trim()) {
      toast.error('Vui lòng nhập tên khách hàng');
      setAddingCustomer(false);
      return;
    }
    
    try {
      // Tạo object dữ liệu khách hàng mới
      const customerData = {
        customerCode: newCustomerCode,
        name: newCustomerName,
        phone: newCustomerPhone,
        address: newCustomerAddress
      };
      
      // Gọi API tạo khách hàng mới
      const response = await CustomerService.createCustomer(customerData);
      toast.success('Thêm khách hàng thành công');
      
      // Cập nhật danh sách khách hàng
      await fetchCustomers();
      
      // Chọn khách hàng vừa tạo trong form
      form.setFieldsValue({
        customerId: response.data.id
      });
      
      // Reset các trường dữ liệu
      setNewCustomerCode('');
      setNewCustomerName('');
      setNewCustomerPhone('');
      setNewCustomerAddress('');
      setCustomerModalVisible(false);
    } catch (error) {
      toast.error('Không thể thêm khách hàng: ' + error.message);
    } finally {
      setAddingCustomer(false);
    }
  };

  const fetchProducts = async () => {
    try {
      const response = await ProductService.getAllProducts();
      setProducts(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách sản phẩm: ' + error.message);
    }
  };

  const fetchBranches = async () => {
    try {
      const response = await BranchService.getAllBranches();
      setBranches(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách chi nhánh: ' + error.message);
    }
  };

  const fetchUsers = async () => {
    try {
      const response = await UserService.getAllUsers();
      setUsers(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách người dùng: ' + error.message);
    }
  };

  const fetchInvoiceData = async () => {
    setLoading(true);
    try {
      const response = await InvoiceService.getInvoiceById(id);
      const invoiceData = response.data;

      form.setFieldsValue({
        invoiceCode: invoiceData.invoiceCode || invoiceData.code,
        customerId: invoiceData.customerId,
        status: invoiceData.status,
        paidAmount: invoiceData.paidAmount || 0,
        branchId: invoiceData.branchId,
        userId: invoiceData.userId,
        note: invoiceData.note || invoiceData.notes,
      });

      // Chuyển đổi dữ liệu từ InvoiceItems sang định dạng phù hợp với selectedItems
      if (invoiceData.InvoiceItems && invoiceData.InvoiceItems.length > 0) {
        const formattedItems = invoiceData.InvoiceItems.map(item => ({
          productId: item.productId,
          quantity: item.quantity,
          price: item.price,
          amount: item.amount,
          product: item.Product // Lưu thông tin sản phẩm đầy đủ
        }));
        setSelectedItems(formattedItems);
      } else {
        setSelectedItems(invoiceData.items || []);
      }
    } catch (error) {
      toast.error('Không thể tải thông tin hoá đơn: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleAddItem = () => {
    // Khởi tạo item mới với các giá trị mặc định
    const newItem = {
      productId: null,
      quantity: 1,
      price: 0,
      amount: 0,
      product: null // Thêm tham chiếu đến sản phẩm để dễ truy cập thông tin
    };

    setSelectedItems([...selectedItems, newItem]);
  };

  const showAddProductModal = () => {
    productForm.resetFields();
    setProductModalVisible(true);
  };

  const handleAddProductModalCancel = () => {
    setProductModalVisible(false);
  };

  const handleAddProductModalOk = async () => {
    try {
      const values = await productForm.validateFields();

      // Chuyển đổi tên trường để phù hợp với backend
      const productData = {
        ...values,
        productCode: values.code,
        sellingPrice: values.price,
      };
      delete productData.code;
      delete productData.price;

      setLoading(true);

      // Gọi API để tạo sản phẩm mới
      const response = await ProductService.createProduct(productData);
      const newProduct = response.data;

      toast.success('Thêm sản phẩm mới thành công');

      // Cập nhật danh sách sản phẩm
      setProducts([...products, newProduct]);

      // Thêm sản phẩm mới vào danh sách sản phẩm đã chọn
      const newItem = {
        productId: newProduct.id,
        quantity: 1,
        price: newProduct.sellingPrice || newProduct.price || 0,
        amount: newProduct.sellingPrice || newProduct.price || 0,
        product: newProduct
      };

      setSelectedItems([...selectedItems, newItem]);

      setProductModalVisible(false);
    } catch (error) {
      if (error.errorFields) {
        toast.error('Vui lòng điền đầy đủ thông tin sản phẩm');
      } else {
        toast.error('Lỗi khi thêm sản phẩm: ' + error.message);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveItem = (index) => {
    const newItems = [...selectedItems];
    newItems.splice(index, 1);
    setSelectedItems(newItems);
  };

  const handleProductChange = (value, index) => {
    const product = products.find(p => p.id === value);
    if (!product) return;

    // Lấy giá từ sản phẩm, kiểm tra tất cả các trường có thể chứa giá
    let productPrice = 0;

    // Kiểm tra tất cả các trường có thể chứa giá
    if (product.price !== undefined && product.price !== null) {
      productPrice = product.price;
    } else if (product.sellingPrice !== undefined && product.sellingPrice !== null) {
      productPrice = product.sellingPrice;
    } else if (product.retailPrice !== undefined && product.retailPrice !== null) {
      productPrice = product.retailPrice;
    } else if (product.salePrice !== undefined && product.salePrice !== null) {
      productPrice = product.salePrice;
    } else if (product.costPrice !== undefined && product.costPrice !== null) {
      productPrice = product.costPrice;
    }

    const newItems = [...selectedItems];
    newItems[index] = {
      ...newItems[index],
      productId: value,
      price: productPrice,
      amount: productPrice * (newItems[index].quantity || 1),
      product: product // Lưu thông tin sản phẩm đầy đủ để dễ truy cập
    };
    setSelectedItems(newItems);
  };

  const handleQuantityChange = (value, index) => {
    const newItems = [...selectedItems];

    // Đảm bảo giá không bị undefined
    const price = newItems[index].price || 0;

    newItems[index] = {
      ...newItems[index],
      quantity: value,
      amount: price * value
    };

    setSelectedItems(newItems);
  };

  const calculateTotal = () => {
    // Tính tổng tiền từ các sản phẩm đã chọn
    const total = selectedItems.reduce((sum, item) => sum + (item.amount || 0), 0);

    // Đảm bảo total là một số hợp lệ
    const validTotal = parseFloat(total) || 0;

    // Cập nhật state và form
    setTotalAmount(validTotal);
    form.setFieldsValue({ totalAmount: validTotal });

    // Log để debug
    console.log('Tổng tiền đã tính:', validTotal);
  };

  // Hàm cập nhật tồn kho sản phẩm
  const updateProductStock = async (productId, quantity, isCompleted, isRemoved = false) => {
    try {
      // Lấy thông tin sản phẩm hiện tại từ backend để đảm bảo dữ liệu mới nhất
      const response = await ProductService.getProductById(productId);
      const product = response.data;

      // Lấy số lượng tồn kho hiện tại từ backend
      const currentStock = product.stock || 0;

      // Tính toán số lượng tồn kho mới
      let newStock;

      if (isRemoved && isCompleted) {
        // Nếu xóa sản phẩm khỏi hóa đơn đã hoàn thành, cộng lại số lượng vào kho
        // Lưu ý quantity đã là số âm từ hàm compareItems
        newStock = currentStock - quantity; // Trừ đi số âm = cộng vào
      } else if (isCompleted) {
        // Kiểm tra nếu tồn kho không đủ
        if (currentStock < quantity) {
          toast.error(`Sản phẩm ${product.name} chỉ còn ${currentStock} trong kho, không đủ số lượng ${quantity} để bán`);
          newStock = 0; // Đặt tồn kho về 0 nếu không đủ
        } else {
          newStock = currentStock - quantity;
        }
      } else {
        newStock = currentStock + quantity;
      }

      // Cập nhật tồn kho sản phẩm
      await ProductService.updateProduct(productId, {
        ...product,
        stock: newStock
      });

    } catch (error) {
      console.error(`Lỗi cập nhật tồn kho sản phẩm ${productId}:`, error);
      // Không hiển thị lỗi cho người dùng để tránh gián đoạn quy trình chính
    }
  };

  // Hàm cập nhật tồn kho cho tất cả sản phẩm trong hóa đơn
  const updateAllProductsStock = async (items, oldStatus, newStatus) => {
    // Chỉ cập nhật khi trạng thái thay đổi từ pending sang completed hoặc ngược lại
    const isCompletedNow = newStatus === 'completed';
    const wasCompletedBefore = oldStatus === 'completed';

    // Nếu trạng thái không thay đổi hoặc không liên quan đến completed/pending, không cần cập nhật
    if ((isCompletedNow === wasCompletedBefore) ||
        (newStatus === 'cancelled' && oldStatus !== 'completed')) {
      return;
    }

    // Cập nhật tồn kho cho từng sản phẩm
    for (const item of items) {
      await updateProductStock(item.productId, item.quantity, isCompletedNow);
    }
  };

  // Hàm so sánh danh sách sản phẩm cũ và mới để xác định sự thay đổi
  const compareItems = (oldItems, newItems, oldStatus, newStatus) => {
    // Đảm bảo oldItems và newItems là mảng
    oldItems = Array.isArray(oldItems) ? oldItems : [];
    newItems = Array.isArray(newItems) ? newItems : [];

    // Trường hợp đặc biệt: khi chuyển từ pending sang completed
    if (oldStatus === 'pending' && newStatus === 'completed') {

      // Khi chuyển từ pending sang completed, thêm tất cả sản phẩm vào danh sách cập nhật
      const allItems = newItems.map(item => ({
        productId: item.productId,
        quantity: item.quantity,
        isNew: false,
        isRemoved: false
      }));

      return allItems;
    }

    // Tạo map để lưu thông tin sản phẩm cũ theo productId
    const oldItemsMap = {};
    oldItems.forEach(item => {
      if (item && item.productId) {
        oldItemsMap[item.productId] = item;
      }
    });

    // Tạo map để lưu thông tin sản phẩm mới theo productId
    const newItemsMap = {};
    newItems.forEach(item => {
      if (item && item.productId) {
        newItemsMap[item.productId] = item;
      }
    });

    // Mảng lưu các sản phẩm cần cập nhật tồn kho
    const itemsToUpdate = [];

    // Kiểm tra các sản phẩm mới hoặc số lượng thay đổi
    newItems.forEach(newItem => {
      // Chỉ xử lý các sản phẩm có productId
      if (!newItem || !newItem.productId) return;

      const oldItem = oldItemsMap[newItem.productId];

      // Nếu sản phẩm không tồn tại trong danh sách cũ hoặc số lượng thay đổi
      if (!oldItem || oldItem.quantity !== newItem.quantity) {
        itemsToUpdate.push({
          productId: newItem.productId,
          // Nếu sản phẩm đã tồn tại, chỉ cập nhật phần chênh lệch số lượng
          quantity: oldItem ? newItem.quantity - oldItem.quantity : newItem.quantity,
          isNew: !oldItem,
          isRemoved: false
        });
      }
    });

    // Kiểm tra các sản phẩm đã bị xóa khỏi hóa đơn
    oldItems.forEach(oldItem => {
      // Chỉ xử lý các sản phẩm có productId
      if (!oldItem || !oldItem.productId) return;

      // Nếu sản phẩm không còn trong danh sách mới
      if (!newItemsMap[oldItem.productId]) {
        itemsToUpdate.push({
          productId: oldItem.productId,
          // Khi xóa sản phẩm, cần trả lại số lượng vào kho (số lượng âm để cập nhật ngược lại)
          quantity: -oldItem.quantity,
          isNew: false,
          isRemoved: true
        });
      }
    });

    return itemsToUpdate;
  };

  const onFinish = async (values) => {
    if (selectedItems.length === 0) {
      toast.error('Vui lòng thêm ít nhất một sản phẩm vào hoá đơn');
      return;
    }

    // Kiểm tra xem các sản phẩm đã được chọn đầy đủ chưa
    const invalidItems = selectedItems.filter(item => !item.productId);
    if (invalidItems.length > 0) {
      toast.error('Vui lòng chọn sản phẩm cho tất cả các dòng');
      return;
    }

    // Log để debug
    console.log('Form values:', values);
    console.log('Selected items:', selectedItems);

    // Đảm bảo totalAmount là một số hợp lệ
    const calculatedTotalAmount = parseFloat(totalAmount) || 0;
    
    // Kiểm tra số tiền thanh toán không vượt quá tổng tiền hàng
    const paidAmount = parseFloat(values.paidAmount || 0);
    if (paidAmount > calculatedTotalAmount) {
      toast.error('Số tiền đã thanh toán không được lớn hơn tổng tiền hàng');
      return;
    }

    const invoiceData = {
      ...values,
      items: selectedItems.map(item => ({
        productId: item.productId,
        quantity: item.quantity || 0,
        price: item.price || 0,
        amount: item.amount || 0
      })),
      totalAmount: calculatedTotalAmount, // Sử dụng giá trị đã được xử lý
      // Đảm bảo các trường bắt buộc có giá trị
      paidAmount: parseFloat(values.paidAmount) || 0, // Chuyển đổi sang số
      status: values.status || 'pending'
    };

    // Khi tạo mới, không gửi invoiceCode để backend tự động tạo
    if (!isEdit) {
      delete invoiceData.invoiceCode;
    }

    // Log dữ liệu gửi đi để debug
    console.log('Invoice data to be sent:', invoiceData);

    setLoading(true);
    try {
      if (isEdit) {
        // Lấy thông tin cũ của hóa đơn để so sánh
        const oldInvoiceResponse = await InvoiceService.getInvoiceById(id);
        const oldInvoiceData = oldInvoiceResponse.data;
        const oldStatus = oldInvoiceData.status;
        const newStatus = values.status;


        // Lấy danh sách sản phẩm cũ
        let oldItems = [];
        if (oldInvoiceData.InvoiceItems && oldInvoiceData.InvoiceItems.length > 0) {
          oldItems = oldInvoiceData.InvoiceItems.map(item => ({
            productId: item.productId,
            quantity: item.quantity,
            price: item.price,
            amount: item.amount
          }));
        } else if (oldInvoiceData.items && oldInvoiceData.items.length > 0) {
          oldItems = oldInvoiceData.items;
        }

        // Trường hợp đặc biệt: Chuyển từ completed sang pending/cancelled
        if (oldStatus === 'completed' && newStatus !== 'completed') {

          // Cập nhật hóa đơn trước
          await InvoiceService.updateInvoice(id, invoiceData);

          // Sau khi cập nhật, xử lý tồn kho cho từng sản phẩm
          for (const item of oldItems) {
            try {
              // Lấy thông tin sản phẩm
              const productId = item.productId;
              const quantity = item.quantity;

              // Lấy thông tin sản phẩm hiện tại
              const productResponse = await ProductService.getProductById(productId);
              const product = productResponse.data;

              // Tính toán tồn kho mới - khi hủy hóa đơn đã hoàn thành, cộng lại số lượng vào tồn kho
              const currentStock = product.stock || 0;
              const newStock = currentStock + quantity;

              // Cập nhật tồn kho
              await ProductService.updateProduct(productId, {
                ...product,
                stock: newStock
              });

            } catch (error) {
              console.error(`Lỗi cập nhật tồn kho sản phẩm:`, error);
            }
          }

          toast.success('Cập nhật hoá đơn và tồn kho thành công');
        }
        // Trường hợp đặc biệt: Chuyển từ pending/cancelled sang completed
        else if (oldStatus !== 'completed' && newStatus === 'completed') {

          // Kiểm tra tồn kho trước khi cập nhật
          let stockEnough = true;

          // Sau khi cập nhật, xử lý tồn kho cho từng sản phẩm
          for (const item of invoiceData.items) {
            try {
              // Lấy thông tin sản phẩm
              const productId = item.productId;
              const quantity = item.quantity;

              // Lấy thông tin sản phẩm hiện tại
              const productResponse = await ProductService.getProductById(productId);
              const product = productResponse.data;

              // Kiểm tra nếu tồn kho không đủ
              const currentStock = product.stock || 0;
              if (currentStock < quantity) {
                toast.error(`Sản phẩm ${product.name} chỉ còn ${currentStock} trong kho, không đủ số lượng ${quantity} để bán`);
                stockEnough = false;
                continue;
              }

              // Tính toán tồn kho mới
              const newStock = Math.max(0, currentStock - quantity);

              // Cập nhật tồn kho
              await ProductService.updateProduct(productId, {
                ...product,
                stock: newStock
              });

            } catch (error) {
              console.error(`Lỗi cập nhật tồn kho sản phẩm:`, error);
            }
          }

          if (stockEnough) {
            // Cập nhật hóa đơn
            await InvoiceService.updateInvoice(id, invoiceData);
            toast.success('Cập nhật hoá đơn và tồn kho thành công');
          } else {
            toast.error('Một số sản phẩm không đủ tồn kho, không thể cập nhật hoá đơn');
          }
        }
        // Trường hợp thông thường
        else {
          // Chỉ cập nhật hóa đơn, không làm gì với tồn kho
          try {
            console.log('Cập nhật hóa đơn với dữ liệu:', invoiceData);

            // Nếu chỉ cập nhật trạng thái, sử dụng API riêng
            if (oldStatus !== newStatus) {
              console.log('Cập nhật trạng thái hóa đơn từ', oldStatus, 'sang', newStatus);
              const statusResponse = await InvoiceService.updateInvoiceStatus(id, newStatus);
              console.log('Phản hồi từ API cập nhật trạng thái:', statusResponse);
              toast.success('Cập nhật trạng thái hoá đơn thành công');
            } else {
              // Cập nhật toàn bộ hóa đơn
              const response = await InvoiceService.updateInvoice(id, invoiceData);
              console.log('Phản hồi từ API cập nhật hóa đơn:', response);
              toast.success('Cập nhật hoá đơn thành công');
            }
          } catch (updateError) {
            console.error('Lỗi chi tiết khi cập nhật hóa đơn:', updateError);
            toast.error('Không thể cập nhật hóa đơn: ' + (updateError.response?.data?.message || updateError.message));
          }
        }
      } else {
        // Tạo hóa đơn mới
        // Nếu hóa đơn mới có trạng thái là completed, kiểm tra và cập nhật tồn kho
        if (values.status === 'completed') {

          // Kiểm tra tồn kho trước khi cập nhật
          let stockEnough = true;

          for (const item of invoiceData.items) {
            try {
              // Lấy thông tin sản phẩm
              const productId = item.productId;
              const quantity = item.quantity;

              // Lấy thông tin sản phẩm hiện tại
              const productResponse = await ProductService.getProductById(productId);
              const product = productResponse.data;

              // Kiểm tra nếu tồn kho không đủ
              const currentStock = product.stock || 0;
              if (currentStock < quantity) {
                toast.error(`Sản phẩm ${product.name} chỉ còn ${currentStock} trong kho, không đủ số lượng ${quantity} để bán`);
                stockEnough = false;
                continue;
              }

              // Tính toán tồn kho mới
              const newStock = Math.max(0, currentStock - quantity);

              // Cập nhật tồn kho
              await ProductService.updateProduct(productId, {
                ...product,
                stock: newStock
              });
            } catch (error) {
              console.error(`Lỗi cập nhật tồn kho sản phẩm:`, error);
            }
          }

          if (stockEnough) {
            // Tạo hóa đơn mới
            const response = await InvoiceService.createInvoice(invoiceData);
            toast.success('Tạo hoá đơn thành công');
          } else {
            toast.error('Một số sản phẩm không đủ tồn kho, không thể tạo hoá đơn');
            setLoading(false);
            return; // Dừng quá trình nếu không đủ tồn kho
          }
        } else {
          // Nếu không phải trạng thái completed, chỉ tạo hóa đơn
          const response = await InvoiceService.createInvoice(invoiceData);
          toast.success('Tạo hoá đơn thành công');
        }
      }
      navigate('/invoices');
    } catch (error) {
      console.error('Lỗi chi tiết:', error);
      toast.error('Lỗi: ' + (error.response?.data?.message || error.message));
    } finally {
      setLoading(false);
    }
  };

  const columns = [
    {
      title: 'Sản phẩm',
      dataIndex: 'productId',
      key: 'productId',
      render: (value, _, index) => (
        <Select
          style={{ width: '100%' }}
          placeholder="Chọn sản phẩm"
          value={value}
          onChange={(val) => handleProductChange(val, index)}
        >
          {products.map(product => (
            <Option key={product.id} value={product.id}>{product.name}</Option>
          ))}
        </Select>
      ),
    },
    {
      title: 'Số lượng',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 120,
      render: (value, _, index) => (
        <InputNumber
          min={1}
          value={value}
          onChange={(val) => handleQuantityChange(val, index)}
          style={{ width: '100%' }}
        />
      ),
    },
    {
      title: 'Đơn giá',
      dataIndex: 'price',
      key: 'price',
      width: 150,
      render: (price) => formatCurrencyWithSymbol(price),
    },
    {
      title: 'Thành tiền',
      dataIndex: 'amount',
      key: 'amount',
      width: 150,
      render: (amount) => formatCurrencyWithSymbol(amount),
    },
    {
      title: '',
      key: 'action',
      width: 80,
      render: (_, __, index) => (
        <Button
          type="text"
          danger
          icon={<MinusCircleOutlined />}
          onClick={() => handleRemoveItem(index)}
        />
      ),
    },
  ];

  return (
    <div>
      <Toaster position="top-right" />
      <Title level={2}>{isEdit ? 'Cập nhật hoá đơn' : 'Tạo hoá đơn mới'}</Title>
      <Divider />

      <Card>
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          initialValues={{ status: 'pending' }}
        >
          <div style={{ display: 'flex', gap: '16px' }}>
            <Form.Item
              name="invoiceCode"
              label="Mã hoá đơn"
              tooltip={isEdit ? "" : "Mã hoá đơn sẽ được tạo tự động (HD...)"}
              style={{ flex: 1 }}
            >
              <Input disabled={!isEdit} placeholder="Tự động tạo (HD...)" />
            </Form.Item>

            <Form.Item
              name="customerId"
              label="Khách hàng"
              rules={[{ required: true, message: 'Vui lòng chọn khách hàng' }]}
              style={{ flex: 1 }}
            >
              <Select 
                placeholder="Chọn khách hàng"
                dropdownRender={menu => (
                  <div>
                    {menu}
                    <Divider style={{ margin: '8px 0' }} />
                    <Space style={{ padding: '0 8px 4px' }}>
                      <Button 
                        type="text" 
                        icon={<PlusOutlined />} 
                        onClick={showAddCustomerModal}
                      >
                        Thêm khách hàng mới
                      </Button>
                    </Space>
                  </div>
                )}
              >
                {customers.map(customer => (
                  <Option key={customer.id} value={customer.id}>
                    {customer.name || customer.fullName || `Khách hàng #${customer.id}`}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="status"
              label="Trạng thái"
              rules={[{ required: true, message: 'Vui lòng chọn trạng thái' }]}
              style={{ flex: 1 }}
            >
              <Select>
                <Option value="pending">Chờ thanh toán</Option>
                <Option value="completed">Đã thanh toán</Option>
                <Option value="cancelled">Đã hủy</Option>
              </Select>
            </Form.Item>
          </div>

          <Divider orientation="left">Chi tiết hoá đơn</Divider>

          <div style={{ display: 'flex', gap: '8px', marginBottom: 16 }}>
            <Button
              type="dashed"
              onClick={handleAddItem}
              icon={<PlusOutlined />}
            >
              Chọn sản phẩm
            </Button>
            <Button
              type="primary"
              onClick={showAddProductModal}
              icon={<PlusCircleOutlined />}
            >
              Thêm sản phẩm mới
            </Button>
          </div>

          <Table
            columns={columns}
            dataSource={selectedItems}
            rowKey={(_, index) => index}
            pagination={false}
            bordered
          />

          <div style={{ textAlign: 'right', marginTop: 16 }}>
            <Title level={4}>Tổng tiền: {formatCurrencyWithSymbol(totalAmount)}</Title>
          </div>

          <Form.Item name="totalAmount" hidden>
            <InputNumber />
          </Form.Item>

          <Form.Item
            name="paidAmount"
            label="Số tiền đã thanh toán"
            style={{ marginTop: 16 }}
            rules={[
              { required: false },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  // Đảm bảo chuyển đổi giá trị thành số trước khi so sánh
                  const paidAmount = parseFloat(value || 0);
                  const total = parseFloat(totalAmount || 0);
                  
                  if (paidAmount > total) {
                    return Promise.reject(new Error('Số tiền đã thanh toán không được lớn hơn tổng tiền hàng'));
                  }
                  return Promise.resolve();
                },
              }),
            ]}
          >
            <InputNumber
              style={{ width: '100%' }}
              formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, '.')}
              parser={value => value.replace(/\$\s?|(\.+)/g, '')}
              min={0}
              max={totalAmount}
            />
          </Form.Item>

          <Form.Item
            name="branchId"
            label="Chi nhánh"
          >
            <Select placeholder="Chọn chi nhánh" allowClear>
              {branches.map(branch => (
                <Option key={branch.id} value={branch.id}>{branch.name}</Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="userId"
            label="Người tạo"
          >
            <Select placeholder="Chọn người tạo" allowClear>
              {users.map(user => (
                <Option key={user.id} value={user.id}>{user.fullName}</Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="note"
            label="Ghi chú"
          >
            <TextArea rows={4} />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={loading}>
                {isEdit ? 'Cập nhật' : 'Tạo hoá đơn'}
              </Button>
              <Button onClick={() => navigate('/invoices')}>
                Hủy
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      {/* Modal thêm khách hàng mới */}
      <Modal
        title="Thêm khách hàng mới"
        visible={customerModalVisible}
        onOk={handleAddCustomer}
        onCancel={() => setCustomerModalVisible(false)}
        confirmLoading={addingCustomer}
      >
        <div style={{ marginBottom: 16 }}>
          <div style={{ marginBottom: 16 }}>
            <label>Mã khách hàng <span style={{ color: 'red' }}>*</span></label>
            <Input 
              value={newCustomerCode} 
              onChange={(e) => setNewCustomerCode(e.target.value)} 
              placeholder="Nhập mã khách hàng" 
            />
          </div>
          
          <div style={{ marginBottom: 16 }}>
            <label>Tên khách hàng <span style={{ color: 'red' }}>*</span></label>
            <Input 
              value={newCustomerName} 
              onChange={(e) => setNewCustomerName(e.target.value)} 
              placeholder="Nhập tên khách hàng" 
            />
          </div>
          
          <div style={{ marginBottom: 16 }}>
            <label>Số điện thoại</label>
            <Input 
              value={newCustomerPhone} 
              onChange={(e) => setNewCustomerPhone(e.target.value)} 
              placeholder="Nhập số điện thoại" 
            />
          </div>
          
          <div style={{ marginBottom: 16 }}>
            <label>Địa chỉ</label>
            <Input 
              value={newCustomerAddress} 
              onChange={(e) => setNewCustomerAddress(e.target.value)} 
              placeholder="Nhập địa chỉ" 
            />
          </div>
        </div>
      </Modal>

      {/* Modal thêm sản phẩm mới */}
      <Modal
        title="Thêm sản phẩm mới"
        visible={productModalVisible}
        onOk={handleAddProductModalOk}
        onCancel={handleAddProductModalCancel}
        confirmLoading={loading}
      >
        <Form
          form={productForm}
          layout="vertical"
          initialValues={{ stock: 0, price: 0, isActive: true }}
        >
          <div style={{ display: 'flex', gap: '16px' }}>
            <Form.Item
              name="code"
              label="Mã sản phẩm"
              rules={[{ required: true, message: 'Vui lòng nhập mã sản phẩm' }]}
              style={{ flex: 1 }}
            >
              <Input />
            </Form.Item>

            <Form.Item
              name="barcode"
              label="Mã vạch"
              style={{ flex: 1 }}
            >
              <Input />
            </Form.Item>
          </div>

          <Form.Item
            name="name"
            label="Tên sản phẩm"
            rules={[{ required: true, message: 'Vui lòng nhập tên sản phẩm' }]}
          >
            <Input />
          </Form.Item>

          <div style={{ display: 'flex', gap: '16px' }}>
            <Form.Item
              name="categoryId"
              label="Nhóm hàng"
              rules={[{ required: true, message: 'Vui lòng chọn nhóm hàng' }]}
              style={{ flex: 1 }}
            >
              <Select
                placeholder="Chọn nhóm hàng"
                dropdownRender={menu => {
                  return categories.roots ? menu : <div style={{ padding: 8 }}>Đang tải...</div>;
                }}
              >
                {categories.roots && categories.roots.map(category => (
                  <Select.OptGroup key={category.id} label={<strong>{category.name}</strong>}>
                    {/* Thêm nhóm cha như một option */}
                    <Option key={category.id} value={category.id}>
                      <strong>{category.name}</strong>
                    </Option>

                    {/* Thêm các nhóm con */}
                    {categories.childrenMap[category.id] && categories.childrenMap[category.id].map(child => (
                      <Option key={child.id} value={child.id}>
                        <span style={{ paddingLeft: '20px' }}>↳ {child.name}</span>
                      </Option>
                    ))}
                  </Select.OptGroup>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="brandId"
              label="Thương hiệu"
              rules={[{ required: true, message: 'Vui lòng chọn thương hiệu' }]}
              style={{ flex: 1 }}
            >
              <Select placeholder="Chọn thương hiệu">
                {brands.map(brand => (
                  <Option key={brand.id} value={brand.id}>{brand.name}</Option>
                ))}
              </Select>
            </Form.Item>
          </div>

          <div style={{ display: 'flex', gap: '16px' }}>
            <Form.Item
              name="costPrice"
              label="Giá vốn"
              rules={[{ required: true, message: 'Vui lòng nhập giá vốn' }]}
              style={{ flex: 1 }}
            >
              <InputNumber
                style={{ width: '100%' }}
                formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, '.')}
                parser={value => value.replace(/\$\s?|(\.+)/g, '')}
                min={0}
              />
            </Form.Item>

            <Form.Item
              name="price"
              label="Giá bán"
              rules={[{ required: true, message: 'Vui lòng nhập giá bán' }]}
              style={{ flex: 1 }}
            >
              <InputNumber
                style={{ width: '100%' }}
                formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, '.')}
                parser={value => value.replace(/\$\s?|(\.+)/g, '')}
                min={0}
              />
            </Form.Item>

            <Form.Item
              name="stock"
              label="Tồn kho"
              rules={[{ required: true, message: 'Vui lòng nhập số lượng tồn kho' }]}
              style={{ flex: 1 }}
            >
              <InputNumber style={{ width: '100%' }} min={0} />
            </Form.Item>
          </div>

          <Form.Item
            name="description"
            label="Mô tả"
          >
            <TextArea rows={4} />
          </Form.Item>

          <Form.Item
            name="isActive"
            label="Trạng thái"
            valuePropName="checked"
          >
            <Switch checkedChildren="Hoạt động" unCheckedChildren="Không hoạt động" defaultChecked />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default InvoiceForm;
