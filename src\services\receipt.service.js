import api from './api';

const ReceiptService = {
  getAllReceipts: async () => {
    return api.get('/receipts?include=receiptType');
  },

  getReceiptById: async (id) => {
    return api.get(`/receipts/${id}?include=receiptType,user,branch,bankAccount,invoice`);
  },

  createReceipt: async (receiptData) => {
    return api.post('/receipts', receiptData);
  },

  updateReceipt: async (id, receiptData) => {
    return api.put(`/receipts/${id}`, receiptData);
  },

  deleteReceipt: async (id) => {
    return api.delete(`/receipts/${id}`);
  }
};

export default ReceiptService;
