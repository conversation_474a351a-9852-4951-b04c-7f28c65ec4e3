import React, { useState, useEffect } from 'react';
import { Table, Button, Space, Popconfirm, Typography, Input, Card, Tag } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, SearchOutlined } from '@ant-design/icons';
import { Link } from 'react-router-dom';
import { Toaster, toast } from 'react-hot-toast';
import SupplierService from '../../services/supplier.service';

const { Title } = Typography;

const SupplierList = () => {
  const [suppliers, setSuppliers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');

  useEffect(() => {
    fetchSuppliers();
  }, []);

  const fetchSuppliers = async () => {
    setLoading(true);
    try {
      const response = await SupplierService.getAllSuppliers();
      setSuppliers(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách nhà cung cấp: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    try {
      await SupplierService.deleteSupplier(id);
      toast.success('Xóa nhà cung cấp thành công');
      fetchSuppliers();
    } catch (error) {
      toast.error('Không thể xóa nhà cung cấp: ' + error.message);
    }
  };

  const filteredSuppliers = suppliers.filter(
    (supplier) =>
      supplier.supplierCode?.toLowerCase().includes(searchText.toLowerCase()) ||
      supplier.name?.toLowerCase().includes(searchText.toLowerCase()) ||
      supplier.phone?.includes(searchText) ||
      supplier.address?.toLowerCase().includes(searchText.toLowerCase()) ||
      supplier.taxCode?.includes(searchText) ||
      supplier.contactPerson?.toLowerCase().includes(searchText.toLowerCase())
  );

  const columns = [
    {
      title: <span style={{ fontSize: 16 }}>Mã NCC</span>,
      dataIndex: 'supplierCode',
      key: 'supplierCode',
      render: (text) => <span style={{ fontSize: '16px' }}>{text}</span>
    },
    {
      title: <span style={{ fontSize: 16 }}>Tên nhà cung cấp</span>,
      dataIndex: 'name',
      key: 'name',
      sorter: (a, b) => a.name.localeCompare(b.name),
      render: (text) => <span style={{ fontSize: '16px' }}>{text}</span>
    },
    {
      title: <span style={{ fontSize: 16 }}>Người liên hệ</span>,
      dataIndex: 'contactPerson',
      key: 'contactPerson',
      render: (text) => <span style={{ fontSize: '16px' }}>{text}</span>
    },
    {
      title: <span style={{ fontSize: 16 }}>Số điện thoại</span>,
      dataIndex: 'phone',
      key: 'phone',
      render: (text) => <span style={{ fontSize: '16px' }}>{text}</span>
    },
    {
      title: <span style={{ fontSize: 16 }}>Địa chỉ</span>,
      dataIndex: 'address',
      key: 'address',
      render: (text) => <span style={{ fontSize: '16px' }}>{text}</span>
    },
    {
      title: <span style={{ fontSize: 16 }}>Trạng thái</span>,
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'} style={{ fontSize: '14px', padding: '2px 10px' }}>
          {isActive ? 'Hoạt động' : 'Không hoạt động'}
        </Tag>
      ),
    },
    {
      title: <span style={{ fontSize: 16 }}>Thao tác</span>,
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Link to={`/suppliers/edit/${record.id}`}>
            <Button 
              type="primary" 
              icon={<EditOutlined />} 
              style={{ fontSize: 16, height: 40, padding: '0 15px', display: 'flex', alignItems: 'center' }}
            >
              Sửa
            </Button>
          </Link>
          <Popconfirm
            title="Bạn có chắc chắn muốn xóa nhà cung cấp này?"
            onConfirm={() => handleDelete(record.id)}
            okText="Có"
            cancelText="Không"
          >
            <Button 
              type="primary" 
              danger 
              icon={<DeleteOutlined />} 
              style={{ fontSize: 16, height: 40, padding: '0 15px', display: 'flex', alignItems: 'center' }}
            >
              Xóa
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ fontSize: '16px' }}> {/* Tăng kích thước phông chữ cơ bản thêm 2px */}
      <Toaster position="top-right" />
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 20 }}>
        <Title level={2} style={{ fontSize: 28, background: 'linear-gradient(90deg, #1890ff, #096dd9)', WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent' }}>Quản lý nhà cung cấp</Title>
        <Link to="/suppliers/add">
          <Button 
            type="primary" 
            icon={<PlusOutlined />} 
            size="large" 
            style={{ height: '40px', fontWeight: 'bold', fontSize: '16px', padding: '0 20px' }}
          >
            Thêm nhà cung cấp
          </Button>
        </Link>
      </div>

      <Card style={{ marginBottom: 20 }}>
        <Input
          placeholder="Tìm kiếm nhà cung cấp..."
          prefix={<SearchOutlined style={{ fontSize: '18px' }} />}
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          style={{ width: 350, height: '42px', fontSize: '16px' }}
        />
      </Card>

      <Table
        columns={columns}
        dataSource={filteredSuppliers}
        rowKey="id"
        loading={loading}
        pagination={{ pageSize: 10, style: { fontSize: 16 } }}
        style={{ fontSize: '16px' }}
      />
    </div>
  );
};

export default SupplierList;
