import api from './api';

const ProductCategoryService = {
  getAllCategories: async () => {
    return api.get('/product-categories');
  },

  getCategoryById: async (id) => {
    return api.get(`/product-categories/${id}`);
  },

  createCategory: async (categoryData) => {
    return api.post('/product-categories', categoryData);
  },

  updateCategory: async (id, categoryData) => {
    return api.put(`/product-categories/${id}`, categoryData);
  },

  deleteCategory: async (id) => {
    return api.delete(`/product-categories/${id}`);
  }
};

export default ProductCategoryService;
