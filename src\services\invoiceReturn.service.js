import api from './api';
import InvoiceService from './invoice.service';
import ReturnService from './return.service';

/**
 * Service để xử lý mối quan hệ giữa hóa đơn và phiếu trả hàng
 */
const InvoiceReturnService = {
  /**
   * Cập nhật trạng thái trả hàng của hóa đơn
   * @param {number} invoiceId - ID của hóa đơn
   * @returns {Promise} - Promise chứa kết quả cập nhật
   */
  updateInvoiceReturnStatus: async (invoiceId) => {
    try {
      // 1. L<PERSON>y thông tin hóa đơn
      const invoiceResponse = await InvoiceService.getInvoiceById(invoiceId);
      const invoice = invoiceResponse.data;
      
      if (!invoice) {
        throw new Error('Không tìm thấy hóa đơn');
      }
      
      // 2. <PERSON><PERSON><PERSON> tất cả các phiếu trả hàng liên quan đến hóa đơn này
      const returnsResponse = await api.get(`/returns?invoiceId=${invoiceId}`);
      const returns = returnsResponse.data;
      
      // 3. Lọc ra các phiếu trả hàng đã hoàn thành
      const completedReturns = returns.filter(ret => ret.status === 'completed');
      
      // 4. Nếu không có phiếu trả hàng nào đã hoàn thành, đặt returnStatus = 'none'
      if (completedReturns.length === 0) {
        return await InvoiceService.updateInvoice(invoiceId, {
          ...invoice,
          returnStatus: 'none'
        });
      }
      
      // 5. Tính tổng số lượng sản phẩm đã trả trong tất cả các phiếu trả hàng đã hoàn thành
      let returnedItems = {};
      let totalReturnAmount = 0;
      
      // Duyệt qua từng phiếu trả hàng đã hoàn thành
      for (const ret of completedReturns) {
        // Lấy chi tiết phiếu trả hàng để có thông tin về các sản phẩm
        const returnDetailResponse = await ReturnService.getReturnById(ret.id);
        const returnDetail = returnDetailResponse.data;
        
        // Lấy danh sách sản phẩm từ phiếu trả hàng
        const returnItems = returnDetail.ReturnItems || returnDetail.items || [];
        
        // Cộng dồn số lượng sản phẩm đã trả
        returnItems.forEach(item => {
          const productId = item.productId;
          if (!returnedItems[productId]) {
            returnedItems[productId] = 0;
          }
          returnedItems[productId] += item.quantity;
        });
        
        // Cộng dồn tổng tiền trả hàng
        totalReturnAmount += returnDetail.totalAmount || 0;
      }
      
      // 6. Lấy danh sách sản phẩm từ hóa đơn gốc
      const invoiceItems = invoice.InvoiceItems || [];
      
      // 7. Kiểm tra xem tất cả các sản phẩm trong hóa đơn có được trả hết không
      let allItemsReturned = true;
      let someItemsReturned = false;
      
      for (const invoiceItem of invoiceItems) {
        const productId = invoiceItem.productId;
        const invoiceQuantity = invoiceItem.quantity;
        const returnedQuantity = returnedItems[productId] || 0;
        
        if (returnedQuantity > 0) {
          someItemsReturned = true;
        }
        
        if (returnedQuantity < invoiceQuantity) {
          allItemsReturned = false;
        }
      }
      
      // 8. Xác định trạng thái trả hàng của hóa đơn
      let returnStatus = 'none';
      if (allItemsReturned) {
        returnStatus = 'full';
      } else if (someItemsReturned) {
        returnStatus = 'partial';
      }
      
      // 9. Tính lại tổng tiền thực tế sau khi trừ đi giá trị trả hàng
      const actualAmount = Math.max(0, invoice.totalAmount - totalReturnAmount);
      
      // 10. Cập nhật hóa đơn
      return await InvoiceService.updateInvoice(invoiceId, {
        ...invoice,
        returnStatus,
        actualAmount,
        returnAmount: totalReturnAmount
      });
    } catch (error) {
      console.error('Lỗi khi cập nhật trạng thái trả hàng của hóa đơn:', error);
      throw error;
    }
  }
};

export default InvoiceReturnService;
