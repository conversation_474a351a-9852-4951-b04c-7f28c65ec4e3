import api from './api';

const ProductService = {
  getAllProducts: async () => {
    return api.get('/products?include=category,brand');
  },

  getProductById: async (id) => {
    return api.get(`/products/${id}`);
  },

  createProduct: async (productData) => {
    return api.post('/products', productData);
  },

  updateProduct: async (id, productData) => {
    return api.put(`/products/${id}`, productData);
  },

  deleteProduct: async (id) => {
    return api.delete(`/products/${id}`);
  },

  /**
   * L<PERSON>y danh sách sản phẩm tồn kho lâu
   * @param {Number} months - S<PERSON> tháng để xác định hàng tồn kho lâu
   * @returns {Promise<Object>} Danh sách sản phẩm tồn kho lâu
   */
  getAgingInventory: async (months = null) => {
    try {
      const params = new URLSearchParams();
      if (months) params.append('months', months);

      return await api.get(`/products/aging-inventory?${params.toString()}`);
    } catch (error) {
      console.error('Error fetching aging inventory:', error);
      throw error;
    }
  },

  /**
   * Lấy danh sách sản phẩm sắp hết hàng
   * @param {Number} threshold - Ngưỡng số lượng tồn kho thấp
   * @returns {Promise<Object>} Danh sách sản phẩm sắp hết hàng
   */
  getLowStockProducts: async (threshold = null) => {
    try {
      const params = new URLSearchParams();
      if (threshold) params.append('threshold', threshold);

      return await api.get(`/products/low-stock?${params.toString()}`);
    } catch (error) {
      console.error('Error fetching low stock products:', error);
      throw error;
    }
  },

  /**
   * Lấy danh sách sản phẩm đã hết hàng (tồn kho = 0)
   * @returns {Promise<Object>} Danh sách sản phẩm đã hết hàng
   */
  getOutOfStockProducts: async () => {
    try {
      return await api.get('/products/out-of-stock');
    } catch (error) {
      console.error('Error fetching out of stock products:', error);
      throw error;
    }
  },

  /**
   * Gửi email thông báo tồn kho ngay lập tức
   * @returns {Promise<Object>} Kết quả gửi email
   */
  sendInventoryNotification: async () => {
    try {
      // Thêm timeout dài hơn cho request này vì gửi email có thể mất thời gian
      const response = await api.post('/products/send-inventory-notification', {}, {
        timeout: 30000 // 30 giây timeout
      });
      return response;
    } catch (error) {
      console.error('Error sending inventory notification:', error);
      // Ghi log chi tiết hơn về lỗi
      if (error.response) {
        console.error('Error response data:', error.response.data);
        console.error('Error response status:', error.response.status);
      }
      throw error;
    }
  }
};

export default ProductService;
