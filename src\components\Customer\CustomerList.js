import React, { useState, useEffect } from 'react';
import { Table, Button, Space, Popconfirm, Typography, Input, Card } from 'antd';
import { Toaster, toast } from 'react-hot-toast';
import { PlusOutlined, EditOutlined, DeleteOutlined, SearchOutlined } from '@ant-design/icons';
import { Link } from 'react-router-dom';
import CustomerService from '../../services/customer.service';

const { Title } = Typography;

const CustomerList = () => {
  const [customers, setCustomers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');

  useEffect(() => {
    fetchCustomers();
  }, []);

  const fetchCustomers = async () => {
    setLoading(true);
    try {
      const response = await CustomerService.getAllCustomers();
      setCustomers(response.data);
    } catch (error) {
      toast.error('Không thể tải danh sách khách hàng: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    try {
      await CustomerService.deleteCustomer(id);
      toast.success('Xóa khách hàng thành công');
      fetchCustomers();
    } catch (error) {
      toast.error('Không thể xóa khách hàng: ' + error.message);
    }
  };

  const filteredCustomers = customers.filter(
    (customer) =>
      customer.customerCode?.toLowerCase().includes(searchText.toLowerCase()) ||
      customer.name?.toLowerCase().includes(searchText.toLowerCase()) ||
      customer.email?.toLowerCase().includes(searchText.toLowerCase()) ||
      customer.phone?.includes(searchText) ||
      customer.address?.toLowerCase().includes(searchText.toLowerCase()) ||
      customer.taxCode?.includes(searchText)
  );

  const columns = [
    {
      title: <span style={{ fontSize: 16 }}>Mã khách hàng</span>,
      dataIndex: 'customerCode',
      key: 'customerCode',
      render: (text) => <span style={{ fontSize: '16px' }}>{text}</span>
    },
    {
      title: <span style={{ fontSize: 16 }}>Tên khách hàng</span>,
      dataIndex: 'name',
      key: 'name',
      sorter: (a, b) => a.name.localeCompare(b.name),
      render: (text) => <span style={{ fontSize: '16px' }}>{text}</span>
    },
    {
      title: <span style={{ fontSize: 16 }}>Email</span>,
      dataIndex: 'email',
      key: 'email',
      render: (text) => <span style={{ fontSize: '16px' }}>{text}</span>
    },
    {
      title: <span style={{ fontSize: 16 }}>Số điện thoại</span>,
      dataIndex: 'phone',
      key: 'phone',
      render: (text) => <span style={{ fontSize: '16px' }}>{text}</span>
    },
    {
      title: <span style={{ fontSize: 16 }}>Địa chỉ</span>,
      dataIndex: 'address',
      key: 'address',
      render: (text) => <span style={{ fontSize: '16px' }}>{text}</span>
    },
    {
      title: <span style={{ fontSize: 16 }}>Thao tác</span>,
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Link to={`/customers/edit/${record.id}`}>
            <Button 
              type="primary" 
              icon={<EditOutlined />} 
              style={{ fontSize: 16, height: 40, padding: '0 15px', display: 'flex', alignItems: 'center' }}
            >
              Sửa
            </Button>
          </Link>
          <Popconfirm
            title="Bạn có chắc chắn muốn xóa khách hàng này?"
            onConfirm={() => handleDelete(record.id)}
            okText="Có"
            cancelText="Không"
          >
            <Button 
              type="primary" 
              danger 
              icon={<DeleteOutlined />} 
              style={{ fontSize: 16, height: 40, padding: '0 15px', display: 'flex', alignItems: 'center' }}
            >
              Xóa
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ fontSize: '16px' }}> {/* Tăng kích thước phông chữ cơ bản thêm 2px */}
      <Toaster position="top-right" />
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 20 }}>
        <Title level={2} style={{ fontSize: 28, background: 'linear-gradient(90deg, #1890ff, #096dd9)', WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent' }}>Quản lý khách hàng</Title>
        <Link to="/customers/add">
          <Button 
            type="primary" 
            icon={<PlusOutlined />} 
            size="large" 
            style={{ height: '40px', fontWeight: 'bold', fontSize: '16px', padding: '0 20px' }}
          >
            Thêm khách hàng
          </Button>
        </Link>
      </div>

      <Card style={{ marginBottom: 20 }}>
        <Input
          placeholder="Tìm kiếm khách hàng..."
          prefix={<SearchOutlined style={{ fontSize: '18px' }} />}
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          style={{ width: 350, height: '42px', fontSize: '16px' }}
        />
      </Card>

      <Table
        columns={columns}
        dataSource={filteredCustomers}
        rowKey="id"
        loading={loading}
        pagination={{ pageSize: 10, style: { fontSize: 16 } }}
        style={{ fontSize: '16px' }}
      />
    </div>
  );
};

export default CustomerList;
