import api from './api';

const ReturnService = {
  getAllReturns: async () => {
    return api.get('/returns?include=customer,invoice');
  },

  getReturnById: async (id) => {
    // Thêm tham số include để lấy thông tin đầy đủ
    return api.get(`/returns/${id}?include=customer,invoice,items,items.product,ReturnItems,ReturnItems.Product`);
  },

  createReturn: async (returnData) => {
    return api.post('/returns', returnData);
  },

  updateReturn: async (id, returnData) => {
    return api.put(`/returns/${id}`, returnData);
  },

  deleteReturn: async (id) => {
    return api.delete(`/returns/${id}`);
  }
};

export default ReturnService;
